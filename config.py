# 每批次最多的三级模块数量
BATCH_COUNT = 30

# 嵌入模型配置
EMBEDDING_MODEL="embed"  # 嵌入模型
EMBEDDING_API_BASE="http://10.10.43.64:38080"
#EMBEDDING_API_BASE = "https://ai.secsign.online:38080"
EMBEDDING_API_KEY = "sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB"

# LEVEL2_NAME = "密码资产数据管理"
TEST_LEVEL2_NAME = ""
TEST_LEVEL3_NAME = "" #"密码服务数据库管理"
ENDPOINT_URL="http://10.20.25.251:3000/v1/chat/completions"
#ENDPOINT_URL="https://ai.secsign.online:3003/v1/chat/completions"
MODEL_NAME="qwen3-32b-sft"
API_KEY="sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB"

# 阿里云
# ENDPOINT_URL="https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
# ## ENDPOINT_URL="https://10.10.43.64:3000/compatible-mode/v1/chat/completions"
# MODEL_NAME="qwen3-coder-plus-2025-07-22"
# API_KEY="sk-1d06e75d7fd94338b5b32cf8f9099651"
# 每分钟的调用次数限制
API_QPM=60
# 每分钟处理的文本token数量
API_TPM=100000

# openrouter
# ENDPOINT_URL="https://openrouter.ai/api/v1/chat/completions"
# MODEL_NAME="moonshotai/kimi-k2:free"
# API_KEY="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"
# # 每分钟的调用次数限制
# API_QPM=1
# # 每分钟处理的文本token数量
# API_TPM=1000000


# 验证CFP拆分的4个检查点
## 1.完整性检查
# 每个功能过程是否包含 至少1个E和1个X？
# *例：密码方案收集（1E+1R+2W+1X）→ 通过*
## 2.数据组聚合检查
# 是否将关联数据属性 合并为最小单元？
# *例：用户姓名+电话 → 1个数据组，非2个*
## 3.存储边界检查
# R/W是否仅针对 边界内持久存储？
# 例：读取外部医保API → 不计R，应计E（输入）
## 4.无重复计数
# 同一数据组在同一功能过程中 是否被重复计数？
# 例：读取用户信息后再次读取 → 计2次R

# 新增知识库配置 - 支持多个文件，逗号分隔
# MARKDOWN_MANUAL_PATH = "debug/test_manual.md"  # 临时使用测试文件
MARKDOWN_MANUAL_PATH = "docs/集省一体平台及服务操作手册.md,docs/三未信安密码服务平台管理端用户手册v3.4.0(无区域多租户模式).md"
SQL_FILE_PATH = "docs/ccsp_data.sql,docs/V3.4.pdma.json"  # 支持SQL和JSON格式

# 知识库相关配置
KNOWLEDGE_BASE_ENABLED = True  # 是否启用知识库功能
KNOWLEDGE_BASE_TOP_K = 5  # 检索返回的最相关结果数量
KNOWLEDGE_BASE_SIMILARITY_THRESHOLD = 0.1  # 相似度阈值，低于此值的结果将被过滤
KNOWLEDGE_BASE_CACHE_DIR = "debug/knowledge_cache"  # 知识库缓存目录

# 分块处理配置
CHUNK_PROCESSING_ENABLED = True  # 是否启用分块处理
MARKDOWN_CHILD_PATTERN = '####'  # 子级分段正则表达式模式（四级标题）
SQL_CHUNK_BY_TABLE = True  # SQL文件是否按数据表分块
MAX_CHUNK_SIZE = 20480  # 单个分块的最大字符数（20KB），超过此大小会进一步分割
MIN_CHUNK_SIZE = 100  # 单个分块的最小字符数，小于此大小会合并到相邻分块
EMBEDDING_BATCH_SIZE = 3  # 向量化处理的批次大小，避免超过模型限制

# COSMIC校验功能配置
# 校验专用的大模型配置（如果不设置，则使用默认配置）
CHECK_ENDPOINT_URL = "https://ai.secsign.online:3003/v1/chat/completions"  # 校验专用API端点，None表示使用默认ENDPOINT_URL
CHECK_MODEL_NAME = "qwen3-32b"    # 校验专用模型名称，None表示使用默认MODEL_NAME
CHECK_API_KEY = "sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB"       # 校验专用API密钥，None表示使用默认API_KEY
CHECK_API_QPM = None       # 校验专用QPM限制，None表示使用默认API_QPM
CHECK_API_TPM = None       # 校验专用TPM限制，None表示使用默认API_TPM

# 校验功能特定配置
CHECK_EXCLUDED_FIELDS = [   # 在校验时需要去除的字段列表
    '预估工作量（人天）',      # 去除预估工作量字段，减少提示词使用
    '功能描述',            # 可选：去除功能描述字段
]

# 校验结果输出配置
CHECK_OUTPUT_DIR = "debug"                                    # 校验结果输出目录
CHECK_INPUT_DEBUG_FILE = "cosmic_validation_input.json"      # 输入数据调试文件名
CHECK_RESULT_FILE = "cosmic_validation_result.json"          # 校验结果文件名
CHECK_PROMPT_FILE = "check_prompt.md"                        # 校验提示词文件名