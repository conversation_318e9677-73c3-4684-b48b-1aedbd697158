﻿一级功能模块,二级功能模块,三级功能模块,功能描述,预估工作量（人天）,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP
系统管理,用户认证管理,用户列表查询,列表展示内容：序号、账号名、姓名、角色、所属租户、账号有效期、完整性、备注、创建时间、状态,2.0,发起者：管理员，接收者：用户管理系统,管理员进入用户管理页面,查询用户列表,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取用户信息,R,用户信息,账号名、姓名、角色、所属租户、账号有效期、完整性、备注、创建时间、状态,1
,,用户注册,注册用户信息，录入内容：账户名、姓名、用户类型、备注,3.0,发起者：管理员，接收者：用户管理系统,管理员点击用户注册按钮,注册新用户,输入用户注册信息,E,用户信息,账户名、姓名、用户类型、备注,1
,,,,,,,,保存用户信息,W,用户信息,账户名、姓名、用户类型、备注,1
,,,,,,,,返回注册结果,X,用户信息,注册状态、错误信息,1
,,用户编辑,编辑用户信息，编辑用户名称,4.0,发起者：管理员，接收者：用户管理系统,管理员点击用户编辑按钮,编辑用户信息,选择用户,E,用户信息,用户ID,1
,,,,,,,,输入用户编辑信息,E,用户信息,姓名、用户类型、备注,1
,,,,,,,,保存用户信息,W,用户信息,用户ID、姓名、用户类型、备注,1
,,,,,,,,返回编辑结果,X,用户信息,编辑状态、错误信息,1
,,启用,启用禁用的用户,2.0,发起者：管理员，接收者：用户管理系统,管理员点击启用按钮,启用用户,选择用户,E,用户信息,用户ID,1
,,,,,,,,更新用户状态,W,用户信息,用户ID、状态,1
,,禁用,禁用用户，禁止用户登录,2.0,发起者：管理员，接收者：用户管理系统,管理员点击禁用按钮,禁用用户,选择用户,E,用户信息,用户ID,1
,,,,,,,,更新用户状态,W,用户信息,用户ID、状态,1
,,重置密码,重置用户登录口令为默认口令,2.0,发起者：管理员，接收者：用户管理系统,管理员点击重置密码按钮,重置用户密码,选择用户,E,用户信息,用户ID,1
,,,,,,,,重置密码,W,用户信息,用户ID、密码,1
,,解锁,解锁到期用户或多次录入错误口令的用户,2.0,发起者：管理员，接收者：用户管理系统,管理员点击解锁按钮,解锁用户,选择用户,E,用户信息,用户ID,1
,,,,,,,,更新用户锁定状态,W,用户信息,用户ID、锁定状态,1
,,设置有效期,设置用户的有效期,3.0,发起者：管理员，接收者：用户管理系统,管理员点击设置有效期按钮,设置用户有效期,选择用户,E,用户信息,用户ID,1
,,,,,,,,输入有效期信息,E,用户信息,有效期开始时间、有效期结束时间,1
,,,,,,,,保存有效期设置,W,用户信息,用户ID、有效期开始时间、有效期结束时间,1
,,删除,删除用户,2.0,发起者：管理员，接收者：用户管理系统,管理员点击删除按钮,删除用户,选择用户,E,用户信息,用户ID,1
,,,,,,,,删除用户,W,用户信息,用户ID,1
,,列表查询,列表展示内容：序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,3.0,发起者：管理员，接收者：用户管理系统,管理员进入用户注册审核页面,查询注册审核列表,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取审核信息,R,审核信息,账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,1
,,,,,,,,返回审核列表,X,审核信息,账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,1
,,删除注册记录,删除注册用户的记录信息,2.0,发起者：管理员，接收者：用户管理系统,管理员点击删除注册记录按钮,删除注册记录,选择注册记录,E,审核信息,注册记录ID,1
,,,,,,,,删除注册记录,W,审核信息,注册记录ID,1
,,用户注册审核,审核通过/拒绝、所属角色、审批意见,3.0,发起者：管理员，接收者：用户管理系统,管理员点击审核按钮,审核用户注册,选择用户,E,审核信息,注册记录ID,1
,,,,,,,,输入审核信息,E,审核信息,审核状态、所属角色、审核意见,1
,,,,,,,,保存审核结果,W,审核信息,注册记录ID、审核状态、所属角色、审核意见,1
