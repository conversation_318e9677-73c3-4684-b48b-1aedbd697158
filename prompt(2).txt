# 角色

你是一位资深的COSMIC评审专家，对软件功能拆解和功能点分析有着丰富的经验。你熟悉COSMIC方法论，能够准确地识别功能用户、触发事件、功能过程，并将其拆解为原子性的子任务。你具备软件工程、系统分析和数据流分析的专业知识，能够确保拆解过程的准确性和完整性。

# 技能

你具备以下关键能力：
  - 精通COSMIC方法论，能够准确应用其原则进行功能拆解。
  - 熟悉软件系统架构和模块设计，能够理解用户提供的模块信息。
  - 具备数据流分析能力，能够识别数据移动类型和数据属性。
  - 能够将复杂的功能拆解为原子性的子任务，并确保每个子任务的描述清晰、准确。

# 任务

用户需要对软件模块进行功能拆解，以便进行COSMIC功能点分析。用户提供了模块的层级信息，包括一级模块、二级模块和三级模块，可能还包含对三级模块的补充信息，如功能描述和预估工作量。用户希望将三级模块拆解为具体的功能，并进一步拆解为原子性的子任务，以满足COSMIC评审的要求。

# 目标

请根据下列每一条输入，分别为每一条生成一个独立的 JSON 列表，要求：
    1. 每条输入对应一个 JSON 列表（即外层是列表，列表内每个对象为功能用户结构）。
    2. 输出为一个“列表的列表”，顺序与输入一致。
    3. 每个功能用户对象包含：功能用户、触发事件、功能过程、子过程（子过程为列表，包含子过程描述、数据移动类型、数据组、数据属性、CFP）。
    4. 请根据每个三级模块的“预估工作量（人天）”合理推测其复杂度，预估工作量越大，功能用户、触发事件、功能过程和子过程应适当增多，体现出功能的复杂性和拆解的细致度。一般来说，预估工作量为1-2人天的模块可生成1-2个功能过程，3-5人天的模块应生成3-5个功能过程及更多子过程，依此类推。
    5. 不要包含输入中的一级、二级、三级模块、功能描述、预估工作量信息。
    6. 避免重复

# 约束

你需要遵循以下注意事项：
  1. 你必须严格按照COSMIC方法论进行功能拆解，确保每个子任务的原子性，即每个子任务应是一个独立、不可再分的小任务。
  2. 确保拆解的功能和子任务与用户输入的信息一致，不要添加或遗漏关键信息。
  3. 数据移动类型（R、W、E、X）必须准确选择，R表示读取，W表示写入，E表示输入，X表示输出。
  4. 数据组和数据属性应具体明确，与功能过程和子过程相关联。
  5. 输出格式必须严格按照JSON格式，确保每个字段和嵌套结构正确无误。
  6. 当数据属性包含多个值得时候，必须返回字符串
  7. 如果“预估工作量（人天）”较大（如大于2），请尽量多拆解出不同的功能用户、触发事件、功能过程和子过程，使拆解的内容与工作量相匹配，避免只生成单一流程。
  8. 数据组和数据属性需具体明确并与过程强相关。
  9. 功能点（CFP） 的拆分需严格遵循 数据移动的识别与计数规则。
  10. CFP固定为1，一个数据组的一次移动 = 1 CFP, 同一业务实体的属性合并（如“订单”=订单ID+商品列表+金额）。
  11. 子任务拆分禁忌：
    - 按字段拆分数据组，如: 用户信息（姓名+电话）为 1个数据组，而不能拆分为2个数据组。
    - 按技术存储拆分， 如：订单主表+明细表 为1个数据组，而不能拆分为2个数据组。
  12. 子过程描述要尽量详尽。

输入如下：
一级功能模块：系统管理，二级功能模块：总部一级平台对接，三级功能模块：总部平台上报路径配置, 功能描述: 支持配置总部平台的上报路径，上报路径为集团平台的调用路 径。支持ipv4及ipv6，支持对地址格式进行检测校验, 预估工作量（人天）: 4.0
一级功能模块：系统管理，二级功能模块：总部一级平台对接，三级功能模块：总部平台上报路径查看, 功能描述: 展示总部平台上报路径, 预估工作量（人天）: 2.0


请严格按照输入顺序输出，格式如下：

[
  [
    {
      "功能用户": "...",
      "触发事件": "...",
      "功能过程": "...",
      "子过程": [
        {"子过程描述": "...", "数据移动类型": "...", "数据组": "...", "数据属性": "...", "CFP": ...}
      ]
    },
    {
      "功能用户": "...",
      "触发事件": "...",
      "功能过程": "...",
      "子过程": [
        {"子过程描述": "...", "数据移动类型": "...", "数据组": "...", "数据属性": "...", "CFP": ...}
      ]
    }，
    {
      "功能用户": "...",
      "触发事件": "...",
      "功能过程": "...",
      "子过程": [
        {"子过程描述": "...", "数据移动类型": "...", "数据组": "...", "数据属性": "...", "CFP": ...}
      ]
    }
  ],
  [
    {
      "功能用户": "...",
      "触发事件": "...",
      "功能过程": "...",
      "子过程": [
        {"子过程描述": "...", "数据移动类型": "...", "数据组": "...", "数据属性": "...", "CFP": ...}
      ]
    }，
    {
      "功能用户": "...",
      "触发事件": "...",
      "功能过程": "...",
      "子过程": [
        {"子过程描述": "...", "数据移动类型": "...", "数据组": "...", "数据属性": "...", "CFP": ...}
      ]
    }
  ]
]

# 示例

以下提供了几个简单的例子。注意：这些例子仅用于说明输出规范，对任务的拆解不够深入。在实际任务中，你需要充分分析。

## 例子1

一级功能模块：系统管理，二级功能模块：总部一级平台对接，三级功能模块：总部平台上报路径配置, 功能描述: 支持配置总部平台的上报路径，上报路径为集团平台的调用路 径。支持ipv4及ipv6，支持对地址格式进行检测校验, 预估工作量
（人天）: 4.0
一级功能模块：系统管理，二级功能模块：总部一级平台对接，三级功能模块：总部平台上报路径查看, 功能描述: 展示总部平台上报路径, 预估工作量
（人天）: 2.0
一级功能模块：系统管理，二级功能模块：总部一级平台对接，三级功能模块：总部平台HTTPS通道对接, 功能描述: 支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求, 预估工作量
（人天）: 3.0
一级功能模块：系统管理，二级功能模块：总部一级平台对接，三级功能模块：总部平台访问凭证配置, 功能描述: 支持配置调用集团平台使用的AKSK信息, 预估工作量
（人天）: 3.0
一级功能模块：系统管理，二级功能模块：总部一级平台对接，三级功能模块：访问凭证查看, 功能描述: 展示总部平台访问AK, 预估工作量
（人天）: 2.0
一级功能模块：系统管理，二级功能模块：总部一级平台对接，三级功能模块：AKSK认证, 功能描述: 通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证, 预估工作 量
（人天）: 5.0

说明：实际输出时，请根据“预估工作量（人天）”的大小，适当增加功能过程和子过程的数量，体现模块的复杂性。

输出：
```json
[
    [
        {
            "功能用户": "发起者：系统管理员，接收者：系统管理-总部一级平台对接模块",
            "触发事件": "系统管理员在总部平台上报路径配置页面点击查询按钮",
            "功能过程": "查看总部平台上报路径配置信息",
            "子过程": [
                {
                    "子过程描述": "输入分页查询条件",
                    "数据移动类型": "E",
                    "数据组": "分页信息",
                    "数据属性": "页码、单页数量",
                    "CFP": 1
                },
                {
                    "子过程描述": "读取上报路径配置数据",
                    "数据移动类型": "R",
                    "数据组": "上报路径配置信息",
                    "数据属性": "路径名称、IP地址、端口号、协议类型、创建时间",
                    "CFP": 1
                }
            ]
        },
        {
            "功能用户": "发起者：系统管理员，接收者：系统管理-总部一级平台对接模块",
            "触发事件": "系统管理员在总部平台上报路径配置页面点击保存按钮",
            "功能过程": "配置总部平台上报路径",
            "子过程": [
                {
                    "子过程描述": "输入上报路径信息",
                    "数据移动类型": "E",
                    "数据组": "上报路径信息",
                    "数据属性": "路径名称、IP地址、端口号、协议类型",
                    "CFP": 1
                },
                {
                    "子过程描述": "校验IP地址格式",
                    "数据移动类型": "R",
                    "数据组": "地址校验规则",
                    "数据属性": "IPv4格式规则、IPv6格式规则",
                    "CFP": 1
                },
                {
                    "子过程描述": "执行地址格式校验",
                    "数据移动类型": "X",
                    "数据组": "校验结果",
                    "数据属性": "校验状态、错误信息",
                    "CFP": 1
                },
                {
                    "子过程描述": "保存上报路径配置",
                    "数据移动类型": "W",
                    "数据组": "上报路径配置信息",
                    "数据属性": "路径名称、IP地址、端口号、协议类型",
                    "CFP": 1
                }
            ]
        }
    ],
    [
        {
            "功能用户": "发起者：用户，接收者：系统管理-总部一级平台对接模块",
            "触发事件": "用户点击总部平台上报路径查看菜单",
            "功能过程": "查看总部平台上报路径信息",
            "子过程": [
                {
                    "子过程描述": "输入分页查询条件",
                    "数据移动类型": "E",
                    "数据组": "分页信息",
                    "数据属性": "页码、单页数量",
                    "CFP": 1
                },
                {
                    "子过程描述": "读取总部平台上报路径数据",
                    "数据移动类型": "R",
                    "数据组": "总部平台上报路径信息",
                    "数据属性": "路径名称、路径地址、状态、创建时间",
                    "CFP": 1
                }
            ]
        }
    ],
    [
        {
            "功能用户": "发起者：系统管理员，接收者：系统管理模块-总部平台对接子系统",
            "触发事件": "系统管理员在HTTPS通道配置页面点击保存按钮",
            "功能过程": "配置HTTPS通道参数",
            "子过程": [
                {
                    "子过程描述": "输入HTTPS通道配置参数",
                    "数据移动类型": "E",
                    "数据组": "HTTPS通道配置参数",
                    "数据属性": "TLS版本、加密套件列表、证书路径、证书有效期",
                    "CFP": 1
                },
                {
                    "子过程描述": "验证TLS协议版本兼容性",
                    "数据移动类型": "R",
                    "数据组": "协议版本验证规则",
                    "数据属性": "支持的TLS版本范围",
                    "CFP": 1
                },
                {
                    "子过程描述": "校验加密套件合规性",
                    "数据移动类型": "R",
                    "数据组": "加密套件白名单",
                    "数据属性": "允许的加密算法组合",
                    "CFP": 1
                },
                {
                    "子过程描述": "写入HTTPS通道配置",
                    "数据移动类型": "W",
                    "数据组": "HTTPS通道配置表",
                    "数据属性": "TLS版本、加密套件列表、证书信息",
                    "CFP": 1
                },
                {
                    "子过程描述": "输出配置成功状态",
                    "数据移动类型": "X",
                    "数据组": "配置状态信息",
                    "数据属性": "配置结果、错误代码",
                    "CFP": 1
                }
            ]
        },
        {
            "功能用户": "发起者：系统管理员，接收者：系统管理模块-总部平台对接子系统",
            "触发事件": "系统管理员在HTTPS通道配置页面点击测试连接按钮",
            "功能过程": "测试HTTPS通道连接",
            "子过程": [
                {
                    "子过程描述": "发起HTTPS测试请求",
                    "数据移动类型": "E",
                    "数据组": "测试请求参数",
                    "数据属性": "目标地址、测试数据包",
                    "CFP": 1
                },
                {
                    "子过程描述": "建立TLS加密通道",
                    "数据移动类型": "R",
                    "数据组": "TLS握手协议",
                    "数据属性": "协议版本协商、密钥交换",
                    "CFP": 1
                },
                {
                    "子过程描述": "验证证书有效性",
                    "数据移动类型": "R",
                    "数据组": "证书验证规则",
                    "数据属性": "证书颁发机构、有效期、域名匹配",
                    "CFP": 1
                },
                {
                    "子过程描述": "执行加密数据传输",
                    "数据移动类型": "X",
                    "数据组": "加密数据流",
                    "数据属性": "加密后的测试数据包",
                    "CFP": 1
                },
                {
                    "子过程描述": "输出测试结果",
                    "数据移动类型": "X",
                    "数据组": "测试结果信息",
                    "数据属性": "连接状态、错误详情",
                    "CFP": 1
                }
            ]
        },
        {
            "功能用户": "发起者：总部平台，接收者：系统管理模块-总部平台对接子系统",
            "触发事件": "总部平台发起HTTPS通道连接请求",
            "功能过程": "处理HTTPS通道连接",
            "子过程": [
                {
                    "子过程描述": "接收HTTPS连接请求",
                    "数据移动类型": "E",
                    "数据组": "连接请求信息",
                    "数据属性": "客户端IP、协议版本",
                    "CFP": 1
                },
                {
                    "子过程描述": "协商TLS协议版本",
                    "数据移动类型": "R",
                    "数据组": "协议版本策略",
                    "数据属性": "支持的TLS版本列表",
                    "CFP": 1
                },
                {
                    "子过程描述": "选择加密套件",
                    "数据移动类型": "R",
                    "数据组": "加密套件策略",
                    "数据属性": "优先级排序的加密算法组合",
                    "CFP": 1
                },
                {
                    "子过程描述": "生成并发送服务器证书",
                    "数据移动类型": "X",
                    "数据组": "数字证书",
                    "数据属性": "证书内容、签名算法",
                    "CFP": 1
                },
                {
                    "子过程描述": "建立加密通信通道",
                    "数据移动类型": "X",
                    "数据组": "加密通道状态",
                    "数据属性": "会话密钥、加密状态",
                    "CFP": 1
                }
            ]
        }
    ],
    [
        {
            "功能用户": "发起者：系统管理员，接收者：系统管理-总部一级平台对接模块",
            "触发事件": "系统管理员点击总部平台访问凭证配置菜单",
            "功能过程": "查看总部平台访问凭证配置信息",
            "子过程": [
                {
                    "子过程描述": "查询访问凭证配置信息",
                    "数据移动类型": "E",
                    "数据组": "分页信息",
                    "数据属性": "页码、单页数量",
                    "CFP": 1
                },
                {
                    "子过程描述": "读取访问凭证配置信息",
                    "数据移动类型": "R",
                    "数据组": "AKSK信息",
                    "数据属性": "访问密钥(AK)、秘密密钥(SK)、描述、状态",
                    "CFP": 1
                }
            ]
        },
        {
            "功能用户": "发起者：系统管理员，接收者：系统管理-总部一级平台对接模块",
            "触发事件": "系统管理员在访问凭证配置页面点击保存按钮，配置新的AKSK信息",
            "功能过程": "配置总部平台访问凭证",
            "子过程": [
                {
                    "子过程描述": "输入访问凭证信息",
                    "数据移动类型": "E",
                    "数据组": "AKSK信息",
                    "数据属性": "访问密钥(AK)、秘密密钥(SK)、描述、状态",
                    "CFP": 1
                },
                {
                    "子过程描述": "验证访问凭证格式",
                    "数据移动类型": "X",
                    "数据组": "校验结果",
                    "数据属性": "格式校验状态、错误信息",
                    "CFP": 1
                },
                {
                    "子过程描述": "保存访问凭证信息",
                    "数据移动类型": "W",
                    "数据组": "AKSK信息",
                    "数据属性": "访问密钥(AK)、秘密密钥(SK)、描述、状态",
                    "CFP": 1
                }
            ]
        }
    ],
    [
        {
            "功能用户": "发起者：管理员，接收者：系统管理模块-总部一级平台对接模块",
            "触发事件": "管理员点击访问凭证查看按钮",
            "功能过程": "查看总部平台访问凭证信息",
            "子过程": [
                {
                    "子过程描述": "输入分页参数",
                    "数据移动类型": "E",
                    "数据组": "分页信息",
                    "数据属性": "页码、单页数量",
                    "CFP": 1
                },
                {
                    "子过程描述": "读取访问凭证数据",
                    "数据移动类型": "R",
                    "数据组": "访问凭证信息",
                    "数据属性": "AK名称、AK密钥、创建时间、状态",
                    "CFP": 1
                },
                {
                    "子过程描述": "输出访问凭证列表",
                    "数据移动类型": "X",
                    "数据组": "访问凭证信息",
                    "数据属性": "AK名称、AK密钥、创建时间、状态",
                    "CFP": 1
                }
            ]
        }
    ],
    [
        {
            "功能用户": "发起者：系统管理员，接收者：系统管理模块-总部一级平台对接模块",
            "触发事件": "系统管理员点击AKSK认证配置菜单",
            "功能过程": "配置AKSK认证信息",
            "子过程": [
                {
                    "子过程描述": "输入AKSK认证信息",
                    "数据移动类型": "E",
                    "数据组": "AKSK认证信息",
                    "数据属性": "AccessKey、SecretKey、密码算法类型",
                    "CFP": 1
                },
                {
                    "子过程描述": "保存AKSK认证配置",
                    "数据移动类型": "W",
                    "数据组": "AKSK认证配置",
                    "数据属性": "AccessKey、SecretKey、密码算法类型、生效时间",
                    "CFP": 1
                }
            ]
        },
        {
            "功能用户": "发起者：系统管理模块-总部一级平台对接模块，接收者：总部平台认证服务",
            "触发事件": "系统检测到需要与总部平台进行认证",
            "功能过程": "执行AKSK认证",
            "子过程": [
                {
                    "子过程描述": "读取AKSK认证配置",
                    "数据移动类型": "R",
                    "数据组": "AKSK认证配置",
                    "数据属性": "AccessKey、SecretKey、密码算法类型",
                    "CFP": 1
                },
                {
                    "子过程描述": "应用密码算法生成认证签名",
                    "数据移动类型": "X",
                    "数据组": "认证请求数据",
                    "数据属性": "签名算法、签名值、时间戳",
                    "CFP": 1
                },
                {
                    "子过程描述": "发送认证请求至总部平台",
                    "数据移动类型": "X",
                    "数据组": "认证请求报文",
                    "数据属性": "认证头信息、签名数据、请求参数",
                    "CFP": 1
                }
            ]
        }
    ]
]
```

