/*
     cn db to mysql
     Source Server Type    : opengauss
     Source Schema         : ccsp_data

     Target Server Type    : opengauss
     Target Schema         : ccsp_data

     Date: 2024-11-27T15:51:10.850672300
*/
-- ----------------------------
-- Table structure for config
-- ----------------------------
DROP TABLE IF EXISTS "config";
CREATE TABLE "config" (
                          "config_id" int8 NOT NULL  ,
                          "config_code" varchar(100) NOT NULL  ,
                          "config_name" varchar(100) NOT NULL  ,
                          "config_value" varchar(1000) NULL  ,
                          "config_type" int4 NOT NULL DEFAULT 0 ,
                          "maintain_flag" int4 NOT NULL DEFAULT 1 ,
                          "sord_num" int4 NOT NULL  ,
                          "remark" varchar(1000) NULL  ,
                          "create_by" int8 NULL  ,
                          "create_time" varchar(30) NULL  ,
                          "update_by" int8 NULL  ,
                          "update_time" varchar(30) NULL
);
ALTER TABLE "config" ADD CONSTRAINT "config_pkey" PRIMARY KEY ("config_id");
COMMENT ON TABLE "config" IS '配置表';
COMMENT ON COLUMN "config"."config_id" IS '主键';
COMMENT ON COLUMN "config"."config_code" IS '配置编码';
COMMENT ON COLUMN "config"."config_name" IS '配置名';
COMMENT ON COLUMN "config"."config_value" IS '配置值';
COMMENT ON COLUMN "config"."config_type" IS '配置类型;0明文1密文';
COMMENT ON COLUMN "config"."maintain_flag" IS '可维护标志;0：不可维护1：可维护';
COMMENT ON COLUMN "config"."sord_num" IS '排序';
COMMENT ON COLUMN "config"."remark" IS '备注';
COMMENT ON COLUMN "config"."create_by" IS '创建人';
COMMENT ON COLUMN "config"."create_time" IS '创建时间';
COMMENT ON COLUMN "config"."update_by" IS '更新人';
COMMENT ON COLUMN "config"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for dic_frequency
-- ----------------------------
DROP TABLE IF EXISTS "dic_frequency";
CREATE TABLE "dic_frequency" (
                                 "frequency_id" int8 NOT NULL  ,
                                 "frequency_name" varchar(500) NOT NULL,
                                 "cron_expression" varchar(500) NOT NULL
);
ALTER TABLE "dic_frequency" ADD CONSTRAINT "dic_frequency_pkey" PRIMARY KEY ("frequency_id");
COMMENT ON TABLE "dic_frequency" IS '上报频率字典';
COMMENT ON COLUMN "dic_frequency"."frequency_id" IS '频率ID';
COMMENT ON COLUMN "dic_frequency"."frequency_name" IS '频率名称';
COMMENT ON COLUMN "dic_frequency"."cron_expression" IS 'cron表达式';
-- ----------------------------
-- Records of dic_frequency
-- ----------------------------
-- INSERT INTO "dic_frequency" (frequency_id, frequency_name, cron_expression) VALUES(1, '每小时上报一次', '0 0 0/1 * * ?');
INSERT INTO "dic_frequency" (frequency_id, frequency_name, cron_expression) VALUES(2, '每天上报一次', '0 0 0 * * ?');
INSERT INTO "dic_frequency" (frequency_id, frequency_name, cron_expression) VALUES(3, '每周上报一次', '0 0 0 ? * 1');
INSERT INTO "dic_frequency" (frequency_id, frequency_name, cron_expression) VALUES(4, '每月上报一次', '0 0 0 1 * ?');



-- ----------------------------
-- Table structure for dict_item
-- ----------------------------
DROP TABLE IF EXISTS "dict_item";
CREATE TABLE "dict_item" (
                             "id" int8 NOT NULL  ,
                             "group_code" varchar(255) NULL  ,
                             "dict_code" varchar(1000) NULL  ,
                             "dict_value" varchar(255) NULL  ,
                             "default_flag" int4 NULL  ,
                             "remark" varchar(1000) NULL  ,
                             "dict_sort" int4 NULL  ,
                             "parent_code" varchar(1000) NULL  ,
                             "parent_id" int8 NULL  ,
                             "css_class" varchar(255) NULL  ,
                             "in_use" int4 NULL  ,
                             "create_by" int8 NULL  ,
                             "create_time" varchar(30) NULL  ,
                             "update_by" int8 NULL  ,
                             "update_time" varchar(30) NULL
);
ALTER TABLE "dict_item" ADD CONSTRAINT "dict_item_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "dict_item" IS '枚举项表';
COMMENT ON COLUMN "dict_item"."id" IS 'ID';
COMMENT ON COLUMN "dict_item"."group_code" IS '枚举分组标识';
COMMENT ON COLUMN "dict_item"."dict_code" IS '枚举项标识，可以为空，主要用于多层枚举使用';
COMMENT ON COLUMN "dict_item"."dict_value" IS '枚举值';
COMMENT ON COLUMN "dict_item"."default_flag" IS '是否默认(1默认 0非默认)';
COMMENT ON COLUMN "dict_item"."remark" IS '备注';
COMMENT ON COLUMN "dict_item"."dict_sort" IS '排序';
COMMENT ON COLUMN "dict_item"."parent_code" IS '上级枚举标识，可以为空，为空表示为最上级枚举';
COMMENT ON COLUMN "dict_item"."parent_id" IS '上级枚举ID';
COMMENT ON COLUMN "dict_item"."css_class" IS '前端样式';
COMMENT ON COLUMN "dict_item"."in_use" IS '是否启用(0启用，1禁用)';
COMMENT ON COLUMN "dict_item"."create_by" IS '创建人';
COMMENT ON COLUMN "dict_item"."create_time" IS '创建时间';
COMMENT ON COLUMN "dict_item"."update_by" IS '更新人';
COMMENT ON COLUMN "dict_item"."update_time" IS '更新时间';
-- ----------------------------
-- Records of dict_item
-- ----------------------------
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (1,'districtCode','001','华北大区',1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (2,'districtCode','002','东北大区',1,NULL,2,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (3,'districtCode','003','华东（北）大区',1,NULL,3,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (4,'districtCode','004','华东（南）大区',1,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (5,'districtCode','005','华南大区',1,NULL,5,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (6,'districtCode','006','华中大区',1,NULL,6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (7,'districtCode','007','西南大区',1,NULL,7,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (8,'districtCode','008','西北大区',1,NULL,8,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (9,'districtCode','009','北京大区',1,NULL,9,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (10,'districtCode','010','新疆大区',1,NULL,10,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (11,'appTypeCode','001','5GOMC',1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (12,'appTypeCode','002','承载网IP运维工作台',1,NULL,2,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (13,'appTypeCode','003','同步网OMC',1,NULL,3,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (14,'appTypeCode','004','传送网传输运维工作台',1,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (15,'appTypeCode','005','传送网OMC',1,NULL,5,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (16,'appTypeCode','006','4A安全管控平台',1,NULL,6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (17,'provinceCode','100','北京移动',1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (18,'provinceCode','200','广东移动',1,NULL,2,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (19,'provinceCode','210','上海移动',1,NULL,3,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (20,'provinceCode','220','天津移动',1,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (21,'provinceCode','230','重庆移动',1,NULL,5,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (22,'provinceCode','240','辽宁移动',1,NULL,6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (23,'provinceCode','250','江苏移动',1,NULL,7,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (24,'provinceCode','270','湖北移动',1,NULL,8,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (25,'provinceCode','280','四川移动',1,NULL,9,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (26,'provinceCode','290','陕西移动',1,NULL,10,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (27,'provinceCode','991','新疆移动',1,NULL,31,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (28,'provinceCode','311','河北移动',1,NULL,11,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (29,'provinceCode','351','山西移动',1,NULL,12,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (30,'provinceCode','371','河南移动',1,NULL,13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (31,'provinceCode','431','吉林移动',1,NULL,14,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (32,'provinceCode','451','黑龙江移动',1,NULL,15,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (33,'provinceCode','471','内蒙移动',1,NULL,16,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (34,'provinceCode','531','山东移动',1,NULL,17,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (35,'provinceCode','551','安徽移动',1,NULL,18,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (36,'provinceCode','571','浙江移动',1,NULL,19,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (37,'provinceCode','591','福建移动',1,NULL,20,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (38,'provinceCode','731','湖南移动',1,NULL,21,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (39,'provinceCode','771','广西移动',1,NULL,22,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (40,'provinceCode','791','江西移动',1,NULL,23,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (41,'provinceCode','851','贵州移动',1,NULL,24,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (42,'provinceCode','871','云南移动',1,NULL,25,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (43,'provinceCode','891','西藏移动',1,NULL,26,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (44,'provinceCode','898','海南移动',1,NULL,27,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (45,'provinceCode','931','甘肃移动',1,NULL,28,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (46,'provinceCode','951','宁夏移动',1,NULL,29,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (47,'provinceCode','971','青海移动',1,NULL,30,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (48,'cryptoBusinessType','B001','数据加解密',1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (49,'cryptoBusinessType','B002','签名验签',1,NULL,2,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (50,'cryptoBusinessType','B003','通道加密',1,NULL,3,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (51,'cryptoBusinessType','B004','密钥管理',1,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (52,'cryptoBusinessType','B005','时间戳',1,NULL,5,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (53,'cryptoBusinessType','B006','统一身份认证',1,NULL,6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (54,'cryptoBusinessType','B007','动态令牌',1,NULL,7,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (55,'cryptoBusinessType','B008','协同签名',1,NULL,8,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (56,'cryptoBusinessType','B009','文件加解密',1,NULL,9,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (57,'cryptoBusinessType','B010','数据库加解密',1,NULL,10,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (58,'cryptoBusinessType','B011','电子签章',1,NULL,11,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (59,'cryptoBusinessType','B012','数字认证证书',1,NULL,12,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (60,'cryptoProductForm','C001','服务器密码机',1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (61,'cryptoProductForm','C002','云服务器密码机',1,NULL,2,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (62,'cryptoProductForm','C003','虚拟密码机',1,NULL,3,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (63,'cryptoProductForm','C004','密码软件',1,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (64,'cryptoProductForm','C005','其他',1,NULL,5,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (65,'cryptoTransformPhase','G001','项目准备',1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (66,'cryptoTransformPhase','G002','方案评估',1,NULL,2,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (67,'cryptoTransformPhase','G003','方案编制',1,NULL,3,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (68,'cryptoTransformPhase','G004','改造实施',1,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (69,'cryptoTransformPhase','G005','实施完成',1,NULL,5,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (70,'cryptoTransformPhase','G006','现场测评',1,NULL,6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (71,'cryptoTransformPhase','G007','测评完成',1,NULL,7,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (72,'cryptoTransformPhase','G008','系统上线',1,NULL,8,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (73,'keyCertificateType','K001','对称密钥',1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (74,'keyCertificateType','K002','非对称密钥',1,NULL,2,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (75,'keyCertificateType','K003','密钥交换密钥',1,NULL,3,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (76,'keyCertificateType','K004','数字证书密钥',1,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (77,'keyCertificateType','K005','会话密钥',1,NULL,5,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (78,'keyCertificateType','K006','存储密钥',1,NULL,6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (79,'keyCertificateType','K007','信任域证书',1,NULL,7,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (80,'keyCertificateType','K008','身份证书',1,NULL,8,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (81,'keyCertificateType','K009','签名验签证书',1,NULL,9,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (82,'keyCertificateType','K010','服务器证书',1,NULL,10,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (83,'keyCertificateType','K011','客户端证书',1,NULL,11,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (84,'securityBreachEvent','E001','密钥安全',1,'密钥相关安全问题，如不安全算法、重复密钥等问题',1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (85,'securityBreachEvent','E002','身份安全',1,'系统用户安全问题，如身份鉴别、权限限制、安全登录等问题',2,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (86,'securityBreachEvent','E003','网络安全',1,'网络相关安全问题，如恶意网络攻击、勒索软件等问题',3,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (87,'securityBreachEvent','E004','传输安全',1,'数据通信交互问题，如通道安全、通信数据机密性、通信数据完整性等问题',4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (88,'securityBreachEvent','E005','存储安全',1,'数据存储安全问题，如关键数据未加密存储等问题',5,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (89,'securityBreachEvent','E006','其他',1,'其他安全问题',6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (90,'dataType','F001','密码资料',1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (91,'dataType','F002','密码标准',1,NULL,2,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (92,'dataType','F003','安全报告',1,NULL,3,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (93,'dataType','F004','经验分享',1,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO "dict_item" ("id","group_code","dict_code","dict_value","default_flag","remark","dict_sort","parent_code","parent_id","css_class","in_use","create_by","create_time","update_by","update_time")
VALUES (94,'provinceCode','000','集团移动',1,NULL,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);

-- ----------------------------
-- Table structure for file_info
-- ----------------------------
DROP TABLE IF EXISTS "file_info";
CREATE TABLE "file_info" (
                             "id" int8 NOT NULL  ,
                             "file_name" varchar(255) NULL  ,
                             "file_remote_path" varchar(255) NULL  ,
                             "file_digest" varchar(255) NULL  ,
                             "file_business" varchar(255) NULL  ,
                             "file_size" int8 NULL  ,
                             "status" int4 NULL  ,
                             "storage_type" int4 NULL  ,
                             "hmac" varchar(255) NULL  ,
                             "remark" varchar(1000) NULL  ,
                             "create_by" int8 NULL  ,
                             "create_time" varchar(30) NULL  ,
                             "update_by" int8 NULL  ,
                             "update_time" varchar(30) NULL
);
ALTER TABLE "file_info" ADD CONSTRAINT "file_info_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "file_info" IS '文件信息';
COMMENT ON COLUMN "file_info"."id" IS '主键';
COMMENT ON COLUMN "file_info"."file_name" IS '文件名称';
COMMENT ON COLUMN "file_info"."file_remote_path" IS '文件路径';
COMMENT ON COLUMN "file_info"."file_digest" IS '文件摘要';
COMMENT ON COLUMN "file_info"."file_business" IS '文件业务类型';
COMMENT ON COLUMN "file_info"."file_size" IS '文件大小/字节';
COMMENT ON COLUMN "file_info"."status" IS '文件状态';
COMMENT ON COLUMN "file_info"."storage_type" IS '存储方式-1本地 2hdfs';
COMMENT ON COLUMN "file_info"."hmac" IS '完整性校验字段';
COMMENT ON COLUMN "file_info"."remark" IS '备注';
COMMENT ON COLUMN "file_info"."create_by" IS '创建人';
COMMENT ON COLUMN "file_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "file_info"."update_by" IS '更新人';
COMMENT ON COLUMN "file_info"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for province_platform
-- ----------------------------
DROP TABLE IF EXISTS "province_platform";
CREATE TABLE "province_platform" (
                                     "id" int8 NOT NULL  ,
                                     "platform_name" varchar(500) NULL  ,
                                     "platform_code" varchar(500) NULL  ,
                                     "province_code" varchar(500) NULL  ,
                                     "remark" varchar(1000) NULL  ,
                                     "create_by" int8 NULL  ,
                                     "create_time" varchar(30) NULL  ,
                                     "update_by" int8 NULL  ,
                                     "update_time" varchar(30) NULL
);
ALTER TABLE "province_platform" ADD CONSTRAINT "province_platform_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "province_platform" IS '集省平台信息表';
COMMENT ON COLUMN "province_platform"."id" IS 'ID';
COMMENT ON COLUMN "province_platform"."platform_name" IS '平台名称';
COMMENT ON COLUMN "province_platform"."platform_code" IS '平台编码';
COMMENT ON COLUMN "province_platform"."province_code" IS '省份编码';
COMMENT ON COLUMN "province_platform"."remark" IS '备注';
COMMENT ON COLUMN "province_platform"."create_by" IS '创建人';
COMMENT ON COLUMN "province_platform"."create_time" IS '创建时间';
COMMENT ON COLUMN "province_platform"."update_by" IS '更新人';
COMMENT ON COLUMN "province_platform"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for province_platform_aksk
-- ----------------------------
DROP TABLE IF EXISTS "province_platform_aksk";
CREATE TABLE "province_platform_aksk" (
                                          "id" int8 NOT NULL  ,
                                          "platform_id" int8 NOT NULL  ,
                                          "accesskey_id" varchar(500) NOT NULL  ,
                                          "secret_key" varchar(500) NOT NULL  ,
                                          "status" int4 NULL  ,
                                          "remark" varchar(1000) NULL  ,
                                          "hmac" varchar(500) NULL  ,
                                          "create_by" int8 NULL  ,
                                          "create_time" varchar(30) NULL  ,
                                          "update_by" int8 NULL  ,
                                          "update_time" varchar(30) NULL
);
ALTER TABLE "province_platform_aksk" ADD CONSTRAINT "province_platform_aksk_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "province_platform_aksk" IS '集省平台AKSK';
COMMENT ON COLUMN "province_platform_aksk"."id" IS 'ID';
COMMENT ON COLUMN "province_platform_aksk"."platform_id" IS '平台ID';
COMMENT ON COLUMN "province_platform_aksk"."accesskey_id" IS 'AK';
COMMENT ON COLUMN "province_platform_aksk"."secret_key" IS 'SK';
COMMENT ON COLUMN "province_platform_aksk"."status" IS '状态;1启用 0停用';
COMMENT ON COLUMN "province_platform_aksk"."remark" IS '备注';
COMMENT ON COLUMN "province_platform_aksk"."hmac" IS 'HMAC';
COMMENT ON COLUMN "province_platform_aksk"."create_by" IS '创建人';
COMMENT ON COLUMN "province_platform_aksk"."create_time" IS '创建时间';
COMMENT ON COLUMN "province_platform_aksk"."update_by" IS '更新人';
COMMENT ON COLUMN "province_platform_aksk"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for report_config
-- ----------------------------
DROP TABLE IF EXISTS "report_config";
CREATE TABLE "report_config" (
                                 "id" int8 NOT NULL  ,
                                 "report_code" varchar(500) NOT NULL  ,
                                 "report_name" varchar(500) NOT NULL  ,
                                 "frequency_id" int8 NOT NULL  ,
                                 "enable" int4 NOT NULL  ,
                                 "last_report_time" varchar(30) NULL  ,
                                 "remark" varchar(1000) NULL  ,
                                 "create_by" varchar(255) NULL  ,
                                 "create_time" varchar(30) NULL  ,
                                 "update_by" varchar(255) NULL  ,
                                 "update_time" varchar(30) NULL
);
ALTER TABLE "report_config" ADD CONSTRAINT "report_config_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "report_config" IS '上报信息配置表';
COMMENT ON COLUMN "report_config"."id" IS '上报配置ID';
COMMENT ON COLUMN "report_config"."report_code" IS '上报内容标识';
COMMENT ON COLUMN "report_config"."report_name" IS '上报内容名称';
COMMENT ON COLUMN "report_config"."frequency_id" IS '上报频率ID';
COMMENT ON COLUMN "report_config"."enable" IS '是否开启; 1开启, 0关闭';
COMMENT ON COLUMN "report_config"."last_report_time" IS '最后上报时间';
COMMENT ON COLUMN "report_config"."remark" IS '备注';
COMMENT ON COLUMN "report_config"."create_by" IS '创建人';
COMMENT ON COLUMN "report_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "report_config"."update_by" IS '更新人';
COMMENT ON COLUMN "report_config"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS "sys_job";
CREATE TABLE "sys_job" (
                           "job_id" int8 NOT NULL  ,
                           "job_name" varchar(255) NOT NULL  ,
                           "job_group" varchar(255) NOT NULL  ,
                           "server_id" varchar(100) NOT NULL  ,
                           "method_url" varchar(1500) NULL  ,
                           "json_param" text NOT NULL  ,
                           "cron_expression" varchar(255) NULL  ,
                           "misfire_policy" varchar(20) NULL DEFAULT 3 ,
                           "concurrent" varchar(1) NULL DEFAULT 1 ,
                           "job_status" varchar(1) NULL DEFAULT 0 ,
                           "created_by" int8 NULL  ,
                           "create_time" varchar(30) NULL  ,
                           "updated_by" int8 NULL  ,
                           "update_time" varchar(30) NULL  ,
                           "remark" varchar(1500) NULL
);
ALTER TABLE "sys_job" ADD CONSTRAINT "sys_job_pkey" PRIMARY KEY ("job_id");
COMMENT ON TABLE "sys_job" IS '定时任务表';
COMMENT ON COLUMN "sys_job"."job_id" IS '任务号';
COMMENT ON COLUMN "sys_job"."job_name" IS '任务名称';
COMMENT ON COLUMN "sys_job"."job_group" IS '任务组名';
COMMENT ON COLUMN "sys_job"."server_id" IS '服务模块';
COMMENT ON COLUMN "sys_job"."method_url" IS '调用接口';
COMMENT ON COLUMN "sys_job"."json_param" IS 'json格式参数';
COMMENT ON COLUMN "sys_job"."cron_expression" IS 'CRON执行表达式';
COMMENT ON COLUMN "sys_job"."misfire_policy" IS '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）';
COMMENT ON COLUMN "sys_job"."concurrent" IS '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）';
COMMENT ON COLUMN "sys_job"."job_status" IS '状态（0正常 1暂停）';
COMMENT ON COLUMN "sys_job"."created_by" IS '创建人';
COMMENT ON COLUMN "sys_job"."create_time" IS '创建时间';
COMMENT ON COLUMN "sys_job"."updated_by" IS '更新人';
COMMENT ON COLUMN "sys_job"."update_time" IS '更新时间';
COMMENT ON COLUMN "sys_job"."remark" IS '备注';


-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS "sys_job_log";
CREATE TABLE "sys_job_log" (
                               "job_log_id" int8 NOT NULL  ,
                               "job_id" int8 NOT NULL  ,
                               "job_message" varchar(1500) NULL  ,
                               "status" varchar(1) NOT NULL  ,
                               "exception_info" varchar(6000) NULL  ,
                               "create_time" varchar(30) NOT NULL  ,
                               "trigger_time" int8 NULL
);
ALTER TABLE "sys_job_log" ADD CONSTRAINT "sys_job_log_pkey" PRIMARY KEY ("job_log_id");
COMMENT ON TABLE "sys_job_log" IS '定时任务执行日志表';
COMMENT ON COLUMN "sys_job_log"."job_log_id" IS '任务日志ID';
COMMENT ON COLUMN "sys_job_log"."job_id" IS '任务ID';
COMMENT ON COLUMN "sys_job_log"."job_message" IS '日志信息';
COMMENT ON COLUMN "sys_job_log"."status" IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN "sys_job_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "sys_job_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "sys_job_log"."trigger_time" IS '触发时间';

-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS "sys_task";
CREATE TABLE "sys_task" (
                            "task_id" int8 NOT NULL  ,
                            "task_name" varchar(255) NOT NULL  ,
                            "task_group" varchar(255) NOT NULL  ,
                            "server_id" varchar(255) NOT NULL  ,
                            "method_url" varchar(1000) NULL  ,
                            "json_param" text NOT NULL  ,
                            "task_status" int4 NOT NULL DEFAULT 0 ,
                            "create_time" varchar(30) NULL  ,
                            "update_time" varchar(30) NULL  ,
                            "remark" varchar(300) NULL  ,
                            "timeout" int4 NULL  ,
                            "start_time" varchar(255) NULL  ,
                            "end_time" varchar(255) NULL  ,
                            "policy" varchar(1) NOT NULL DEFAULT 0
);
ALTER TABLE "sys_task" ADD CONSTRAINT "sys_task_pkey" PRIMARY KEY ("task_id");
COMMENT ON TABLE "sys_task" IS '异步任务表';
COMMENT ON COLUMN "sys_task"."task_id" IS '任务号';
COMMENT ON COLUMN "sys_task"."task_name" IS '任务名称';
COMMENT ON COLUMN "sys_task"."task_group" IS '任务组名;执行任务串行';
COMMENT ON COLUMN "sys_task"."server_id" IS '服务模块';
COMMENT ON COLUMN "sys_task"."method_url" IS '调用接口';
COMMENT ON COLUMN "sys_task"."json_param" IS 'json格式参数';
COMMENT ON COLUMN "sys_task"."task_status" IS '状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时)';
COMMENT ON COLUMN "sys_task"."create_time" IS '创建时间';
COMMENT ON COLUMN "sys_task"."update_time" IS '更新时间';
COMMENT ON COLUMN "sys_task"."remark" IS '备注';
COMMENT ON COLUMN "sys_task"."timeout" IS '超时时间;单位秒';
COMMENT ON COLUMN "sys_task"."start_time" IS '开始时间';
COMMENT ON COLUMN "sys_task"."end_time" IS '结束时间';
COMMENT ON COLUMN "sys_task"."policy" IS '是否允许重复执行;0-不允许，1允许';

-- ----------------------------
-- Table structure for sys_task_log
-- ----------------------------
DROP TABLE IF EXISTS "sys_task_log";
CREATE TABLE "sys_task_log" (
                                "task_log_id" int8 NOT NULL  ,
                                "task_id" int8 NOT NULL  ,
                                "task_message" varchar(3000) NULL  ,
                                "status" varchar(1) NOT NULL  ,
                                "exception_info" varchar(6000) NULL  ,
                                "create_time" varchar(30) NULL  ,
                                "trigger_time" int8 NULL
);
ALTER TABLE "sys_task_log" ADD CONSTRAINT "sys_task_log_pkey" PRIMARY KEY ("task_log_id");
COMMENT ON TABLE "sys_task_log" IS '异步任务执行日志表';
COMMENT ON COLUMN "sys_task_log"."task_log_id" IS '任务日志ID';
COMMENT ON COLUMN "sys_task_log"."task_id" IS '任务ID';
COMMENT ON COLUMN "sys_task_log"."task_message" IS '日志信息';
COMMENT ON COLUMN "sys_task_log"."status" IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN "sys_task_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "sys_task_log"."create_time" IS '创建时间;单位毫秒';
COMMENT ON COLUMN "sys_task_log"."trigger_time" IS '触发时间;任务服务上送';

-- ----------------------------
-- Table structure for up_app
-- ----------------------------
DROP TABLE IF EXISTS "up_app";
CREATE TABLE "up_app" (
                          "id" int8 NOT NULL  ,
                          "app_id" varchar(1000) NOT NULL  ,
                          "app_name" varchar(1000) NOT NULL  ,
                          "app_type" int4 NOT NULL  ,
                          "province_code" varchar(100) NOT NULL  ,
                          "remark" varchar(3000) NULL  ,
                          "app_scenarios" varchar(3000) NULL  ,
                          "create_by" int8 NULL  ,
                          "create_time" varchar(30) NULL  ,
                          "update_by" int8 NULL  ,
                          "update_time" varchar(30) NULL
);
ALTER TABLE "up_app" ADD CONSTRAINT "up_app_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "up_app" IS '密码应用';
COMMENT ON COLUMN "up_app"."id" IS 'ID';
COMMENT ON COLUMN "up_app"."app_id" IS '业务系统ID，内部ID即可，主要用于和密评信息和资料关联使用';
COMMENT ON COLUMN "up_app"."app_name" IS '业务系统名称';
COMMENT ON COLUMN "up_app"."app_type" IS '业务系统类型,可选择类型如下，1：重要业务系统；2：关基业务系统；3：其他';
COMMENT ON COLUMN "up_app"."province_code" IS '参照附录省份编码，添加对应省份';
COMMENT ON COLUMN "up_app"."remark" IS '业务系统的描述';
COMMENT ON COLUMN "up_app"."app_scenarios" IS '描述当前业务系统应用的主要场景';
COMMENT ON COLUMN "up_app"."create_by" IS '创建人';
COMMENT ON COLUMN "up_app"."create_time" IS '创建时间';
COMMENT ON COLUMN "up_app"."update_by" IS '更新人';
COMMENT ON COLUMN "up_app"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for up_app_cypher_type
-- ----------------------------
DROP TABLE IF EXISTS "up_app_cypher_type";
CREATE TABLE "up_app_cypher_type" (
                                      "id" int8 NOT NULL  ,
                                      "up_app_id" int8 NOT NULL  ,
                                      "cypher_type_code" varchar(100) NOT NULL  ,
                                      "cypher_type_name" varchar(1000) NOT NULL  ,
                                      "product_type" varchar(100) NOT NULL  ,
                                      "product_id" varchar(1000) NULL  ,
                                      "product_name" varchar(1000) NULL  ,
                                      "product_firm" varchar(1000) NULL  ,
                                      "create_by" int8 NULL  ,
                                      "create_time" varchar(30) NULL  ,
                                      "update_by" int8 NULL  ,
                                      "update_time" varchar(30) NULL
);
ALTER TABLE "up_app_cypher_type" ADD CONSTRAINT "up_app_cypher_type_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "up_app_cypher_type" IS '密码应用业务类型';
COMMENT ON COLUMN "up_app_cypher_type"."id" IS 'ID';
COMMENT ON COLUMN "up_app_cypher_type"."up_app_id" IS '密码应用ID';
COMMENT ON COLUMN "up_app_cypher_type"."cypher_type_code" IS '业务系统进行密码改造采用的密码产品。具体密码业务类型参照附录：密码业务类型';
COMMENT ON COLUMN "up_app_cypher_type"."cypher_type_name" IS '密码业务名称';
COMMENT ON COLUMN "up_app_cypher_type"."product_type" IS '密码业务使用的密码产品形态，具体形态参考服务：密码产品形态';
COMMENT ON COLUMN "up_app_cypher_type"."product_id" IS '密码产品ID';
COMMENT ON COLUMN "up_app_cypher_type"."product_name" IS '密码产品名称';
COMMENT ON COLUMN "up_app_cypher_type"."product_firm" IS '提供该密码产品的厂商名称';
COMMENT ON COLUMN "up_app_cypher_type"."create_by" IS '创建人';
COMMENT ON COLUMN "up_app_cypher_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "up_app_cypher_type"."update_by" IS '更新人';
COMMENT ON COLUMN "up_app_cypher_type"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for up_assess
-- ----------------------------
DROP TABLE IF EXISTS "up_assess";
CREATE TABLE "up_assess" (
                             "id" int8 NOT NULL  ,
                             "assess_id" varchar(1000) NOT NULL  ,
                             "province_code" varchar(100) NOT NULL  ,
                             "app_id" varchar(1000) NOT NULL  ,
                             "assess_reform_code" varchar(100) NOT NULL  ,
                             "assess_reform_rate" int4 NULL  ,
                             "product_firm" varchar(1000) NULL  ,
                             "assess_agency" varchar(1000) NULL  ,
                             "assess_level" int4 NULL  ,
                             "assess_score" int4 NULL  ,
                             "create_by" int8 NULL  ,
                             "create_time" varchar(30) NULL  ,
                             "update_by" int8 NULL  ,
                             "update_time" varchar(30) NULL
);
ALTER TABLE "up_assess" ADD CONSTRAINT "up_assess_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "up_assess" IS '密评信息';
COMMENT ON COLUMN "up_assess"."id" IS 'ID';
COMMENT ON COLUMN "up_assess"."assess_id" IS '密评ID';
COMMENT ON COLUMN "up_assess"."province_code" IS '参照附录省份编码，添加对应省份';
COMMENT ON COLUMN "up_assess"."app_id" IS '业务系统ID，内部ID即可';
COMMENT ON COLUMN "up_assess"."assess_reform_code" IS '根据密码阶段，上报当前进度。具体密改阶段参照附录密码改造阶段编码';
COMMENT ON COLUMN "up_assess"."assess_reform_rate" IS '根据密评进度不同类型内部的进度100为完成。根据进度填写具体进度0~100';
COMMENT ON COLUMN "up_assess"."product_firm" IS '提供密码改造支持的厂商名称，可多个逗号隔开';
COMMENT ON COLUMN "up_assess"."assess_agency" IS '密评机构名称';
COMMENT ON COLUMN "up_assess"."assess_level" IS '应用系统完成的安全等级:1:一级,2：二级,3：三级,4:四级';
COMMENT ON COLUMN "up_assess"."assess_score" IS '密评得分';
COMMENT ON COLUMN "up_assess"."create_by" IS '创建人';
COMMENT ON COLUMN "up_assess"."create_time" IS '创建时间';
COMMENT ON COLUMN "up_assess"."update_by" IS '更新人';
COMMENT ON COLUMN "up_assess"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for up_certificate
-- ----------------------------
DROP TABLE IF EXISTS "up_certificate";
CREATE TABLE "up_certificate" (
                                  "id" int8 NOT NULL  ,
                                  "certificate_id" varchar(1000) NOT NULL  ,
                                  "certificate_code" varchar(1000) NOT NULL  ,
                                  "province_code" varchar(100) NOT NULL  ,
                                  "key_certificate_type" varchar(100) NOT NULL  ,
                                  "app_id" varchar(1000) NULL  ,
                                  "certificate_algorithm" varchar(1000) NULL  ,
                                  "certificate_state" int4 NULL  ,
                                  "create_by" int8 NULL  ,
                                  "create_time" varchar(30) NULL  ,
                                  "update_by" int8 NULL  ,
                                  "update_time" varchar(30) NULL
);
ALTER TABLE "up_certificate" ADD CONSTRAINT "up_certificate_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "up_certificate" IS '证书';
COMMENT ON COLUMN "up_certificate"."id" IS 'ID';
COMMENT ON COLUMN "up_certificate"."certificate_id" IS '证书ID，内部ID即可';
COMMENT ON COLUMN "up_certificate"."certificate_code" IS '证书的标识、名称或编号';
COMMENT ON COLUMN "up_certificate"."province_code" IS '参照附录省份编码，添加对应省份';
COMMENT ON COLUMN "up_certificate"."key_certificate_type" IS '证书类型。参考附录密钥证书类型中证书类型';
COMMENT ON COLUMN "up_certificate"."app_id" IS '所属应用系统ID';
COMMENT ON COLUMN "up_certificate"."certificate_algorithm" IS '证书采用的具体算法名称';
COMMENT ON COLUMN "up_certificate"."certificate_state" IS '证书状态：0：未使用；1：使用中';
COMMENT ON COLUMN "up_certificate"."create_by" IS '创建人';
COMMENT ON COLUMN "up_certificate"."create_time" IS '创建时间';
COMMENT ON COLUMN "up_certificate"."update_by" IS '更新人';
COMMENT ON COLUMN "up_certificate"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for up_document
-- ----------------------------
DROP TABLE IF EXISTS "up_document";
CREATE TABLE "up_document" (
                               "id" int8 NOT NULL  ,
                               "document_id" varchar(1000) NOT NULL  ,
                               "document_name" varchar(1000) NOT NULL  ,
                               "province_code" varchar(100) NOT NULL  ,
                               "object_type" int4 NULL  ,
                               "object_id" varchar(1000) NULL  ,
                               "document_type" varchar(1000) NOT NULL  ,
                               "file_extension" varchar(1000) NOT NULL  ,
                               "file_id" int8 NOT NULL  ,
                               "create_by" int8 NULL  ,
                               "create_time" varchar(30) NULL  ,
                               "update_by" int8 NULL  ,
                               "update_time" varchar(30) NULL
);
ALTER TABLE "up_document" ADD CONSTRAINT "up_document_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "up_document" IS '密码文档';
COMMENT ON COLUMN "up_document"."id" IS 'ID';
COMMENT ON COLUMN "up_document"."document_id" IS '文档ID，内部ID即可';
COMMENT ON COLUMN "up_document"."document_name" IS '问档的名称';
COMMENT ON COLUMN "up_document"."province_code" IS '参照附录省份编码，添加对应省份';
COMMENT ON COLUMN "up_document"."object_type" IS '所属对象类型，分为1：应用系统、2：密码产品、3：安全漏洞事件';
COMMENT ON COLUMN "up_document"."object_id" IS '根据所属对象类型，录入应用系统、密码产品或安全漏洞事件的ID，用于数据和文件关联';
COMMENT ON COLUMN "up_document"."document_type" IS '文档类型参照附录资料类型';
COMMENT ON COLUMN "up_document"."file_extension" IS '文件扩展名，如pdf、doc、excel等，根据不同文件类型，采用不同的展示形态';
COMMENT ON COLUMN "up_document"."file_id" IS '文件存储的FILE_INFO表ID';
COMMENT ON COLUMN "up_document"."create_by" IS '创建人';
COMMENT ON COLUMN "up_document"."create_time" IS '创建时间';
COMMENT ON COLUMN "up_document"."update_by" IS '更新人';
COMMENT ON COLUMN "up_document"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for up_key
-- ----------------------------
DROP TABLE IF EXISTS "up_key";
CREATE TABLE "up_key" (
                          "id" int8 NOT NULL  ,
                          "key_id" varchar(1000) NOT NULL  ,
                          "key_code" varchar(2000) NOT NULL  ,
                          "province_code" varchar(100) NOT NULL  ,
                          "app_id" varchar(1000) NULL  ,
                          "key_certificate_type" varchar(100) NOT NULL  ,
                          "key_algorithm" varchar(1000) NOT NULL  ,
                          "key_length" int4 NOT NULL  ,
                          "key_state" int4 NOT NULL  ,
                          "create_by" int8 NULL  ,
                          "create_time" varchar(30) NULL  ,
                          "update_by" int8 NULL  ,
                          "update_time" varchar(30) NULL
);
ALTER TABLE "up_key" ADD CONSTRAINT "up_key_pkey" PRIMARY KEY ("id");
-- 索引
DROP INDEX IF EXISTS "idx_up_key_province_code";
CREATE INDEX "idx_up_key_province_code" ON "up_key" ("province_code");
DROP INDEX IF EXISTS "idx_up_key_key_state";
CREATE INDEX "idx_up_key_key_state" ON "up_key" ("key_state");
DROP INDEX IF EXISTS "idx_up_key_province_code_key_id";
CREATE INDEX "idx_up_key_province_code_key_id" ON "up_key" ("province_code","key_id");
--
COMMENT ON TABLE "up_key" IS '密钥';
COMMENT ON COLUMN "up_key"."id" IS 'ID';
COMMENT ON COLUMN "up_key"."key_id" IS '密钥ID，内部ID即可';
COMMENT ON COLUMN "up_key"."key_code" IS '密钥的标识、名称或编号';
COMMENT ON COLUMN "up_key"."province_code" IS '参照附录省份编码，添加对应省份';
COMMENT ON COLUMN "up_key"."app_id" IS '所属应用系统ID';
COMMENT ON COLUMN "up_key"."key_certificate_type" IS '密钥类型。参考附录密钥证书类型中密钥类型';
COMMENT ON COLUMN "up_key"."key_algorithm" IS '密钥算法';
COMMENT ON COLUMN "up_key"."key_length" IS '密钥长度';
COMMENT ON COLUMN "up_key"."key_state" IS '密钥状态;0:未激活；1：激活；2：归档';
COMMENT ON COLUMN "up_key"."create_by" IS '创建人';
COMMENT ON COLUMN "up_key"."create_time" IS '创建时间';
COMMENT ON COLUMN "up_key"."update_by" IS '更新人';
COMMENT ON COLUMN "up_key"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for up_product
-- ----------------------------
DROP TABLE IF EXISTS "up_product";
CREATE TABLE "up_product" (
                              "id" int8 NOT NULL  ,
                              "product_id" varchar(1000) NOT NULL  ,
                              "product_name" varchar(1000) NOT NULL  ,
                              "product_type" varchar(100) NOT NULL  ,
                              "province_code" varchar(100) NOT NULL  ,
                              "product_firm" varchar(1000) NULL  ,
                              "product_ref" varchar(100) NULL  ,
                              "product_address" varchar(1000) NULL  ,
                              "product_state" int4 NULL  ,
                              "product_remark" varchar(5000) NULL  ,
                              "create_by" int8 NULL  ,
                              "create_time" varchar(30) NULL  ,
                              "update_by" int8 NULL  ,
                              "update_time" varchar(30) NULL
);
ALTER TABLE "up_product" ADD CONSTRAINT "up_product_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "up_product" IS '无注释';
COMMENT ON COLUMN "up_product"."id" IS 'ID';
COMMENT ON COLUMN "up_product"."product_id" IS '密码产品ID';
COMMENT ON COLUMN "up_product"."product_name" IS '密码产品名称';
COMMENT ON COLUMN "up_product"."product_type" IS '密码产品的不同形态，具体形态参考服务：密码产品形态';
COMMENT ON COLUMN "up_product"."province_code" IS '参照附录省份编码，添加对应省份';
COMMENT ON COLUMN "up_product"."product_firm" IS '提供该密码产品的厂商名称';
COMMENT ON COLUMN "up_product"."product_ref" IS '商用密码产品的认证证书编号';
COMMENT ON COLUMN "up_product"."product_address" IS '密码产品的IP、端口或域名';
COMMENT ON COLUMN "up_product"."product_state" IS '当前密码产品的使用状态； 1：使用中、2:空闲中、3：异常、4：停用';
COMMENT ON COLUMN "up_product"."product_remark" IS '密码产品的功能描述和应用场景等信息';
COMMENT ON COLUMN "up_product"."create_by" IS '创建人';
COMMENT ON COLUMN "up_product"."create_time" IS '创建时间';
COMMENT ON COLUMN "up_product"."update_by" IS '更新人';
COMMENT ON COLUMN "up_product"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for up_product_cypher_type
-- ----------------------------
DROP TABLE IF EXISTS "up_product_cypher_type";
CREATE TABLE "up_product_cypher_type" (
                                          "id" int8 NOT NULL  ,
                                          "up_product_id" int8 NOT NULL  ,
                                          "cypher_type_code" varchar(100) NOT NULL  ,
                                          "cypher_type_name" varchar(1000) NOT NULL  ,
                                          "create_by" int8 NULL  ,
                                          "create_time" varchar(30) NULL  ,
                                          "update_by" int8 NULL  ,
                                          "update_time" varchar(30) NULL
);
ALTER TABLE "up_product_cypher_type" ADD CONSTRAINT "up_product_cypher_type_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "up_product_cypher_type" IS '密码资产业务类型';
COMMENT ON COLUMN "up_product_cypher_type"."id" IS 'ID';
COMMENT ON COLUMN "up_product_cypher_type"."up_product_id" IS '密码资产ID';
COMMENT ON COLUMN "up_product_cypher_type"."cypher_type_code" IS '业务系统进行密码改造采用的密码产品。具体密码业务类型参照附录：密码业务类型';
COMMENT ON COLUMN "up_product_cypher_type"."cypher_type_name" IS '密码业务名称';
COMMENT ON COLUMN "up_product_cypher_type"."create_by" IS '创建人';
COMMENT ON COLUMN "up_product_cypher_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "up_product_cypher_type"."update_by" IS '更新人';
COMMENT ON COLUMN "up_product_cypher_type"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for up_security_flaw_event
-- ----------------------------
DROP TABLE IF EXISTS "up_security_flaw_event";
CREATE TABLE "up_security_flaw_event" (
                                          "id" int8 NOT NULL  ,
                                          "flaw_event_id" varchar(1000) NOT NULL  ,
                                          "flaw_event_name" varchar(1000) NOT NULL  ,
                                          "flaw_event_type" varchar(100) NOT NULL  ,
                                          "province_code" varchar(100) NOT NULL  ,
                                          "app_id" varchar(1000) NULL  ,
                                          "remark" varchar(3000) NULL  ,
                                          "influence" varchar(3000) NULL  ,
                                          "state" int4 NOT NULL  ,
                                          "create_by" int8 NULL  ,
                                          "create_time" varchar(30) NULL  ,
                                          "update_by" int8 NULL  ,
                                          "update_time" varchar(30) NULL
);
ALTER TABLE "up_security_flaw_event" ADD CONSTRAINT "up_security_flaw_event_pkey" PRIMARY KEY ("id");
COMMENT ON TABLE "up_security_flaw_event" IS '安全漏洞事件';
COMMENT ON COLUMN "up_security_flaw_event"."id" IS 'ID';
COMMENT ON COLUMN "up_security_flaw_event"."flaw_event_id" IS '安全漏洞事件ID，内部ID即可';
COMMENT ON COLUMN "up_security_flaw_event"."flaw_event_name" IS '安全漏洞事件名称';
COMMENT ON COLUMN "up_security_flaw_event"."flaw_event_type" IS '安全漏洞事件类型,可选择类型参考附录安全漏洞事件类型';
COMMENT ON COLUMN "up_security_flaw_event"."province_code" IS '参照附录省份编码，添加对应省份';
COMMENT ON COLUMN "up_security_flaw_event"."app_id" IS '所属应用系统';
COMMENT ON COLUMN "up_security_flaw_event"."remark" IS '安全事件的描述信息';
COMMENT ON COLUMN "up_security_flaw_event"."influence" IS '本次安全事件的影响范围';
COMMENT ON COLUMN "up_security_flaw_event"."state" IS '当前安全事件的处理状态。1：未处理、2：处理中、3：已处理';
COMMENT ON COLUMN "up_security_flaw_event"."create_by" IS '创建人';
COMMENT ON COLUMN "up_security_flaw_event"."create_time" IS '创建时间';
COMMENT ON COLUMN "up_security_flaw_event"."update_by" IS '更新人';
COMMENT ON COLUMN "up_security_flaw_event"."update_time" IS '更新时间';




-- @nzd-----------------------------------------------start


INSERT INTO config (CONFIG_ID, CONFIG_CODE, CONFIG_NAME, CONFIG_VALUE, CONFIG_TYPE, MAINTAIN_FLAG, SORD_NUM, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(1, 'province_platfrom_aksk_key_num', '省级平台aksk数量限制', '10', 0, 1, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO config (config_id, config_code, config_name, config_value, config_type, maintain_flag, sord_num, remark, create_by, create_time, update_by, update_time) VALUES(2, 'up_url', '集团平台路径', '', 0, 1, 2, NULL, NULL, NULL, NULL, NULL);
INSERT INTO config (config_id, config_code, config_name, config_value, config_type, maintain_flag, sord_num, remark, create_by, create_time, update_by, update_time) VALUES(3, 'ak', 'ak', '', 0, 1, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO config (config_id, config_code, config_name, config_value, config_type, maintain_flag, sord_num, remark, create_by, create_time, update_by, update_time) VALUES(4, 'sk', 'sk', '', 1, 1, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO config (config_id, config_code, config_name, config_value, config_type, maintain_flag, sord_num, remark, create_by, create_time, update_by, update_time) VALUES(5, 'operCode', '操作员账户名', NULL, 0, 1, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO config (config_id, config_code, config_name, config_value, config_type, maintain_flag, sord_num, remark, create_by, create_time, update_by, update_time) VALUES(6, 'operAuth', '操作员密码', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO config (config_id, config_code, config_name, config_value, config_type, maintain_flag, sord_num, remark, create_by, create_time, update_by, update_time) VALUES(7, 'platformCode', '省级平台标识', NULL, 0, 1, 1, NULL, NULL, NULL, NULL, NULL);


INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(1, 'reportAppInfo', 'reportAppInfo', 'ccsp-data-service', 'app', 'dataReportServiceImpl.reportAppData()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(2, 'reportProductInfo', 'reportProductInfo', 'ccsp-data-service', 'product', 'dataReportServiceImpl.reportProductInfo()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(3, 'reportKeyInfo', 'reportKeyInfo', 'ccsp-data-service', 'key', 'dataReportServiceImpl.reportKeyInfo()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(4, 'reportCertificateInfo', 'reportCertificateInfo', 'ccsp-data-service', 'certificate', 'dataReportServiceImpl.reportCertificateInfo()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(5, 'reportDocumentInfo', 'reportDocumentInfo', 'ccsp-data-service', 'document', 'dataReportServiceImpl.reportDocumentInfo()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(6, 'reportAssessInfo', 'reportAssessInfo', 'ccsp-data-service', 'assess', 'dataReportServiceImpl.reportAssessInfo()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(7, 'reportSecurityFlawEvent', 'reportSecurityFlawEvent', 'ccsp-data-service', 'securityFlaw', 'dataReportServiceImpl.reportSecurityFlawEvent()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(8, 'fetchAppData', 'fetchAppData', 'ccsp-data-service', 'app', 'dataFetchServiceImpl.fetchAppData()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(9, 'fetchProductInfo', 'fetchProductInfo', 'ccsp-data-service', 'product', 'dataFetchServiceImpl.fetchProductInfo()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(10, 'fetchKey', 'fetchKey', 'ccsp-data-service', 'key', 'dataFetchServiceImpl.fetchKey()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(11, 'fetchCertificate', 'fetchCertificate', 'ccsp-data-service', 'certificate', 'dataFetchServiceImpl.fetchCertificate()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(12, 'fetchDocumentInfo', 'fetchDocumentInfo', 'ccsp-data-service', 'document', 'dataFetchServiceImpl.fetchDocumentInfo()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sys_job (job_id, job_name, job_group, server_id, method_url, json_param, cron_expression, misfire_policy, concurrent, job_status, created_by, create_time, updated_by, update_time, remark) VALUES(13, 'fetchAssessInfo', 'fetchAssessInfo', 'ccsp-data-service', 'assess', 'dataFetchServiceImpl.fetchAssessInfo()', '0 0 0 ? * 1', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);


INSERT INTO report_config (id, report_code, report_name, frequency_id, enable, last_report_time, remark, create_by, create_time, update_by, update_time) VALUES(1, 'reportAppInfo', '密码应用数据上报', 3, 0,  NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO report_config (id, report_code, report_name, frequency_id, enable, last_report_time, remark, create_by, create_time, update_by, update_time) VALUES(2, 'reportProductInfo', '密码产品信息上报', 3, 0,  NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO report_config (id, report_code, report_name, frequency_id, enable, last_report_time, remark, create_by, create_time, update_by, update_time) VALUES(3, 'reportKeyInfo', '密钥信息上报', 3, 0,  NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO report_config (id, report_code, report_name, frequency_id, enable, last_report_time, remark, create_by, create_time, update_by, update_time) VALUES(4, 'reportCertificateInfo', '证书信息上报', 3, 0,  NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO report_config (id, report_code, report_name, frequency_id, enable, last_report_time, remark, create_by, create_time, update_by, update_time) VALUES(5, 'reportDocumentInfo', '密码文档信息上报', 3, 0,  NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO report_config (id, report_code, report_name, frequency_id, enable, last_report_time, remark, create_by, create_time, update_by, update_time) VALUES(6, 'reportAssessInfo', '密码应用测评情况上报', 3, 0,  NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO report_config (id, report_code, report_name, frequency_id, enable, last_report_time, remark, create_by, create_time, update_by, update_time) VALUES(7, 'reportSecurityFlawEvent', '密码应用漏洞/安全事件上报', 3, 0,  NULL, NULL, NULL, NULL, NULL, NULL);

-- @nzd-----------------------------------------------end

