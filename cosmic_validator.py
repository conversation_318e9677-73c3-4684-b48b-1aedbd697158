#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能拆解校验模块

该模块实现以下功能：
1. 解析CSV格式的cosmic功能拆解数据
2. 将CSV数据转换为层次化JSON结构
3. 调用大模型进行cosmic规范校验
4. 输出校验结果和修改建议
"""

import csv
import json
import pandas as pd
import requests
import time
import math
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, OrderedDict


class CosmicValidator:
    """COSMIC功能拆解校验器"""
    
    def __init__(self, config):
        """
        初始化校验器

        Args:
            config: 配置模块，包含大模型配置等
        """
        self.config = config

        # 使用校验专用配置，如果为None则使用默认配置
        self.endpoint_url = getattr(config, 'CHECK_ENDPOINT_URL', None) or config.ENDPOINT_URL
        self.model_name = getattr(config, 'CHECK_MODEL_NAME', None) or config.MODEL_NAME
        self.api_key = getattr(config, 'CHECK_API_KEY', None) or config.API_KEY
        self.api_qpm = getattr(config, 'CHECK_API_QPM', None) or config.API_QPM
        self.api_tpm = getattr(config, 'CHECK_API_TPM', None) or config.API_TPM

        # 可配置的字段过滤列表
        self.excluded_fields = getattr(config, 'CHECK_EXCLUDED_FIELDS', [
            '预估工作量（人天）'  # 默认去除预估工作量字段
        ])

        # 限流状态
        self.qpm_counter = 0
        self.tpm_counter = 0
        self.window_start_time = time.time()
        
    def parse_csv_to_hierarchical_json(self, csv_file_path: str) -> Dict[str, Any]:
        """
        解析CSV文件并转换为层次化JSON结构
        
        Args:
            csv_file_path: CSV文件路径
            
        Returns:
            层次化的JSON数据结构
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
            
            # 过滤掉不需要的字段
            for field in self.excluded_fields:
                if field in df.columns:
                    df = df.drop(field, axis=1)
            
            # 构建层次化结构
            hierarchical_data = self._build_hierarchical_structure(df)
            
            return hierarchical_data
            
        except Exception as e:
            print(f"解析CSV文件失败: {e}")
            return {}
    
    def _build_hierarchical_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        构建层次化数据结构

        Args:
            df: pandas DataFrame

        Returns:
            层次化的数据结构
        """
        result = {
            "cosmic_data": {
                "modules": defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
            },
            "summary": {
                "total_records": len(df),
                "level1_modules": set(),
                "level2_modules": set(),
                "level3_modules": set(),
                "data_movement_types": defaultdict(int),
                "cfp_distribution": defaultdict(int)
            }
        }

        for _, row in df.iterrows():
            level1 = str(row.get('一级功能模块', '')).strip()
            level2 = str(row.get('二级功能模块', '')).strip()
            level3 = str(row.get('三级功能模块', '')).strip()

            # 跳过空值
            if not level1 or not level2 or not level3:
                continue

            # 构建记录
            record = {}
            for col in df.columns:
                if col not in ['一级功能模块', '二级功能模块', '三级功能模块']:
                    value = str(row[col]) if pd.notna(row[col]) else ""
                    record[col] = value

                    # 统计数据移动类型
                    if col == '数据移动类型' and value:
                        result["summary"]["data_movement_types"][value] += 1

                    # 统计CFP分布
                    if col == 'CFP' and value:
                        try:
                            cfp_value = int(value)
                            result["summary"]["cfp_distribution"][cfp_value] += 1
                        except ValueError:
                            pass

            # 添加到层次结构
            result["cosmic_data"]["modules"][level1][level2][level3].append(record)

            # 更新统计信息
            result["summary"]["level1_modules"].add(level1)
            result["summary"]["level2_modules"].add(level2)
            result["summary"]["level3_modules"].add(level3)

        # 转换set为list以便JSON序列化
        result["summary"]["level1_modules"] = list(result["summary"]["level1_modules"])
        result["summary"]["level2_modules"] = list(result["summary"]["level2_modules"])
        result["summary"]["level3_modules"] = list(result["summary"]["level3_modules"])
        result["summary"]["data_movement_types"] = dict(result["summary"]["data_movement_types"])
        result["summary"]["cfp_distribution"] = dict(result["summary"]["cfp_distribution"])

        return result

    def _group_by_function_process(self, hierarchical_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        按功能过程重新组织数据，便于校验

        Args:
            hierarchical_data: 层次化数据

        Returns:
            按功能过程组织的数据
        """
        function_processes = {}

        for level1, level2_data in hierarchical_data["cosmic_data"]["modules"].items():
            for level2, level3_data in level2_data.items():
                for level3, records in level3_data.items():
                    # 按功能过程分组
                    process_groups = defaultdict(list)
                    for record in records:
                        process_name = record.get('功能过程', '未知功能过程')
                        process_groups[process_name].append(record)

                    # 构建功能过程数据
                    for process_name, process_records in process_groups.items():
                        process_key = f"{level1}/{level2}/{level3}/{process_name}"
                        function_processes[process_key] = {
                            "module_path": f"{level1}/{level2}/{level3}",
                            "function_process": process_name,
                            "subprocesses": process_records,
                            "subprocess_count": len(process_records),
                            "data_movements": self._analyze_data_movements(process_records)
                        }

        return function_processes

    def _analyze_data_movements(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析功能过程中的数据移动情况

        Args:
            records: 子过程记录列表

        Returns:
            数据移动分析结果
        """
        movements = {"E": 0, "R": 0, "W": 0, "X": 0}
        data_groups = set()
        total_cfp = 0

        for record in records:
            # 统计数据移动类型
            movement_type = record.get('数据移动类型', '')
            if movement_type in movements:
                movements[movement_type] += 1

            # 收集数据组
            data_group = record.get('数据组', '')
            if data_group:
                data_groups.add(data_group)

            # 统计CFP
            try:
                cfp = int(record.get('CFP', 0))
                total_cfp += cfp
            except ValueError:
                pass

        return {
            "movement_counts": movements,
            "unique_data_groups": list(data_groups),
            "data_group_count": len(data_groups),
            "total_cfp": total_cfp,
            "has_input": movements["E"] > 0,
            "has_output": movements["X"] > 0,
            "is_complete": movements["E"] > 0 and movements["X"] > 0
        }
    
    def _call_llm_for_validation(self, prompt: str, cosmic_data: str) -> Optional[str]:
        """
        调用大模型进行校验
        
        Args:
            prompt: 系统提示词
            cosmic_data: 要校验的cosmic数据
            
        Returns:
            大模型返回的校验结果
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": cosmic_data}
        ]
        
        data = {
            "model": self.model_name,
            "messages": messages,
            "temperature": 0.1,  # 校验任务使用较低温度
            "stream": False
        }
        
        # 限流处理
        self._handle_rate_limiting(prompt + cosmic_data)
        
        try:
            response = requests.post(self.endpoint_url, headers=headers, json=data, timeout=300)
            response.raise_for_status()
            
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            # 更新token计数
            output_token_count = math.ceil(len(content) * 1.5)
            self.tpm_counter += output_token_count
            
            return content
            
        except Exception as e:
            print(f"调用大模型失败: {e}")
            return None
    
    def _handle_rate_limiting(self, input_text: str):
        """处理API限流"""
        current_time = time.time()
        
        # 重置窗口
        if current_time - self.window_start_time >= 60:
            self.qpm_counter = 0
            self.tpm_counter = 0
            self.window_start_time = current_time
        
        # 输入token估算
        input_token_count = math.ceil(len(input_text) * 1.5)
        
        # 等待限流
        while self.qpm_counter >= self.api_qpm or self.tpm_counter + input_token_count > self.api_tpm:
            sleep_time = max(60 - (current_time - self.window_start_time) + 0.1, 0)
            print(f"达到限流阈值，等待{sleep_time:.1f}秒...")
            time.sleep(sleep_time)
            current_time = time.time()
            if current_time - self.window_start_time >= 60:
                self.qpm_counter = 0
                self.tpm_counter = 0
                self.window_start_time = current_time
        
        # 更新计数器
        self.qpm_counter += 1
        self.tpm_counter += input_token_count
    
    def validate_cosmic_data(self, csv_file_path: str, prompt_file_path: str = "check_prompt.md") -> Dict[str, Any]:
        """
        校验cosmic数据

        Args:
            csv_file_path: CSV文件路径
            prompt_file_path: 提示词文件路径

        Returns:
            校验结果
        """
        print("开始解析CSV文件...")
        hierarchical_data = self.parse_csv_to_hierarchical_json(csv_file_path)

        if not hierarchical_data:
            return {"error": "CSV文件解析失败"}

        print("读取系统提示词...")
        try:
            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                prompt = f.read()
        except FileNotFoundError:
            return {"error": f"提示词文件 {prompt_file_path} 不存在"}

        print("按功能过程重新组织数据...")
        function_processes = self._group_by_function_process(hierarchical_data)

        print("准备校验数据...")
        # 构建更适合校验的数据结构
        validation_data = {
            "summary": hierarchical_data["summary"],
            "function_processes": function_processes,
            "validation_focus": {
                "completeness_check": "每个功能过程是否包含至少1个E和1个X",
                "data_group_aggregation": "是否将关联数据属性合并为最小单元",
                "storage_boundary": "R/W是否仅针对边界内持久存储",
                "no_duplicate_counting": "同一数据组在同一功能过程中是否被重复计数"
            }
        }

        validation_data_str = json.dumps(validation_data, ensure_ascii=False, indent=2)

        # 检查数据大小
        data_size = len(validation_data_str)
        estimated_tokens = math.ceil(data_size * 1.5)
        print(f"数据大小: {data_size} 字符, 预估token: {estimated_tokens}")

        # 如果数据太大，进行简化处理
        if estimated_tokens > 50000:  # 如果超过50k tokens
            print("数据量过大，进行简化处理...")
            simplified_data = self._simplify_validation_data(validation_data)
            validation_data_str = json.dumps(simplified_data, ensure_ascii=False, indent=2)
            print(f"简化后数据大小: {len(validation_data_str)} 字符")

        # 保存调试信息
        debug_file = "debug/cosmic_validation_input.json"
        try:
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(validation_data_str)
            print(f"调试数据已保存到: {debug_file}")
        except Exception as e:
            print(f"保存调试数据失败: {e}")

        print("调用大模型进行校验...")
        validation_result = self._call_llm_for_validation(prompt, validation_data_str)

        if validation_result is None:
            return {"error": "大模型调用失败"}

        # 尝试解析JSON格式的校验结果
        parsed_result = self._parse_validation_result(validation_result)

        # 保存校验结果
        result = {
            "input_summary": hierarchical_data["summary"],
            "function_process_summary": {
                "total_processes": len(function_processes),
                "complete_processes": sum(1 for fp in function_processes.values()
                                        if fp["data_movements"]["is_complete"]),
                "incomplete_processes": sum(1 for fp in function_processes.values()
                                          if not fp["data_movements"]["is_complete"])
            },
            "validation_result": parsed_result if parsed_result else validation_result,
            "raw_validation_result": validation_result,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        return result

    def _parse_validation_result(self, validation_result: str) -> Optional[Dict[str, Any]]:
        """
        尝试解析大模型返回的JSON格式校验结果

        Args:
            validation_result: 大模型返回的原始结果

        Returns:
            解析后的JSON对象，如果解析失败则返回None
        """
        try:
            # 尝试直接解析JSON
            return json.loads(validation_result)
        except json.JSONDecodeError:
            # 尝试提取JSON代码块
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', validation_result, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(1))
                except json.JSONDecodeError:
                    pass

            # 如果都失败了，返回None
            return None

    def _simplify_validation_data(self, validation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        简化校验数据，减少token使用量

        Args:
            validation_data: 原始校验数据

        Returns:
            简化后的校验数据
        """
        simplified = {
            "summary": validation_data["summary"],
            "validation_focus": validation_data["validation_focus"],
            "function_processes_sample": {},
            "analysis_summary": {
                "total_processes": len(validation_data["function_processes"]),
                "complete_processes": 0,
                "incomplete_processes": 0,
                "common_issues": []
            }
        }

        # 统计完整性
        complete_processes = []
        incomplete_processes = []

        for process_key, process_data in validation_data["function_processes"].items():
            if process_data["data_movements"]["is_complete"]:
                complete_processes.append(process_key)
            else:
                incomplete_processes.append((process_key, process_data))

        simplified["analysis_summary"]["complete_processes"] = len(complete_processes)
        simplified["analysis_summary"]["incomplete_processes"] = len(incomplete_processes)

        # 选择代表性样本进行详细校验
        sample_count = min(20, len(validation_data["function_processes"]))  # 最多20个样本

        # 优先选择不完整的功能过程
        selected_processes = {}

        # 添加所有不完整的功能过程（最多15个）
        for process_key, process_data in incomplete_processes[:15]:
            selected_processes[process_key] = process_data

        # 如果还有空间，添加一些完整的功能过程作为对比
        remaining_slots = sample_count - len(selected_processes)
        if remaining_slots > 0:
            for process_key in complete_processes[:remaining_slots]:
                selected_processes[process_key] = validation_data["function_processes"][process_key]

        simplified["function_processes_sample"] = selected_processes

        # 添加常见问题分析
        if incomplete_processes:
            simplified["analysis_summary"]["common_issues"].append(
                f"发现{len(incomplete_processes)}个不完整的功能过程（缺少E或X）"
            )

        return simplified
    
    def save_validation_result(self, result: Dict[str, Any], output_file: str = "debug/cosmic_validation_result.json"):
        """
        保存校验结果到文件

        Args:
            result: 校验结果
            output_file: 输出文件路径
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"校验结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存校验结果失败: {e}")

    def generate_validation_report(self, result: Dict[str, Any]) -> str:
        """
        生成人类可读的校验报告

        Args:
            result: 校验结果

        Returns:
            格式化的校验报告
        """
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("COSMIC功能拆解校验报告")
        report_lines.append("=" * 80)
        report_lines.append(f"生成时间: {result.get('timestamp', '未知')}")
        report_lines.append("")

        # 输入数据摘要
        input_summary = result.get('input_summary', {})
        report_lines.append("输入数据摘要:")
        report_lines.append(f"  总记录数: {input_summary.get('total_records', 0)}")
        report_lines.append(f"  一级模块数: {len(input_summary.get('level1_modules', []))}")
        report_lines.append(f"  二级模块数: {len(input_summary.get('level2_modules', []))}")
        report_lines.append(f"  三级模块数: {len(input_summary.get('level3_modules', []))}")

        # 数据移动类型分布
        data_movements = input_summary.get('data_movement_types', {})
        if data_movements:
            report_lines.append(f"  数据移动类型分布: {data_movements}")

        # CFP分布
        cfp_dist = input_summary.get('cfp_distribution', {})
        if cfp_dist:
            report_lines.append(f"  CFP分布: {cfp_dist}")

        report_lines.append("")

        # 功能过程摘要
        fp_summary = result.get('function_process_summary', {})
        if fp_summary:
            report_lines.append("功能过程摘要:")
            report_lines.append(f"  总功能过程数: {fp_summary.get('total_processes', 0)}")
            report_lines.append(f"  完整功能过程数: {fp_summary.get('complete_processes', 0)}")
            report_lines.append(f"  不完整功能过程数: {fp_summary.get('incomplete_processes', 0)}")

            total = fp_summary.get('total_processes', 0)
            complete = fp_summary.get('complete_processes', 0)
            if total > 0:
                completion_rate = (complete / total) * 100
                report_lines.append(f"  完整性比率: {completion_rate:.1f}%")
            report_lines.append("")

        # 校验结果
        validation_result = result.get('validation_result', {})
        if isinstance(validation_result, dict):
            # 结构化的校验结果
            overall = validation_result.get('overall_assessment', {})
            if overall:
                report_lines.append("整体评估:")
                report_lines.append(f"  合规率: {overall.get('compliance_rate', '未知')}")
                report_lines.append(f"  重大问题数: {overall.get('major_issues_count', 0)}")
                report_lines.append(f"  轻微问题数: {overall.get('minor_issues_count', 0)}")
                report_lines.append("")

            # 详细发现
            findings = validation_result.get('detailed_findings', [])
            if findings:
                report_lines.append("详细问题发现:")
                for i, finding in enumerate(findings[:10], 1):  # 只显示前10个问题
                    report_lines.append(f"  {i}. {finding.get('module_path', '未知路径')}")
                    report_lines.append(f"     功能过程: {finding.get('function_process', '未知')}")
                    report_lines.append(f"     问题类型: {finding.get('issue_type', '未知')}")
                    report_lines.append(f"     严重程度: {finding.get('severity', '未知')}")
                    report_lines.append(f"     问题描述: {finding.get('issue_description', '无描述')}")
                    report_lines.append(f"     修改建议: {finding.get('suggested_fix', '无建议')}")
                    report_lines.append("")

                if len(findings) > 10:
                    report_lines.append(f"  ... 还有 {len(findings) - 10} 个问题，详见完整报告")
                    report_lines.append("")

            # 总体建议
            recommendations = validation_result.get('summary_recommendations', [])
            if recommendations:
                report_lines.append("总体建议:")
                for rec in recommendations:
                    report_lines.append(f"  • {rec}")
                report_lines.append("")
        else:
            # 非结构化的校验结果
            report_lines.append("校验结果:")
            report_lines.append(str(validation_result))
            report_lines.append("")

        report_lines.append("=" * 80)

        return "\n".join(report_lines)

    def save_validation_report(self, result: Dict[str, Any], report_file: str = "debug/cosmic_validation_report.txt"):
        """
        保存人类可读的校验报告

        Args:
            result: 校验结果
            report_file: 报告文件路径
        """
        try:
            report = self.generate_validation_report(result)
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"校验报告已保存到: {report_file}")
        except Exception as e:
            print(f"保存校验报告失败: {e}")


def main():
    """主函数，用于测试"""
    import config

    print("初始化COSMIC校验器...")
    validator = CosmicValidator(config)

    # 校验output-new.csv文件
    print("开始校验cosmic数据...")
    result = validator.validate_cosmic_data("output-new.csv")

    if "error" in result:
        print(f"校验失败: {result['error']}")
        return

    print("校验完成！")

    # 显示摘要信息
    input_summary = result.get('input_summary', {})
    print(f"\n输入数据统计:")
    print(f"  总记录数: {input_summary.get('total_records', 0)}")
    print(f"  模块数量: L1={len(input_summary.get('level1_modules', []))}, "
          f"L2={len(input_summary.get('level2_modules', []))}, "
          f"L3={len(input_summary.get('level3_modules', []))}")

    fp_summary = result.get('function_process_summary', {})
    if fp_summary:
        print(f"  功能过程: 总数={fp_summary.get('total_processes', 0)}, "
              f"完整={fp_summary.get('complete_processes', 0)}, "
              f"不完整={fp_summary.get('incomplete_processes', 0)}")

    # 保存结果和报告
    print("\n保存校验结果...")
    validator.save_validation_result(result)
    validator.save_validation_report(result)

    # 显示校验结果预览
    validation_result = result.get('validation_result', {})
    if isinstance(validation_result, dict):
        overall = validation_result.get('overall_assessment', {})
        if overall:
            print(f"\n校验结果预览:")
            print(f"  合规率: {overall.get('compliance_rate', '未知')}")
            print(f"  重大问题: {overall.get('major_issues_count', 0)}")
            print(f"  轻微问题: {overall.get('minor_issues_count', 0)}")

    print("\n校验完成！请查看debug目录下的详细结果文件。")


if __name__ == "__main__":
    main()
