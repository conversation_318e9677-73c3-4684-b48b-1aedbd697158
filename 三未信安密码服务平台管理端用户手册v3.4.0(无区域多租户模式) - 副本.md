密码服务平台管理端

用户手册

# 1. 产品介绍

## 1.1.产品简介

密码作为信息安全防护的重要手段，在关键信息系统和基础网络防护中发挥着不可替代的重要作用。密码服务平台从服务的视角来进行设计，提供了直接满足用户业务需求的密码服务，其具有灵活的架构基础来支撑服务的多样性和可扩展性。

## 1.2.产品功能

密码服务管理平台以密码设备为基础，提供设备以及服务的集中统一管理方式，包含租户管理、密码设备管理、密码服务管理、用户管理等功能。通过管理平台用户可以方便快捷的完成设备的管理、服务的管理以及租户的快速接入使用，提高了系统的资源利用率、完善弥补了传统密码设备使用率低、集中管理复杂的一系列问题。

# 2. 基本概念

密码机：提供密码基础运算功能的服务器硬件产品或云密码机创建的虚拟密码机。设备主密钥：虚拟服务器密码机的密钥产生时由设备内部的主密钥进行加密后产生，同一个租户内的虚拟机的主密钥需保持一致才可以保持同一个密钥在租户内所有设备上进行正常的密码运算。没有主密钥的设备，不允许分配给租户和进行业务运算。

# 3. 功能模块介绍

## 3.1.平台大屏

平台大屏对密码设备、密码服务、应用、密评、密码监控信息、告警信息等进行了总体展示于统计

![](images/da291914db0f67864c86d0c0ae9e40232a23befa3e37d1b3a29f71ace76300c1.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张密码服务平台监控大屏的截图，显示了多个监控和统计信息。以下是图片内容的详细描述：

### 顶部信息
- **时间**：15:46:59
- **日期**：2025年6月23日，星期一

### 左侧区域
#### 密码设备统计
- **云密码机**：1台
- **物理密码机**：0台
- **虚拟密码机**：2台
- **密码设备状态**：
  - 运行中：100%，共3台
  - 未运行：0%
  - 异常：0%

#### 应用调用次数排行
- **Top 5**：
  - tsa：23099050次
  - sms：4704071次
  - tor：1784179次

#### 密码设备监控
- **vsm_10_20_17_134_443**
  - CPU：5%
  - 内存：34%
  - 磁盘：8%
- **vsm_10_20_17_133_443**
  - CPU：5%
  - 内存：34%
  - 磁盘：8%

### 中央区域
- **用户**：1个
- **应用**：4个
- **服务**：5个
- **设备**：3个
- 显示了多个立方体图标，代表不同的密码设备和服务。

### 右侧区域
#### 密码服务统计
- **数字证书认证**：0次
- **统一身份认证**：0次
- **文件加密服务**：0次
- **加解密服务**：0次
- **密码服务状态**：
  - 运行中：100%，共5个
  - 初始化中：0%
  - 运行异常：0%

#### 密评进度
- **项目准备**：0%
- **方案编制**：0%
- **改造实施**：0%
- **密码评估**：0%

#### 告警信息
- **CPU使用率**：数据库节点10.0.101.147的CPU使用率达到72.22%

### 底部区域
- **密码服务调用次数**：
  - 显示了实时、近24小时和近30天的调用次数统计图表。
  - 当前选择的是“加解密服务”。

这张监控大屏提供了全面的密码服务平台的运行状态、设备统计、应用调用情况以及告警信息，帮助管理员实时监控和管理密码服务。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.1.1. 大屏中间区域-平台详情

大屏中间区域展示了总体的平台数据详情包括：应用数量、服务类型以及数量、设备类以

及数量信息

![](images/8688a8b1053e551ef2ef24eecc707e78e94cd45fd41cf32d3ce7ae512329714e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个密码服务平台的监控大屏，整体设计科技感十足，主要以蓝色和黑色为主色调。图片顶部有标题“密码服务平台监控大屏”，下面分为三个主要部分：

1. **应用（7）**：左上角显示有7个应用图标，代表平台支持的应用数量。
2. **服务（14）**：中间上方显示有14个服务图标，代表平台提供的服务种类。
3. **设备（5）**：右上角显示有5个设备图标，代表平台连接的设备数量。

图片中部和下部展示了具体的设备和服务模块，每个模块都有相应的图标和文字说明：

- **物理机（0）**：左下角显示有0台物理机。
- **云密码机（1）**：中间左侧显示有1台云密码机。
- **虚拟机（4）**：右下角显示有4台虚拟机。
- **签名验签（1）**：中间上方显示有1个签名验签服务。
- **数据加解密（2）**：中间显示有2个数据加解密服务。
- **密钥管理（1）**：中间右侧显示有1个密钥管理服务。
- **其它服务（10）**：右上角显示有10个其它服务。

这些模块通过线条连接，表示它们之间的关系和数据流动。整体布局清晰，信息一目了然，适合用于监控和管理密码服务平台的各项指标和状态。
```


### 3.1.2.密码设备统计

密码设备统计，展示了云密码机、物理密码机、虚拟密码机的数量，以及密码设备整体的一个状态分布情况：

![](images/f07fd0050e7677a8dbfc6d121c1b1a7e32f16f501a4cb754d4d5950122035630.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个密码设备统计界面，主要内容包括：

1. **密码设备统计**：
   - 云密码机：数量为1。
   - 物理密码机：数量为0。
   - 虚拟密码机：数量为2。

2. **密码设备状态**：
   - 运行中：占比100%，数量为3。
   - 未运行：占比0%，数量为0。
   - 异常：占比0%，数量为0。

界面设计简洁，使用了蓝色、黄色和绿色来区分不同的密码设备类型，并用圆形图表和文字说明来展示设备的运行状态。
```


点击详情可进入二级页面：

![](images/4a7b062b9710eb919f644d30ad5901c515fed29e7ca915c157f1af40533e4349.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张密码服务平台监控大屏的截图，显示了多个监控模块和统计信息。以下是各部分的详细描述：

### 1. 设备组统计
- **专享设备组**：数量为1。
- **共享设备组**：数量为0。

### 2. 密码设备状态
- 显示了一个圆形图表，表示设备的运行状态：
  - 运行中：100%，3台设备。
  - 未运行：0%。
  - 异常：0%。

### 3. 密码设备统计
- **云密码机**：数量为1。
- **物理密码机**：数量为0。
- **虚拟密码机**：数量为2。

### 4. 租户使用设备数量排行
- 显示了TOP1租户的信息：
  - 租户名称：测试用户1。
  - 使用设备数量：2。

### 5. 告警信息
- 显示了一条告警信息：
  - 内容：CPU使用率，数据库节点10.0.101.147的CPU使用率达到72.22%。
  - 时间：2025-06-23 15:44:58。

### 6. 数据库监控
- 显示了两个opengauss数据库单节点的信息：
  - 状态：正常。
  - 会话数：0。
  - 主备状态：单节点。
  - 上次备份时间：2025-06-23 15:45:02。
  - CPU使用率：69.5%。
  - 内存使用率：68.16%。
  - 磁盘使用率：61.82%。

### 7. 物理设备监控
- 此部分为空白，没有显示任何信息。

### 8. 密码设备监控
- 显示了三个设备的监控信息：
  - vsm_10_20_17_133_443：
    - CPU使用率：5%。
    - 内存使用率：34%。
    - 磁盘使用率：8%。
  - vsm_10_20_17_134_443：
    - CPU使用率：5%。
    - 内存使用率：34%。
    - 磁盘使用率：8%。
  - vsm_10_20_17_133_443（重复）：
    - CPU使用率：5%。
    - 内存使用率：34%。
    - 磁盘使用率：8%。

### 其他信息
- 页面顶部显示了当前时间为15:49:53，日期为2025年6月23日星期一。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.1.3.密码服务统计

展示各类服务的数量以及运行状态：

![](images/6da9a11a6ba6652817b65b2e1d4c8fadb1db7f64ea96ca4ec8b1010b09946257.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个密码服务统计界面，背景为深蓝色，带有网格图案。界面顶部有一个标题“密码服务统计”，右上角有一个“详情”按钮。

在标题下方，有四个圆形图标，分别代表不同的服务：
1. 统一身份认证服务
2. 文件加密服务
3. 加解密服务
4. 签名验签服务

每个服务图标下方都有一个数字“0”，表示当前这些服务的使用次数或状态为零。

在这些图标下方，有一个标题“密码服务状态”，旁边有一个蓝色的圆形图标，图标内有一个堆叠的文件夹图案。

在“密码服务状态”标题下方，有一个表格，显示了密码服务的状态信息：
- 运行中：100%，数量为5
- 初始化中：0%，数量为0
- 运行异常：0%，数量为0

表格左侧有一个蓝色的圆环，可能用于视觉装饰或表示某种进度。
```


密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/fcb7c5a5c605e6669a74d90a2317b280c587d07dcbb765bfe9911448b040c723.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张密码服务平台监控大屏的截图，显示了多个监控和统计信息。以下是详细描述：

### 顶部信息
- **时间**：15:49:02
- **日期**：2025年6月23日 星期一

### 左侧区域
#### 服务组统计
- **专享服务组**：1个
- **共享服务组**：0个

#### 告警信息
- **CPU使用率**：数据库节点[10.0.101.147]cpu使用率达到72.22%

### 中间区域
#### 密码服务统计
- **签名验签服务**：0次
- **密码管理服务**：0次
- **时间戳服务**：1次
- **协同签名服务**：1次
- **动态令牌服务**：1次
- **数据库加密...**：1次
- **电子签章服务**：1次
- **SSLVPN加...**：0次
- **数字证书认...**：0次

#### 密码服务详情
- **密码管理服务**
  - 预激活：0个
  - 激活：0个
  - 注册：0个
  - 销毁：0个
- **时间戳服务**
  - 总调用次数：23099050次

### 右侧区域
#### 密码服务状态
- **运行中**：100%，5个
- **初始化中**：0%
- **运行异常**：0%

#### API网关监控
- **认证中心**、**etcd**、**redis**、**监控服务**的资源使用情况：
  - CPU：69.5%
  - 内存：68.16%
  - 磁盘：61.82%
- **API网关**（IP：************）
  - 网关类型：管理网关
  - CPU：3.03%
  - 内存：35.8%
  - 磁盘：33.92%
- **API网关**（IP：************）
  - 网关类型：管理网关
  - CPU：2.28%
  - 内存：34.31%
  - 磁盘：28.87%

#### 平台管理节点监控
- **管理节点-1**（IP：************）
  - CPU：3.03%
  - 内存：35.8%
  - 磁盘：33.92%
- **管理节点-2**（IP：************）
  - CPU：2.28%
  - 内存：34.31%
  - 磁盘：28.87%

#### 密码服务监控
- **tsa**
  - CPU：1%
  - 内存：49%
  - 磁盘：11%
- **tsc**
  - CPU：23%
  - 内存：42%
  - 磁盘：14%
- **sms**
  - CPU：1%
  - 内存：42%
  - 磁盘：14%
- **secab**
  - CPU：0%
  - 内存：37%
  - 磁盘：11%
- **otp**
  - CPU：1%
  - 内存：52%
  - 磁盘：14%
- **tsa**
  - CPU：1%
  - 内存：49%
  - 磁盘：11%

这张监控大屏提供了详细的密码服务统计、服务状态、资源使用情况等信息，帮助管理员实时监控和管理密码服务平台的运行状态。
```
  
点击详情可进入二级页面：

### 3.1.4. 应用调用次数统计

展示了应用次数top5，并且可点击切换到Last5：

![](images/4ac759df87107fbf7ae9730a79e321d3576f1eae39d72933307d42aa183be783.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个应用调用次数的排行榜界面。界面上方有一个蓝色的标题栏，写着“应用调用次数排行”，右侧有一个“更多”的链接。

在标题栏下方有两个选项按钮：“Top 5”和“Last 5”，当前选中的是“Top 5”。

排行榜列出了三个应用及其调用次数：
1. 第一个应用名为“test1”，调用次数为49031次。
2. 第二个应用名为“ca”，调用次数为1272次。
3. 第三个应用名为“ter”，调用次数为622次。

每个应用旁边都有一个图标，图标是一个堆叠的形状，颜色分别为绿色、蓝色和浅蓝色。
```


点击更多可展示应用详情信息:

![](images/35fcfcf7829a0b318485c863d1f362b819bd3f396f8e2c791fa320f92cfcfee4.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个应用调用次数排行列表的界面，背景为深蓝色，具有科技感的设计风格。列表中包含了五个不同的应用及其调用次数、应用标识、应用名称、业务类型和描述信息。

1. 第一行数据：应用调用次数为49031次，应用标识和应用名称均为“test1”，业务类型包括数据加解密、签名验签，描述为“swxa1234。”。
2. 第二行数据：应用调用次数为1272次，应用标识和应用名称均为“ca”，业务类型包括数字证书认证、动态令牌。
3. 第三行数据：应用调用次数为622次，应用标识和应用名称均为“tsc”，业务类型为电子签章。
4. 第四行数据：应用调用次数为493次，应用标识和应用名称均为“sms”，业务类型为协同签名。
5. 第五行数据：应用调用次数为387次，应用标识和应用名称均为“tsa”，业务类型包括时间戳、数据加解密。

此外，在界面的右下角有一个分页导航栏，显示当前页面为第1页，共5条记录，每页显示20条。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.1.5.密评进度

展示密评相关的进度信息：

![](images/b1a206a10550709a1940b2344260c56b01759074eaed72492ff04f2ff9ab6352.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“密评进度”的界面，背景为深蓝色网格图案。界面顶部有一个蓝色的标题栏，标题为“密评进度”，右侧有一个“详情”按钮。

在界面的左侧，有一个环形图，分为四个部分，分别用不同的颜色表示：
- 蓝色
- 绿色
- 黄色
- 红色

在界面的右侧，有一个列表，列出了四个项目及其对应的进度和数值：
1. 项目准备：0%，0
2. 方案编制：0%，0
3. 改造实施：0%，0
4. 密码评测：0%，0

每个项目的名称旁边都有一个对应的颜色方块，与环形图中的颜色相对应。目前所有项目的进度都显示为0%。
```


点击详情可进入二级页面：

![](images/4e20c3aa482328e47552b56b906c5e973caa21d11fe0bf56f4af57834d3cd0b9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张密码服务平台监控大屏的截图，显示了多个与密码应用相关的统计和监控信息。以下是图片内容的详细描述：

### 顶部信息
- **时间**：15:51:04
- **日期**：2025年6月23日，星期一

### 主标题
- **密码服务平台监控大屏**

### 左上角导航栏
- **密码应用**

### 主要内容区域
#### 第一行
1. **等保等级应用分布**
   - 显示了不同等级的应用分布情况，目前所有等级的应用数量均为0。
   
2. **密码应用活跃度**
   - 显示了密码应用的活跃度情况，长期使用占比100%，数量为4；中断使用和尚未使用的占比均为0%。

3. **密评通过率**
   - 显示了密评通过率的情况，已通过和未通过的数量均为0。

4. **密评进度**
   - 显示了密评进度的情况，项目准备、方案编制、改造实施、密码测评的进度均为0%。

#### 第二行
1. **应用业务排行**
   - 列出了TOP7的应用业务及其数量：
     - 时间戳服务：1
     - 电子签章服务：1
     - 协同签名服务：1
     - 动态令牌服务：1
     - 密钥管理服务：0
     - 签名验签服务：0
     - SSLVPN加密通道服务：0

2. **密钥统计**
   - 显示了不同类型的密钥数量：
     - 3DES：0
     - AES：0
     - RSA：0
     - HMAC：0
     - SM1：0
     - SM2：0
     - SM3：0
     - SM4：0
   - 下方有一个柱状图，显示了不同密钥类型的激活、预激活、销毁、注销情况。

3. **租户包含应用数量排行**
   - 显示了TOP1的租户及其包含的应用数量：
     - 测试用户1：4

### 右上角图标
- 有一些功能图标，可能用于页面操作或设置。

这张监控大屏提供了全面的密码应用监控和统计信息，帮助管理员了解当前系统的运行状态和各项指标。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.1.6.密码设备监控

展示各机器的cpu、内存、磁盘占用信息：

![](images/5c24e2e2e795512bdcfa8cce471114aeca862bd0ef92170426807147c4f6edaf.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个密码设备监控界面，背景为深蓝色，带有网格图案。界面上方有一个标题“密码设备监控”，右侧有一个“更多”按钮。

界面中列出了三个设备的状态信息：

1. **vsm_10_20_17_114_443**
   - CPU使用率：6%
   - 内存使用率：34%
   - 磁盘使用率：8%

2. **vsm_10_20_17_115_443**
   - CPU使用率：0%
   - 内存使用率：0%
   - 磁盘使用率：0%

3. **vsm_10_20_17_112_443**
   - CPU使用率：6%
   - 内存使用率：34%
   - 磁盘使用率：8%

每个设备的状态信息旁边都有一个图标，前两个设备的图标是黄色的文件夹，最后一个设备的图标是红色的文件夹。每个设备的状态信息下方都有一个进度条，显示了CPU、内存和磁盘的使用情况。
```


点击更多，可展示近一小时各指标曲线：

![](images/3bcfe9a56ee1848725a15a4a90b4c7677f1da684fca5045d03e22dca4050c746.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个监控信息界面，背景为深蓝色，界面上有四个并排的矩形框，每个框内包含三个图表，分别代表CPU、内存和磁盘的使用情况。每个矩形框的左上角标有“vsm_10_20_17_XXX_443”的标识，其中XXX部分在不同框中分别为112、113、114和115，这可能表示不同的服务器或服务实例。

- CPU图表：显示了近一小时内CPU的使用率，图表中的线条颜色为绿色，目前的使用率为0%。
- 内存图表：显示了近一小时内内存的使用情况，图表中的线条颜色为蓝色，目前的使用率为0%。
- 磁盘图表：显示了近一小时内磁盘的使用情况，图表中的线条颜色为黄色，目前的使用率为0%。

每个矩形框的右上角都有一个“近一小时”的标签，表明这些图表显示的是最近一个小时的数据。界面底部有一个分页导航条，显示当前页面为第1页，总共有4条记录，每页显示20条。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.1.7.告警信息

展示平台告警信息，包括告警级别、告警时间、告警详情：

![](images/7970b11902761761714f029472c173387377de66f0aa98e23a306bdbbfabc5b7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个告警信息界面，背景为深蓝色，带有网格图案。界面上方有一个标题“告警信息”，右侧有一个“更多”按钮。

界面中列出了两条告警信息，每条告警信息包含以下内容：

1. **告警级别**：红色圆角矩形框内显示“1级”，表示这是第一级别的告警。
2. **告警类型**：旁边的文字显示“平台管理状态”。
3. **告警描述**：详细描述了告警的具体情况，内容为“服务器[************]平台管理模块[聚合服务]状态异常”。
4. **告警时间**：右侧显示了告警发生的时间，两条告警的时间都是“2025-06-20 11:24:56”。

这两条告警信息完全相同，表明在指定时间点，同一服务器的平台管理模块出现了状态异常的情况。
```


点击更多可查看告警详情：

![](images/9cb323bb654076da907d0e5fce4f3f94b78da004892e0dfba7fd0eff4432ff2e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个告警信息列表的界面，背景为深蓝色，整体风格科技感十足。界面顶部有“告警信息列表”的标题。表格中列出了告警等级、告警类型、告警信息、告警源类型、告警源IP、告警次数、告警状态、首次告警时间和末次告警时间等信息。

具体来看，当前只有一条告警信息，其告警等级为1级，告警类型为平台管理状态，告警信息是服务器[************]平台节点，告警源类型为平台节点，告警源IP为************.pt，告警次数为4238次，告警状态为告警中，首次告警时间为2025-06-05 18:10:11，末次告警时间为2025-06-20 11:24:56。

此外，界面底部还显示了当前页面的记录数和分页信息，当前页面共有0条记录，每页显示20条，当前在第1页，共1页。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

## 3.2.平台首页

平台首页对密码服务平台用到的设备、服务、创建的租户、创建的应用、服务调用次数以及其他各项做了总体展示与统计。

![](images/864ccb7ec3c7b492e77f174ee3382ef2c5ac2274f0043c8f4df345a061611cac.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示平台运行状态和资源统计的仪表板截图。以下是对图片内容的详细描述：

1. **平台连续运行时间**：
   - 显示平台已经连续运行了7天6小时16分钟。

2. **平台资源**：
   - 租户数量：0个
   - 服务数量：0个
   - 设备数量：0个
   - 应用数量：0个

3. **密评通过率**：
   - 显示一个饼图，表示密评通过率。图中有两种颜色：绿色（已通过）和蓝色（未通过）。具体比例没有给出数值。

4. **密码服务调用统计**：
   - 显示一个折线图，表示加解密服务的调用次数。图中有一个时间轴，从14:53到15:21，但没有具体的调用次数数据。

5. **工单状态**：
   - 显示一个饼图，表示工单的状态。图中有四种颜色：绿色（已处理）、蓝色（已撤销）、黄色（已退回）、红色（处理中）。具体比例没有给出数值。

6. **服务详情**：
   - 加解密服务：显示密钥数量和总调用次数，但具体数值为空。
   - 电子签章服务：显示总签章次数、签章数量和总验签次数，但具体数值为空。
   - SSLVPN加密通道服务：显示并发连接数和新建连接数，但具体数值为空。
   - 数字证书认证服务：显示用户证书数量，但具体数值为空。

7. **服务运行状态**：
   - 显示一个饼图，表示服务运行状态，但具体比例没有给出数值。

8. **应用调用次数排行**：
   - 显示一个条形图，表示应用调用次数排行，但具体数值为空。

9. **告警信息**：
   - 显示一个链接“查看全部”，但没有具体的告警信息。

10. **设备监控**：
    - 没有具体的监控信息显示。

总体来看，这张仪表板提供了平台运行的各种统计数据和状态信息，但由于大部分数据为空，无法提供详细的分析结果。
```
  
图 3-0-1 平台首页

### 3.2.1. 平台资源统计

展示平台连续运行时间，以及全平台中的设备数量（独享密码设备）、服务调用次数、服务数量。

图 3-0-2 平台资源统计

### 3.2.2. 密码服务统计

平台首页中，服务统计包括三部分信息：服务调用次数统计、服务详情统计、服务运行状态统计。

#### 3.2.2.1. 服务调用次数统计

服务调用次数统计支持展示指定类型的服务，在指定时间段的调用次数曲线。其中，界面右上角支持选择要展示的服务类型，可以选择的时间范围有：实时（近30 分钟）、近24 小时、近30 天。

![](images/7078d7178c3100998fae6374269c881fec2ecbcafe240a3fce1fcbe7f4684538.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“密码服务调用统计”的图表，具体是关于加解密服务的使用情况。图表的时间范围被设定为近30天，从8月12日到9月9日。

在图表中，我们可以看到一个折线图，它表示了在这段时间内加解密服务被调用的次数。横轴代表日期，纵轴代表调用次数。从图表上可以看出，在大部分时间里，加解密服务的调用次数都接近于零，只有在9月3日和9月7日出现了两次明显的高峰，其中9月7日的调用次数达到了最高点，接近10次。这可能意味着在这两天，系统对加解密服务的需求突然增加。

此外，图表右上角有一个下拉菜单，当前选择的是“加解密服务”，这意味着我们正在查看的是与加解密服务相关的数据。同时，还有三个选项卡，分别是“实时”、“近24小时”和“近30天”，当前选中的选项卡是“近30天”，这表明我们正在查看的是过去30天的数据。
```
  
图 3-0-2 服务调用次数统计

#### 3.2.2.2. 服务详情统计

服务详情统计支持同时展示所有类型服务的累计使用详情，例如加解密服务支持展示全平台密钥数量、总调用次数，密钥管理服务支持展示全平台密钥数量、各状态密钥数量等。

![](images/640d7d2fc0bbe173e7d307535d9ef8509e45eb7b00371edd67eb33a24b15b988.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个服务详情的界面，包含了四个主要的服务模块及其相关数据：

1. **加解密服务**：
   - 密钥数量：26
   - 总调用次数：15

2. **密钥管理服务**：
   - 密钥数量：8
   - 预激活：0
   - 注册：0
   - 激活：8
   - 销毁：0

3. **协同签名服务**：
   - 用户数量：0
   - 总调用次数：-

4. **数据库加密服务**：
   - 密钥数量：-
   - 加密数据库：0
   - 加密表：0

每个服务模块都有一个图标和相应的数据统计，帮助用户快速了解各个服务的使用情况。
```
  
图 3-0-3 服务调用次数统计

#### 3.2.2.3. 服务运行状态统计

服务运行状态，展示全平台所有服务中，处于运行中、初始化中、异常状态的服务数量，鼠标移入某个状态后，会展示当前状态的服务数量

服务运行状态

![](images/17fcf605b9dff9b9b89e11ce8b5bcc45a070d5b676fb29deb66c74816da7ea1a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张展示服务状态的图表。图表主要由一个环形图和一个矩形信息框组成。

1. **环形图**：
   - 环形图分为三个部分，分别用不同的颜色表示不同的服务状态。
   - 大部分区域是浅绿色（运行中），表示有10个服务正在运行。
   - 一小部分区域是黄色（异常），表示有少量服务处于异常状态。
   - 没有蓝色（初始化中）的部分，表示没有服务处于初始化状态。

2. **矩形信息框**：
   - 位于环形图的右侧，显示了服务数量的信息。
   - 文字内容为“服务数量 运行中: 10”，表示当前有10个服务正在运行。

3. **图例**：
   - 在环形图下方有一个图例，解释了不同颜色代表的服务状态：
     - 浅绿色：运行中
     - 蓝色：初始化中
     - 黄色：异常

这张图表清晰地展示了当前服务的状态分布，帮助用户快速了解各个服务的运行情况。
```
  
图 3-0-4 服务运行状态统计

### 3.2.3. 密码设备统计

平台首页中，密码统计包括两部分信息：设备运行状态、设备监控。

#### 3.2.3.1. 设备运行状态

设备运行状态，展示全平台所有设备中处于运行中、未运行、异常状态的设备数量，鼠标移入某个状态后，会显示当前状态的设备数量。

1设备运行状态

![](images/eefc99b7da300d023481b6e20a061787836bfb6bd0c8af9bc5a3e1127688b2c8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个圆形图表，分为三个部分，每部分用不同的颜色表示。图表的下方有颜色对应的标签说明：

- 绿色（运行中）：占图表的上半部分。
- 蓝色（未运行）：占图表的下半部分左侧。
- 黄色（异常）：占图表的下半部分右侧。

这个图表可能用于表示某种状态或进程的分布情况，例如系统运行状态、任务执行情况等。绿色部分表示“运行中”的状态，蓝色部分表示“未运行”的状态，黄色部分表示“异常”的状态。通过观察各部分的比例，可以了解不同状态的占比情况。
```


#### 3.2.3.2. 设备监控

设备监控，展示最近采集的 6 台设备的实时运行状态、资源使用情况，包括 CPU、内存、磁盘的使用率。

设备监控   
vpn-1   
CPU 内存 磁盘   
vsm_10_20_18_150_443 .   
CPU 内存 磁盘   
vsm_10_20_18_151_443 .   
CPU 内存 磁盘 三   
vsm _10 _20_18 152_443 .   
CPU 内存 磁盘 三   
vsm_10_20_18_157_443 .   
CPU 内存 磁盘 三   
vsm_10_20_18_156_443 .   
CPU 内存 磁盘 /!

### 3.2.4. 密评统计

平台首页中，密评通过率是指在所有密评对象中，已通过密评和未通过密评的对象数量、比例。

![](images/005faa1b9822b6818a0aea9dc466806a1f999538730262d1907c160457725260.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张展示密评通过率的图表。图表是一个环形图，分为两部分：蓝色部分表示未通过的比率，绿色部分表示已通过的比率。从图中可以看出，蓝色部分占据了大部分，表明未通过的比率较高；绿色部分较小，表明已通过的比率较低。图表下方有颜色说明，绿色代表“已通过”，蓝色代表“未通过”。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.2.5. 工单统计

平台首页中，工单状态是指在所有工单中，处于已处理、已撤销、已退回、处理中状态的工单的数量、比例。

![](images/eb243fa15b64e993d8ec5b7802415777b845e8fa121fbf219fe93a2ae54e737d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个关于工单状态的饼图。饼图被分为四个部分，每部分用不同的颜色表示不同的工单状态：

1. 绿色部分表示“已处理”的工单。
2. 蓝色部分表示“已撤销”的工单。
3. 黄色部分表示“已退回”的工单。
4. 红色部分表示“处理中”的工单。

从饼图的比例来看，“已处理”的工单占了较大比例，其次是“已撤销”和“已退回”的工单，而“处理中”的工单占的比例相对较小。这个饼图可以帮助我们直观地了解当前工单的状态分布情况。
```
  
图 3-0-6 密评通过率  
图 3-0-7 工单状态统计

### 3.2.6. 告警统计

告警信息，指的是平台对自身各服务模块、依赖组件、数据库、密码设备、服务器等部件进行监控指标采集，然后基于指标设置不同的告警阈值，当达到阈值后就会触发该部件的指标告警，并通过弹窗、记录形式告知用户。  
平台首页中，告警信息展示最近的13 条报警记录，点击右上角的查看全部，跳转到监控统计-告警信息页面，展示完整告警内容。  
1告警信息 查看全部 >磁盘使用率  
数据库节点[10.20.37.177]磁盘使用率达到74.01%CPU使用率  
数据库节点[10.20.37.178]cpu使用率达到85.91%内存使用率  
数据库节点[10.20.37.178]内存使用率达到93.41%磁盘使用率  
数据库节点[10.20.37.178]磁盘使用率达到80.59%CPU使用率  
数据库节点[10.20.37.178]cpu使用率达到97.99%磁盘使用率  
数据库节点[10.20.37.178]磁盘使用率达到80.59%3级磁盘使用率  
数据库节点[10.20.37.177]磁盘使用率达到74.01%内存使用率  
数据库节点[10.20.37.178]内存使用率达到93.52%

### 3.2.7. 应用调用次数

应用调用次数展示的是该应用下绑定的服务的服务次数统计，次数按照从多到少排行。

![](images/b220d60c8ffcaf68217c2a1669b36abdc54849b752786b3f32330e5091ab682e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个应用调用次数的排行界面。在图片的顶部，有一个标题“应用调用次数排行”，下面是一个进度条，进度条的左侧标有数字“1”，右侧标有“1次”。进度条的颜色为蓝色，表示当前的应用调用次数为1次。整个界面简洁明了，主要用来展示某个应用的调用频率。
```
  
图 3-0-8 告警统计  
图 3-0-9 应用调用次数排行

## 3.3. 许可授权管理

许可授权管理主要负责平台和密码服务使用所需许可证的申请、导入和续签功能。平台的操作功能需平台具有平台使用许可才可使用。平台在添加密码服务时，也需要平台具有对应数量的业务实例许可才可添加使用。所以平台初始化完成后，应先申请、导入所需的平台许可和业务实例许可。许可申请导入流程可参考下图：

![](images/0f149505780d1c3ec3f5ddf77c7b263de5cf048d71f2ab96abf022742d0937f8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张系统操作员与三未客服人员之间的交互流程图，主要展示了密码服务管理平台在其中的作用。流程如下：

1. 系统操作员向密码服务管理平台申请许可证。
2. 密码服务管理平台向系统操作员返回申请凭证文件。
3. 系统操作员将申请凭证文件发送给三未客服人员。
4. 三未客服人员向系统操作员返回授权许可证。
5. 系统操作员将授权许可证导入密码服务管理平台。
6. 密码服务管理平台向系统操作员确认导入成功。

这个流程图清晰地展示了系统操作员、密码服务管理平台和三未客服人员之间的交互步骤，确保了密码服务的顺利进行。
```
  
  
图3-1-1 许可申请导入流程图

### 3.3.1. 许可申请管理

选择许可授权管理下许可申请菜单，打开许可申请页面，页面展示所有申请许可的历史记录，可通过许可证类型、状态、申请时间进行过滤。

![](images/51559808ef37b311a9229222606b8beb844be0cfe21fa341f4bf1fc76dd0190d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示许可证申请状态的网页截图。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.3.1.1. 申请许可

申请许可时，可点击页面上的“申请”按钮，弹出申请许可证弹出框，选择要申请的许可证类型、周期类型和填写许可证数量和业务描述后，点击“确定”按钮，即可获取的申请的凭证，将凭证发给三未客服人员，由客服人员根据申请凭证信息颁发对应的许可证文件。

![](images/e5fc210cffe8fb85010e07ad610e0f6c53bbe5d5f0267b6adde606b2fe5defb7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示许可证申请界面的图片。界面上有四个主要部分：

1. **许可证类型**：有两个选项，分别是“业务实例许可”和“平台许可”。当前选中的是“业务实例许可”。

2. **周期类型**：有两个选项，分别是“按年授权”和另一个未显示的选项。当前选中的是“按年授权”。

3. **许可证数量**：有一个输入框，提示用户“请输入许可证数量”。

4. **业务描述**：有一个文本框，提示用户“请输入业务描述”，并且显示当前已输入字符数为0/100。

在界面底部有两个按钮，分别是“取消”和“确定”。用户可以通过这些选项和输入框来填写和提交许可证申请的相关信息。
```
  
图3-1-2 许可申请管理界面  
图3-1-3 申请许可界面

#### 3.3.1.2. 下载凭证

在许可申请列表页面可重复下载状态为“申请中”申请记录凭证，以防止申请许可凭证文件丢失后，而导致无法申请许可的场景。选择状态为“申请中”许可申请记录，点击操作列中“下载凭证”按钮，下载许可申请凭证文件。

![](images/4fe965c74f800c5491cf88e1c635883a82847fae2f40c93d3ef319d1327b7b74.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个许可证管理系统的界面，主要用于管理和查看不同类型的许可证申请。界面上方有筛选条件，包括许可证类型、周期类型和状态等选项，用户可以通过这些选项来筛选特定的许可证记录。

在界面的主要部分，有一个表格列出了多条许可证记录，每条记录包含了序号、许可证类型、周期类型、服务数量、备注、申请人、申请时间、状态和操作等信息。例如，第一条记录显示的是业务服务类型的永久授权，服务数量为11，申请人为oper，申请时间为2023年5月17日16:52:45，当前状态为申请中，并且有一个“下载凭证”的操作按钮。

此外，在图片的中央位置，有一个弹出的提示框，内容是“确认要下载许可证申请凭证吗？”，这表明用户正在尝试下载某条记录的许可证凭证，并需要进行确认操作。

整体来看，这个界面设计简洁明了，方便用户管理和操作许可证相关的事务。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.3.2. 许可管理

选择许可管理菜单，显示已有的许可证信息，可通过许可证名称、许可证类型、状态进行过滤。

![](images/59e666bfc10abba188a2b7179e82ddcadd2139b20f41f51de34c2c3a3067864d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示许可证管理界面的截图，界面上方有“首页”和“许可证管理”的标签。在许可证管理页面中，有一个表格列出了多个许可证的信息，包括序号、许可证名称、许可证类型、周期类型、起止时间、创建时间、创建人、更新时间和状态。表格中有10条记录，其中一些许可证的状态为“已使用”，而其他一些则为“未使用”。此外，页面顶部还有搜索框和筛选选项，允许用户根据许可证名称、类型和状态进行查询。右上角有“查询”和“重置”按钮，底部显示了当前页码和总页数。
```
  
图3-1-4 下载许可申请凭证界面  
图3-1-5 许可管理功能界面

#### 3.3.2.1. 许可导入

获得三未下发的许可证文件后，在许可管理页面，点击“导入”按钮，弹出“导入授权文件”弹出框。

![](images/70a07b6ed54e876369272fb38663872776b173235ddb1251379f20b7ae1911e3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个软件界面，具体来说是一个许可证管理系统的界面。界面上方有一个标题栏，显示了“首页”和“许可管理”的标签。在标题栏下方，有一个搜索框，用户可以输入许可证名称进行搜索。搜索框右侧有两个按钮，分别是“查询”和“重置”。

在界面的中间部分，有一个弹出窗口，标题为“导入授权文件”。弹出窗口内有一个提示信息，告诉用户可以将文件拖到此处或点击上传按钮来上传文件。提示信息下方有一个“取消”按钮和一个“确定”按钮。

在弹出窗口的左侧，有一个表格，列出了多个许可证的信息。表格的列标题包括序号、许可证名称、许可证类型、创建人、更新时间、更新人和状态。表格中列出了13个许可证的信息，每个许可证都有一个唯一的许可证名称和类型，以及创建人、更新时间和状态等信息。

在弹出窗口的右侧，有一个红色的“导入”按钮，用户可以点击该按钮来导入授权文件。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

上传平台许可，解析文件后，会展示许可证相关内容信息，确认无误后，点击“确定”按钮，即可在许可证列表中查看到新导入的许可证信息。  
导入平台许可成功后，平台会显示相关功能模块菜单。上传业务实例许可，解析文件后，会展示许可证相关内容信息，确认无误后，点击“确定”按钮，即可在许可证列表中查看到新导入的许可证信息。  
导入业务实例许可成功后，可以添加相关服务信息。

![](images/4c6e0ab2b8b4d50fa56bf0af051675229c1dc7d26e07727a4b78d4cd9f78d6e3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示导入授权文件界面的截图。界面上有一个提示框，指示用户将文件拖到此处或点击上传。提示框内有一个云状图标，表示上传功能。下方有一行小字提示，提醒用户请选择授权文件，并且不能超过2MB。

在提示框下方，显示了一个文件名，文件名为“1e6be47d37ea4d38b392b4ca23b22971.lice...”。

再往下是许可证信息部分，详细列出了以下内容：
- 许可证类型：平台许可
- 周期类型：永久授权
- 许可证数量：1
- 颁发时间：2024-09-14 10:14:39
- 过期时间：2034-09-12

在界面底部有两个按钮，分别是“取消”和“确定”，供用户选择是否确认导入授权文件的操作。
```
  
图3-1-6 许可导入界面  
图3-1-7 平台许可授权文件解析界面

![](images/425e1e9a18262522b469b580d9e8207e85b5ef8f041fb9d88b2b11798c14c27a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示导入授权文件界面的截图。界面上有一个蓝色虚线框，内有云朵和向上的箭头图标，提示用户可以将文件拖到此处或点击上传。下方有一行小字提示，请选择授权文件且不能超过2MB。在虚线框下方，显示了一个名为“license-5710343387647839874.tmp”的文件。再往下是许可证信息，包括许可证类型为业务实例许可，周期类型为按年授权，许可证数量为50，颁发时间为2023年5月24日16:39:29，过期时间为2023年9月14日。最后，界面底部有两个按钮，分别是“取消”和“确定”。
```
  
图3-1-8 业务实例许可授权文件解析界面

### 3.3.3. 服务续约管理

平台初始化时，会选择授权周期类型（无需授权、永久授权或按年授权），如果是按年授权，会显示该功能，用于为密码服务提供续签能力。

选择服务续期菜单，显示当前平台包含的所有密码服务的到期时间，可根据服务名称、服务类型进行过滤。

![](images/690d97cd3ddc74204fedef2fd9cd03dea506c69311f7e153044872c8dd2c63d4.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个服务续期管理的界面，界面上方有搜索栏，可以输入服务名称、选择服务类型、输入IP地址和端口进行查询。下方是一个表格，列出了序号、服务名称、服务类型、管理IP、业务IP、管理端口、业务端口、许可证数量、服务起止时间以及操作选项。表格中列出了三项服务：电子签章、协同签名和签名验签，它们的管理IP和业务IP都是************，但端口和服务起止时间有所不同。每项服务的操作选项都显示为“续期”。
```
  
图3-1-9 服务续约管理功能界面

### *******. 服务续约

在服务续约列表页面，点击密码服务对象操作列中的 “续约”按钮，弹出续约提示框，点击“确认”后，会自动选择一个未使用的许可证进行续期。

![](images/3cc079cf2d7d4ab7b20e15d594209a95abeeb3d52f113cad881d48e53c315e68.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个服务续期管理的界面，界面上有一个弹出的提示框。提示框的内容是关于服务续期的确认信息，具体来说，它提醒用户续期一次将消耗1个许可证，并询问是否确认续期。提示框中有两个按钮，一个是“取消”，另一个是“确定”。在背景中，我们可以看到一个表格，列出了不同的服务名称、服务类型、管理IP、业务IP、管理端口、业务端口、许可证数量和服务起止时间等信息。表格中的第一行服务名称为“电子签章”，其服务类型也是“电子签章”，管理IP和业务IP都是“************”，管理端口是“8011”，业务端口是“-”，许可证数量是“-”，服务起止时间是“-”，操作列有一个红色的“续期”按钮。第二行和第三行分别是“协同签名”和“匿名验签”的服务信息，它们的服务类型、管理IP、业务IP、管理端口、业务端口、许可证数量和服务起止时间都有具体的数值，操作列也有红色的“续期”按钮。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

## 3.4.租户管理

## 3.4.1. 租户审核

系统操作员登录平台，打开租户管理下待审核租户菜单，查看待审核的租户信息。点击待审核租户对象操作列“审核”按钮，弹出租户审核弹出框，选择是否通过和输入审核意见。审核通过：为租户选择区域、使用的数据库、单位名称、是否支持专享服务，点击“确定”按钮，通过租户。

审核拒绝：输入不通过的审核意见。

![](images/10b62b25b989b3521e8d0b237832f25badadcca15502329fe566509f3942a44f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示待审核租户信息的网页截图。页面顶部有一个导航栏，显示了当前页面是“首页”和“待审核租户”。在导航栏下方，有一个搜索框，用户可以输入租户标识或租户名称进行查询。右侧有两个按钮，分别是“查询”和“重置”。

页面的主要部分是一个表格，列出了待审核租户的信息。表格的列标题包括序号、租户标识、租户名称、机构名称、完整性校验、备注、申请时间和操作。表格中有一条记录，显示了一个租户的信息：

- 序号：1
- 租户标识：zuhu02
- 租户名称：zuhu02
- 机构名称：zuhu02
- 完整性校验：校验通过
- 备注：无
- 申请时间：2024-05-11 13:35:49
- 操作：审核

页面底部有一个分页控件，显示当前页为第1页，共1条记录，每页显示20条。
```
  
图3-1-10 确认服务续约界面

注意：

（1）审核租户之前需要具有已经存在的数据库实例。(详见服务管理--数据库管理)（2）系统初始化时，如果选择了支持组织管理功能，单位名称才会显示，需要提前创建单  
位（详见用户管理--单位管理）（3）是否支持专享服务，如果选择是，租户即可添加专享服务，也可添加共享服务，如果  
选择否，租户只可添加共享服务

图 3-3-1 租户待审核界面

租户审核

![](images/f5a3615bd3e58838f36fa0ff1bc2571ed191f22846ac43be9fe0fdbf434c7325.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个租户信息和账号信息的界面。租户标识、名称和机构名称都是“zuhu02”，申请时间为2024年5月11日13:35:49，没有备注信息。

在账号信息部分，列出了三个角色：操作员（oper）、管理员（admin）和审计员（audit），每个角色都有对应的账号名和姓名。

审核信息部分显示了是否通过的选择，当前选择为“通过”。区域选择了“region1”，数据库选择了“区域一mysql”，单位名称为“sansec”，并且支持专享服务。审核意见也填写为“通过”。

界面底部有两个按钮：“关闭”和“确定”。
```
  
图3-3-2 租户审核界面  
图3-3-3 已审核租户列表界面

## 3.4.2. 已审核租户

系统操作员登录系统，在已审核租户界面中可以查询审核记录信息，包括租户标识、租户名称、机构名称、业务、申请时间、审核人、审核意见、完整性校验、审核状态、操作列。

Q查询号 租户标识 租户名称 机构名称 业务 申请时间 审授人 市技意见 完整性校验 审核状态 操作

点击列表操作列中的详情按钮，可以查看租户的详细信息。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/20bb80001c5552e837deb314d655aeab9840e89c0120b69030ec09fc4f67a351.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个详细的账户信息页面，包含了基本信息和账号信息两部分。

在基本信息部分，列出了租户标识、租户名称、机构名称、业务信息、申请时间和备注信息。具体来说，租户标识为test_001，租户名称为test_002，机构名称为test_003。业务信息包括数据加解密、签名验签、密钥管理、时间戳、协同签名、统一身份认证、数据库加密、文件加密、电子签章、SSLVPN加密通道等服务。申请时间为2023年7月27日9点46分32秒，备注信息为test_001。

在账号信息部分，列出了三个角色：操作员、管理员和审计员，以及他们的账号名和姓名。操作员的账号名为test01，姓名为test01；管理员的账号名为test02，姓名为test02；审计员的账号名为test03，姓名为test03。

页面底部有一个关闭按钮，用于关闭当前窗口。
```
  
图3-3-4 已审核租户详情界面

### 3.4.3. 租户管理

系统操作员登录平台，在租户管理中可查看每个租户的详细信息，可管理租户的设备组、服务组、对外业务地址、启动、停服、编辑和删除。  
打开租户管理菜单，页面展示已审核通过的所有租户信息。

![](images/3f7546a07a52e903a672bb70e554574bcc4edca3a50974caa2f99b079eb6ecd7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示租户管理界面的截图，界面上方有“首页”和“租户管理”的标签，以及许可证有效期至2034年5月11日的信息。在搜索栏中可以输入租户标识、租户名称和选择区域进行查询，右侧有“查询”和“重置”按钮。表格列出了序号、租户标识、租户名称、机构名称、区域、单位名称、业务、状态和操作等信息，当前显示了两条记录，状态均为“运行中”，并且每条记录后都有“详情”、“密码产品配额”、“业务地址”和“更多”等操作选项。
```
  
图3-3-5 租户管理列表界面

#### 3.4.3.1. 租户详情

点击租户列表中操作列详情按钮，打开租户详情页面，页面展示租户基础信息、绑定的服务组信息和设备组信息。

![](images/be35b1880bbef8af8f2b08eb4f4760d3a57c6bb73bb6a60ff02bb5cae57ae9a3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示租户管理界面的截图，具体是租户详情页面。页面顶部有导航栏，显示当前所在位置为“首页 > 租户管理”，并且有一个返回按钮。页面右上角显示了许可证的有效期为2034年5月11日，并且有操作员信息。

在页面的主要部分，有一个标题为“基础信息”的区域，列出了租户的相关信息，包括租户标识、租户名称、机构名称、关联数据库、区域、单位名称、应用数量和密钥数量等。此外，还列出了业务类型和备注信息。

在基础信息下方，有一个标题为“服务组列表”的表格，列出了服务组的相关信息，包括序号、服务组标识、服务组名称、区域、业务类型、服务数量、备注、创建时间和状态等。在这个例子中，只有一条记录，显示了一个名为“zuhu02-专享服务组”的服务组，其状态为“创建完成”。

总的来说，这张图片展示了一个租户管理系统的用户界面，提供了关于特定租户和服务组的详细信息。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.4.3.2. 租户编辑

点击租户列表操作列中的编辑按钮，打开租户编辑页面，输入租户名称、机构名称、单位名称（系统支持组织管理功能）、业务描述，点击确定按钮，编辑成功。

![](images/de2743759bcb14b188773e5e7405f58da188cdafdea46de23d3d2db377ea4e73.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“编辑租户”界面的图片，界面上有四个输入框和两个按钮。第一个输入框标有红色星号，提示为必填项，内容为“租户名称”，当前填写的是“zuhu02”。第二个输入框同样标有红色星号，提示为必填项，内容为“机构名称”，当前填写的也是“zuhu02”。第三个输入框没有红色星号，内容为“单位名称”，当前填写的是“sansec”，并且右侧有一个“X”按钮和一个下拉箭头。第四个输入框是“业务描述”，提示用户“请输入备注”，当前为空白，右下角显示已输入字符数为0/100。在界面底部有两个按钮，分别是“取消”和“确定”，其中“确定”按钮为蓝色。
```
  
图3-3-6 租户详情界面  
图3-3-7 租户编辑界面

#### 3.4.3.3. 租户业务地址

系统操作员在给租户添加密码服务后，需设置该租户在使用密码服务做业务时可连接的业务地址信息，以帮助租户更方便快捷的使用密码服务做业务。点击租户对象操作列下的“业务地址”按钮，打开业务地址页面，显示该租户的所有业务地址信息。可点击新建、编辑、删除按钮管理当前租户的业务地址信息。

图3-3-8 租户业务地址列表界面  
![](images/8c1c7534ec63f2ed21c4daad712c694c8e70ee9c768f3856da94dc62f787287e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个业务地址管理界面，包含一个表格和一些操作按钮。表格的列标题包括序号、业务地址名称、地址类型、业务类型、服务组、业务IP、业务端口、备注、创建时间和操作。表格中有一条记录，序号为1，业务地址名称为pki业务地址，地址类型为基础业务地址，业务类型包括数据加解密、签名验签、密钥管理等，服务组为pki_share，业务IP为************，业务端口为8867，创建时间为2024-05-13 09:28:58。在操作列中，有两个按钮，分别是编辑和删除。页面底部有分页信息，显示当前是第1页，共1条记录，每页显示20条。左上角有一个“新增”按钮，用于添加新的业务地址记录。
```
  
zuhu01-业务地址管理

新增业务地址时，业务地址类型可选择基础业务类型、SSLVPN 连接地址、协同签名地址、密钥管理KMIP 地址、文件加密配置地址，对应不同业务服务的请求地址。网关需下拉选择当前对外 IP 关联的网关信息。添加后在租户端对应服务的资源信息页面将展示该服务的业务地址。

![](images/6bc7a2333c756717c704d737ae403195e88cf5f1ffc0ae947ae1637179ff560b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新增业务地址”表单的图片。表单包含以下字段：

1. **业务地址名称**：这是一个必填字段，用户需要输入业务地址的名称。
2. **业务地址类型**：这是一个必填字段，用户需要从下拉菜单中选择业务地址的类型。
3. **网关**：这是一个必填字段，用户需要从下拉菜单中选择网关。
4. **IP地址**：这是一个必填字段，用户需要输入IP地址。
5. **端口**：这是一个必填字段，用户需要输入端口号。
6. **备注**：这是一个非必填字段，用户可以在此处输入备注信息，最大长度为100个字符。

在表单底部有两个按钮：“取消”和“确定”。用户可以点击“取消”按钮取消操作，或者点击“确定”按钮提交表单。
```
  
图3-3-9 业务地址新增界面(服务组数据不隔离)

服务组数据隔离时，新增业务地址，可以选择服务组信息，选择服务组信息后，网关下拉框显示的是该服务组下对应的数据，如果不选择服务组，网关下拉框显示的是默认服务组下对应的数据。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/09e20328b2e0e6ac76564c89753885c9c78cf0f6b64c0e16e61b2537ee6343a6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新增业务地址”表单的图片。表单包含以下字段：

1. **业务地址名称**：一个输入框，提示用户“请输入业务地址名称”。
2. **业务地址类型**：一个下拉菜单，当前选择的是“密钥管理KMIP地址”。
3. **服务组**：一个下拉菜单，提示用户“请选择服务组”，并且已经展开了选项，显示了“服务组2”和“服务组1”两个选项。
4. **网关**：一个输入框，但没有具体的提示信息。
5. **IP地址**：一个输入框，但没有具体的提示信息。
6. **端口**：一个输入框，提示用户“请输入端口”。
7. **备注**：一个文本区域，提示用户“请输入备注”，并且右下角显示当前输入字数为0/100。

在表单的底部有两个按钮：“取消”和“确定”。其中，“确定”按钮是蓝色的，表示这是一个主要操作按钮。
```
  
图3-3-10 业务地址新增界面(服务组数据隔离)

#### *******. 服务组管理

点击租户列表操作列中的服务组管理按钮，打开租户服务组管理页面，显示租户绑定的服务组信息。

![](images/a0141a5da19a194b8a9794b6eb4a07e972f2b95496df096356c5de31adcc3b17.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“zuhu01-租户服务组管理”的界面，列出了两个服务组的信息。第一个服务组标识为“pki_share”，名称也是“pki_share”，位于“region1”区域，业务类型为“数据加解密”，服务数量为1，创建时间为2024年5月11日17:11:58，状态为“创建完成”。第二个服务组标识为“zuhu01”，名称为“zuhu01-专享服务组”，同样位于“region1”区域，业务类型包括“动态令牌、协同签名、数据库加密”，服务数量为2，创建时间为2024年5月11日11:32:05，状态也为“创建完成”。
```
  
图3-3-11 租户服务组管理界面

#### 3.4.3.5. 设备组管理

点击租户列表操作列中的设备组管理按钮，打开租户设备组管理页面，显示租户绑定的设备组信息。

![](images/5bd05e08daf86190f3c9f099ef040ed5f99c5f3a2f0686bf4da2480cb954e68c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“zuhu01-租户设备组管理”的界面，其中包含一个表格，列出了设备组的相关信息。表格的列标题包括序号、设备组名称、业务类型、区域、支持业务调用、设备类型、设备数量、业务地址、备注、创建时间和操作。具体来说，表格中有一行数据，序号为1，设备组名称为“zuhu01-专享设备组”，业务类型包括数据加解密、文件加密和SSL等，区域为region1，支持业务调用为否，设备类型为空，设备数量为1，业务地址为空，备注为空，创建时间为2024年5月11日11点32分05秒，操作列有一个链接“解绑设备组”。此外，在表格上方还有一个绿色按钮，标有“绑定异构设备组”。
```


点击绑定异构设备组按钮，弹出界面显示租户可以绑定的所有共享异构设备组，选中设备组信息，点击确定按钮。

![](images/e9dd0eff606dc9b38685c510d1b4823ad79e26a8c26ba71af0e087ae5a3ec067.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个设备组选择界面，具体如下：

1. **标题栏**：顶部有一个标题栏，显示“请选择设备组”。

2. **表格内容**：
   - 表格有五个列标题：设备组名称、区域、设备类型、支持网络协议和创建时间。
   - 表格中有一行数据：
     - 设备组名称：加解密物理机设备组
     - 区域：region1
     - 设备类型：异构设备类型
     - 支持网络协议：HTTPS
     - 创建时间：2024-05-13 09:58:52

3. **按钮**：
   - 表格下方有两个按钮：“取消”和“确定”。

4. **背景信息**：
   - 背景中可以看到一些其他界面元素，如“租户管理”、“绑定异构设备组”等。

这个界面主要用于选择一个设备组进行操作。
```
  
图3-3-12 租户设备组管理界面

点击设备组信息列表中的解绑设备组按钮，可以解绑共享异构设备组。

![](images/3df78196bbc1d6fb8a4e6c95030ef599bd10883c58d7e3629901c6739136c2bf.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“租户设备组管理”的界面，具体是针对“zuhu01”租户的设备组管理。界面上有一个表格，列出了两个设备组的信息：

1. 第一个设备组名称为“zuhu01-专享设备组”，业务类型包括数据加解密、文件加密、SSL等，区域为region1，不支持业务调用，设备类型未指定，设备数量为1，业务地址为空，备注为空，创建时间为2024年5月11日11:32:05。

2. 第二个设备组名称为“加解密物理机设备组”，业务类型包括时间戳、协同签名、签名验证等，区域同样为region1，支持业务调用，设备类型为异构设备类型，设备数量为1，业务地址为https://************:...，备注为空，创建时间为2024年5月13日09:58:52。

在界面的右下角，有一个弹出的提示框，询问是否确认解绑设备组，提供了“取消”和“确定”两个选项按钮。
```
  
图3-3-13 租户绑定共享异构设备组

#### 3.4.3.6. 租户停服

点击租户列表右侧操作列更多下停服按钮，可停止租户下的密服业务服务。

![](images/2d3385b0ae955bca85ca4c5a056b59212fa8f45ebb2bbf8ba685d5eb28fe18f5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个提示框，内容为：“停止后租户无法正常使用业务，确认要停止吗？” 提示框的左上角有一个黄色的感叹号图标，表示这是一个警告或重要信息。右上角有一个“X”按钮，用于关闭提示框。提示框的底部有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来选择是否继续执行停止操作。
```
  
图3-3-14 租户解绑共享异构设备组  
图 3-3-15 租户停服界面

#### 3.4.3.7. 租户启动

已停服的租户，可点击操作列更多下的“启动”按钮，启动当前租户。

![](images/1ebcbce689f87bdf9962f995679bdd085374c3c26ba945e429436d98881b83ee.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个管理租户信息的系统页面。页面上有一个表格，列出了多个租户的信息，包括租户标识、租户名称、机构名称、业务、应用数量、服务数量和设备数量等字段。表格中有几行数据，每行代表一个租户的信息。

在页面的中央，有一个弹出的提示框，背景为白色，边框为灰色。提示框内有一个黄色的感叹号图标，旁边的文字是“确认启动租户吗？”，这表明系统正在询问用户是否确认执行某个操作，即启动某个租户。提示框下方有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来选择是否执行该操作。

从图片中可以看出，这个系统可能用于管理和控制不同租户的服务状态，用户可以在这里查看和修改租户的相关信息，并通过提示框中的操作来启动或停止租户的服务。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.4.3.8. 删除租户

点击租户列表右侧操作列删除按钮，删除租户。运行中的租户，不允许点击删除按钮。租户需要先停服，保证租户内无使用中应用、密钥、服务和设备后，再删除。

![](images/c19409fa8f41045aef61f5d0ed64c41f59823e6215f05233d0416297e76836f0.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个确认对话框，内容是询问用户是否确认要删除名为“Test_Tenant”的租户。对话框的左上角有一个黄色的感叹号图标，表示这是一个警告或重要提示。对话框的右上角有一个关闭按钮（X）。对话框底部有两个按钮：“取消”和“确定”，分别用于取消操作或确认删除。
```
  
图 3-3-17 租户删除界面

### 3.4.4. 密码产品配额管理

以系统操作员身份登录，点击租户管理页面列表项操作列中的密码产品配额按钮，进入密码产品配额管理页面，可以对当前租户的密码产品配额进行管理。

![](images/b47f19252c4efc9d96d05cf9a61c4cafea3a96c45b2b4a2010d879e1de2ee95b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个管理界面，列出了三个租户的信息。每个租户的详细信息包括序号、租户标识、租户名称、机构名称、区域、单位名称、业务、应用数量、创建时间和状态。所有租户的状态都显示为“运行中”。此外，每个租户旁边都有一个操作选项，可以查看详情、密码产品配额和业务地址等更多信息。
```
  
图 3-3-16 租户启动界面

![](images/17fc6e5d95a13e50f984577920693019f2cf2297d5fed4b472ea21e48a5d2490.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“zuhu01-密码产品配额管理”的界面，列出了各种密码服务及其相关详细信息。表格中包含了序号、产品名称、产品类型、开通状态、开通数量、单位、配额、到期时间以及备注等列。例如，第一行显示了“数据加解密服务”，其产品类型为“密码服务”，开通状态为“已开通”，开通数量为1实例/月，配额为50Mbs，到期时间为2024年5月31日19:00:39。每行的最右侧有操作选项，包括详情、开通、编辑和停用。
```
  
图3-3-18 密码配额管理页面入口

#### 3.4.4.1. 密码产品配额详情

进入密码产品配额管理页面，点击列表项操作列中“详情”按钮，进入密码产品配额详情页面，展示配额信息以及变更记录。

配额信息

SSL网关服务 这好似一段备注

变更记录

![](images/634e57961ebdffae0535029b799fa43a3e0e83529b83efcc40bf9b0a41af46f7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个变更记录表，包含了两行变更信息。第一行是关于开通数量的变更，从1变更为12，完整性校验通过，并附有备注“这好似一段备注”，变更时间为2023年12月4日15:03:25。第二行是关于到期时间的变更，从2023年12月29日00:00:00变更为2023年12月8日00:00:00，同样通过了完整性校验，备注内容相同，变更时间也是2023年12月4日15:03:25。
```
  
图3-3-19 密码配额管理页面  
图3-3-20 密码配额管理详情

### 3.4.4.2. 密码产品配额开通

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

点击密码产品配额管理页面列表项操作列中的“开通”按钮，弹出框显示开通密码产品表单。

![](images/13fa5309a89d667ffd5ea924e2708c43f2a4126a8c8b6ebac71ca6e7f190867c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“zuhu01 - 密码产品配额管理”的界面，主要用于管理和配置密码相关的服务。界面上有一个弹出窗口，标题为“开通密码产品”，用户正在配置“密钥管理服务”这一产品。在弹出窗口中，用户已经设置了以下参数：

- 开通数量：2
- 共享服务组：kms_share
- 到期时间：2024年5月30日00:00:00

此外，还有一个备注栏，目前为空白，允许用户输入额外的注释信息。

背景中列出了多种密码服务，包括数据加解密服务、签名验签密码服务、时间戳密码服务、SSL网关服务、密钥管理服务、协同签名服务、动态令牌认证服务、数据库加密服务、文件加密服务、电子签章密码服务和数字证书认证服务等。每项服务都有对应的实例/月配额，但具体数值未显示。用户可以通过“详情”、“开通”、“编辑”和“停用”按钮对这些服务进行操作。
```
  
图 3-3-21 密码配额开通

输入信息后，点击确定进行密码产品配额开通。

密码产品配额列表中只有未开通和已停用的列表项才能开通配额，其他情况下开通按钮不可用。

### 3.4.4.3. 密码产品配额编辑

点击密码产品配额管理页面列表项操作列中的“编辑”按钮，弹出框显示编辑密码产品表单。

![](images/968ee5ceda2721634d815294a2ccf577839cc2120fbca8f923f75d25596a3729.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个名为“zuhu01 - 密码产品配额管理”的网页界面。页面的主要内容是关于密码产品的管理和配置，具体包括数据加解密服务、签名验签密码服务、时间戳密码服务、SSL网关服务、密钥管理服务、协同签名服务、动态令牌认证服务和数据库加密服务等。

在页面的中央，有一个弹出窗口，标题为“编辑密码产品配额”，正在编辑的产品名称是“数据加解密服务”。在这个窗口中，可以看到以下信息：

- 开通数量：1 实例/月（配额：50Mbps）
- 共享服务组：pki_share
- 到期时间：2024-05-31 19:00:39
- 备注：请输人备注

在弹出窗口的底部有两个按钮，分别是“取消”和“确定”。

在背景页面中，可以看到一个表格列出了各种密码服务的序号、产品名称、产品类型、实例/月、备注和操作选项。每个服务都有对应的详细、开通、编辑和停用的操作按钮。
```
  
图 3-3-22 密码配额开通

更改信息后，点击确定进行改密码产品配额编辑。

密码产品配额表中只有已开通状态的列表项才能编辑配额，其他情况下编辑按钮不可用。

### 3.4.4.4. 密码产品配额停用

![](images/f2bdd6c93790cd364dc6b48f8e71ba55e5df9c33760e5c0e02cda6e6a6f62766.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个名为“zuhu01 - 密码产品配额管理”的界面，主要用于管理和查看各种密码服务产品的配额和状态。界面上方有一个搜索栏，用户可以输入产品名称或选择产品类型进行查询。右侧有两个按钮，分别是“查询”和“重置”。

在界面的主体部分，有一个表格列出了不同产品的详细信息，包括序号、产品名称、产品类型、开通状态、开通数量、单位、配额、到期时间和备注等。每行代表一个不同的密码服务产品，例如数据加密解密服务、签名验签密码服务、时间戳密码服务等。

在表格的右侧，有一列操作选项，每个产品都有“详情”、“开通”、“编辑”和“停用”四个按钮，用户可以通过这些按钮对产品进行相应的操作。

当前图片中，有一个弹出的提示框，询问用户是否确认停用当前产品，提示框中有“取消”和“确定”两个按钮供用户选择。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

点击密码产品配额管理页面列表项操作列中的“停用”按钮，弹出框提示确认信息，确认后停用该密码产品配额，配额状态开通状态变为已停用。

## 3.5.设备管理

设备管理负责管理平台的所有设备信息，包括云密码机、虚拟密码机、物理密码机、服务一体机等，并负责设备组的信息管理

### 3.5.1. 云密码机管理

#### *******. 云密码机列表

云密码机列表页面中，在搜索框中输入名称和管理 IP，可以模糊查询云密码机列表。

![](images/1baaaa7a2f204e6d4b3e7daa97883f7785443aafe4d18942fc7974babf60cdfc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示云密码机管理界面的截图。界面上有一个表格，列出了云密码机的相关信息，包括序号、名称、所属厂商、设备类型、区域、管理IP、管理端口、版本、序列和设备状态等。
```
  
图 3-3-23 密码配额开通  
图 3-4- 1 云密码机列表页面

#### *******. 添加云密码机

云密码机管理页面中，点击“新建”按钮，打开添加云密码机页面。输入云密码机信息，点击“确定”按钮添加云密码机。管理 ip 和管理端口根据实际设备部署情况获取。

![](images/60e631cd454128df998c1248f7227323d88b4966e6976671bdd56ccac2e5aa06.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个管理界面，具体来说是一个用于新增云密码机的弹窗表单。表单中包含多个输入字段和选择框，用户需要填写或选择以下信息：

1. **名称**：这是一个必填项，用户需要在这里输入云密码机的名称。
2. **所属厂商**：这是一个下拉选择框，当前选择的是“三未信安科技股份有限公司”。
3. **设备类型**：这也是一个下拉选择框，当前选择的是“云密码机_V4.1.2”。
4. **区域**：这是一个下拉选择框，用户需要选择云密码机所在的区域。
5. **IP**：这是一个必填项，用户需要在这里输入云密码机的IP地址。
6. **管理端口**：这是一个必填项，当前默认值为8083。
7. **支持虚机总数**：这是一个必填项，当前默认值为32。
8. **备注**：这是一个可选的文本输入框，用户可以在这里添加一些额外的备注信息。

在表单的底部有两个按钮：“取消”和“确定”。用户可以点击“取消”按钮来关闭这个弹窗而不保存任何信息，或者点击“确定”按钮来提交表单并新增云密码机。

背景中可以看到一个表格，列出了已有的云密码机的信息，包括序号、名称、所属厂商等。右上角有一些操作按钮，如“查询”和“重置”，用户可以通过这些按钮来进行相应的操作。
```
  
图 3-4- 2 云密码机添加页面

#### *******. 编辑云密码机

云密码机信息列表，点击右侧操作列“编辑”按钮，打开编辑云密码机信息页面，修改云密码机名称和备注，点击“确定”按钮保存云密码机信息。

![](images/c6dd4d45d359a67d5130e40d76e2b3cea7e2fde5e67ce5a5845ec067dac3c61f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示云密码机管理界面的截图，
```


![](images/3dad7ca10f32c213116f746c70429d0077f5d1d7673babc8d1cb2a7a47174baf.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“编辑云密码机”界面的图片，界面上有多个输入框和选择框，用于填写云密码机的相关信息。具体包括：

- 名称：已填写为“362云机”
- 所属厂商：已选择“三未信安科技股份有限公司”
- 设备类型：已选择“云密码机_V4.1.1”
- 区域：已选择“region1”
- IP：已填写为“**********”
- 管理端口：已填写为“8083”
- 可创建虚机总数：已填写为“32”
- 备注：提示“请输入备注”，目前为空

在界面底部有两个按钮，分别是“取消”和“确定”。
```
  
图 3-4- 3 云密码机编辑

#### *******. 查看设备详情

云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。

![](images/60c24a26141760a07c615146aaf4dbb02c14cce47dcbb0ec85466a1fb1f14853.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个云密码机的详细信息页面，具体包括以下内容：

### 基础信息
- **设备名称**: 云机
- **版本**: 4.0.2
- **管理端口**: 8083
- **所属厂商**: 三未信安科技股份有限公司
- **序列号**: 48313942-3032-3031-3231-353030303031
- **虚机数量**: 已用26/可用6
- **设备类型**: 云密码机_V4.1.1
- **管理IP**: **********
- **登记时间**: 2024-05-13 15:17:16
- **设备管理平台地址**: 未提供具体地址
- **公钥指纹**: hFVstZQV87my3TYL8WqVJN57tQ5CP1vdyZxbatyW2M8=
- **备注信息**: 无

### 网络信息
- **网卡bond0**: 
  - IP: **********
  - 子网掩码/前缀: *************
  - 网关: ************

此外，页面顶部有一个返回按钮和标题“云机-云密码机详情”，页面底部有一个标签“虚拟机列表”，但该列表在图片中并未展示具体内容。
```
  
图 3-4- 4 云密码机编辑页面  
图 3-4- 5 云密码机详情页面

#### *******. 云密码机删除

云密码机列表页，点击右侧操作列 更多->“删除”按钮，在弹出框中点击“确定”按钮删除云密码机。系统中存在使用该云密码机生成的虚拟机时，云密码机不能删除。

![](images/adb143ac302b6921648a698cb65a56877a6eab7f3be3869885129e168343a53d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个设备管理界面，具体来说是一个云密码机的管理页面。界面上方有两个输入框，分别用于输入“名称”和“管理IP”，以及两个按钮，分别是“查询”和“重置”。在输入框下方有一个表格，列出了设备的相关信息，包括序号、名称、所属厂商、设备类型、区域、管理IP、管理端口、版本、序列、设备状态等。表格中有一条记录，显示了一台名为“362云机”的设备，其所属厂商为“三未信安科技股份有限公司”，设备类型为“云密码机_V4.1.1”，区域为“region1”，管理IP为“**********”，管理端口为“8083”，版本为“4.0.2”，序列号为“483”，设备状态为“运行中”。在操作列中，有“详情”、“网络配置”、“创建虚拟机”和“更多”等选项，其中“更多”选项下拉菜单中显示了“编辑”和“删除”两个操作选项，其中“删除”选项被红色框标出。
```
  
图 3-4- 6 云密码机删除页面

#### *******. 添加虚拟机网络

云密码机列表页，点击右侧操作列网络配置按钮，打开网络配置页面。

![](images/15fab1e87be0bb2cab95c163110f2cf39f8d4bf347f282d78b03eb9abaaa7213.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个云密码机的管理界面。
```
  
图 3-4- 7 云密码机网络配置

点击“新建”按钮，打开添加虚拟机网络页面，输入虚机网关，填写起始管理 IP、结束管理IP，填写起始业务IP、结束业务IP，点击“确定”按钮保存虚拟机网络。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/5414c7fcd606bf3124d341621f9d57a32c022945c2488a281357cc0b2892745e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个名为“chsm - 虚拟机网络配置”的管理界面。界面上有一个表格，列出了虚拟机的网络配置信息，包括序号、管理IP、业务IP、虚拟网关和掩码等。具体来说，有两条记录，每条记录都包含了上述信息，并且显示了使用状态为“已使用”，创建时间为2023-10-13 15:28:15。此外，表格右侧还有操作列，提供了删除选项。
```
  
图 3-4- 8 虚拟机网络页面

重要字段说明：

虚拟网关：云机所在网络的网关

子网掩码：云机所在网络的子网掩码，默认*************，需现场确认

起始管理ip：虚拟机 ip 范围段的的起始ip，确保未被占用

结束管理ip：虚拟机 ip 范围段的的结束ip，确保未被占用

![](images/b883ae3556c4cabf1c2e21489759ce815d268b7a71189d05ca4cdaccd45c7304.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个虚拟机网络配置的界面，具体来说是一个新建虚拟机网络配置的弹窗。在这个弹窗中，用户需要填写一些必要的信息来完成配置，包括：

- 虚拟网关：这是一个必填项，用户需要在这里输入虚拟网关的地址。
- 子网掩码：这也是一个必填项，当前已经预设为*************。
- 起始管理IP和结束管理IP：这两个也是必填项，用户需要在这里输入起始和结束的管理IP地址。
- 起始业务IP和结束业务IP：这两个是可选项，用户可以在这里输入起始和结束的业务IP地址。
- 备注：这是一个可选项，用户可以在这里输入一些备注信息。

在弹窗的底部有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来取消或确认他们的操作。

在背景中，我们可以看到一个表格，这个表格列出了已经存在的虚拟机网络配置的信息，包括序号、管理IP、业务IP、创建时间、备注和操作等。用户可以通过点击“删除”按钮来删除这些已有的配置。
```
  
图 3-4- 9 新增虚拟机网络配置页面

提示：添加云密码机时，如果云密码机存在虚拟机，系统会依据虚拟机网段信息回填网络信息。

#### *******. 删除虚拟机网络

在虚拟机网络配置页面，点击右侧操作列“删除”按钮，二次确认后删除网络。当网络配置信息被使用时不允许删除。

![](images/17f7001eaf98d064f4b5c7dd767863642d8d4766f379997c23546061dc79e364.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个虚拟机网络配置的管理界面，具体是“chsm - 虚拟机网络配置”页面。界面上方有多个标签页，包括首页、服务管理、服务类型管理、网关管理、数据库管理、云密码机和设备管理等。

在页面的主要部分，有一个表格列出了两行虚拟机网络配置的信息，每行包含序号、管理IP、业务IP、虚拟网关、掩码、使用状态、创建时间、备注和操作等列。可以看到，这两行的管理IP分别是************和************，业务IP与管理IP相同，虚拟网关都是************，掩码为*************，使用状态都显示为“已使用”，创建时间都是2023-10-13 15:28:15，备注栏为空，操作列提供了删除选项。

此外，在页面的中间位置，有一个弹出的提示框，内容是“是否确认删除网络配置信息？”，并提供了“取消”和“确定”两个按钮供用户选择。这表明用户可能正在尝试删除某个网络配置，并需要进行确认操作。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### *******. 配置 vxlan 网络

云密码机列表页面，点击右侧的“vxlan 网络配置”按钮，打开网络配置页面。填写网络名称、网卡、网段、网关、子网掩码、vxlanID、Vxlan 对端地址、Vxlan 端口 P，点击“确定”按钮保存vxlan 网络。

![](images/7b53422e233d4a9cc07a495421beab0490ba186ab8df16c7ed9d91813a5e5a2d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个设备管理界面，具体来说是一个云密码机的管理页面。界面上方有搜索栏，可以输入名称和管理IP进行查询。下方是一个表格，列出了设备的相关信息：

- 序号：1
- 名称：测试部门机器云密...
- 所属厂商：三未信安科技股份...
- 设备类型：云密码机_V4.1.X
- 管理IP：************
- 管理端口：8083
- 版本：4.1.4
- 序列号：48313942-3032-3031...
- 可创建虚机数量：0
- 已创建虚机数量：32
- 设备状态：运行中

在操作列中，有一个红色框标注了“vxlan网络配置”的选项，这可能是当前关注的重点功能或操作。

此外，页面底部显示了分页信息，当前显示的是第1页，共1条记录，每页显示20条。
```
  
图 3-4- 10 删除虚拟机网络配置页面  
  
图 3-4- 11 云密码机列表页面

![](images/6ca39c2d1395bf9fd78508ab4d30b52ba05d6235614ba98eea0a46dd19453769.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于VXLAN网络配置的界面截图。界面上有多个输入框和选项，用于设置VXLAN网络的相关参数。具体包括：

1. 网络名称：一个输入框，提示用户输入网络名称。
2. 网卡：一个下拉菜单，当前选择的是“bond0”。
3. 网段：一个输入框，提示用户输入网段，例如“如192.168.1.0/24”。
4. 网关：一个输入框，提示用户输入网关。
5. 子网掩码：一个输入框，提示用户输入子网掩码。
6. VXLAN ID：一个输入框，提示用户输入VXLAN ID。
7. VXLAN端口：一个输入框，提示用户输入VXLAN端口。
8. VXLAN对端地址：一个输入框，提示用户输入VXLAN对端地址，并且有一个加号和减号按钮，可能用于添加或删除对端地址。
9. 备注：一个文本区域，提示用户输入备注，当前显示为“0/100”，可能表示最多可以输入100个字符。

在界面底部，有三个按钮：“取消”、“删除”和“确定”。用户可以通过这些按钮来取消操作、删除配置或确认并保存配置。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图 3-4- 12 vxlan 网络配置页面

如果已添加vlxan 网络，点击“vxlan 网络配置”，打开 vxlan 网络配置页面，可编辑vxlan 对端地址以及备注，点击确定，保存 vxlan 网络配置信息。

![](images/41e81ca197fc509be5737a54b8c167fea5210fd3c19703507955320340b56a70.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个网络配置界面，具体是关于VXLAN（虚拟扩展局域网）的设置。界面上有多个输入框和选项，用户可以在这里配置网络的相关参数。以下是各个部分的详细说明：

1. **网络名称**：当前设置为“vxlan”，这是用户为这个网络配置指定的名字。
2. **网卡**：选择的是“bond0”，这可能是一个绑定的网络接口，用于提高网络的可靠性和性能。
3. **网段**：设置为“**********/24”，表示这是一个私有IP地址段，用于内部网络通信。
4. **网关**：设置为“**********”，这是该网络的默认网关，用于数据包的转发。
5. **子网掩码**：设置为“*************”，与网段配合使用，定义了网络中可用的IP地址范围。
6. **vxlanID**：设置为“100”，这是VXLAN的标识符，用于区分不同的VXLAN网络。
7. **vxlan端口**：设置为“4789”，这是VXLAN的标准UDP端口号，用于封装和传输VXLAN数据包。
8. **vxlan对端地址**：列出了两个IP地址，“************”和“************”，这些是对端设备的IP地址，用于建立VXLAN隧道。
9. **备注**：这是一个可选的文本框，用户可以在这里添加任何相关的注释或说明。
10. 在界面底部，有三个按钮：“取消”、“删除”和“确定”。用户可以通过点击这些按钮来取消操作、删除配置或确认并保存设置。

这个界面允许管理员详细配置VXLAN网络，以满足特定的网络需求和架构要求。
```
  
vxlan网络配置  
图 3-4- 13vxlan 网络编辑页面

在vxlan 网络编辑页面，点击“删除”按钮，可删除 vxlan 网络配置。提示：配置 vxlan 网络后，如果需要使 vxlan 网络创建虚拟机，需在对应服务器上搭建vxlan 网络。

#### *******. 批量创建虚拟机

云密码机列表中，点击右侧操作列创建虚拟机按钮，打开批量创建虚拟机页面。在批量创建虚拟机页面，选择设备类型、虚拟机网络、资源配置，输入创建虚拟机的创建数量、管理端口和服务端口；可以更改连接密码（可选），点击“确定”按钮，后台异步创建虚拟机。

打开虚拟机页面，可以查看新创建的虚拟机状态为创建中。  
后台异步创建成功后，虚拟机状态则会变成运行中。

![](images/2c99eceb471b38eeada81294e5b4989c1ba01d025032fbc42abdba19cbb6731e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示云密码机管理界面的截图。
```
  
图 3-4- 14 创建虚拟机

重要字段说明：

虚拟机网络：云机中配置的虚机网络

资源配置：资源由云机资源/可创建数量计算得出，如果云机配置为128g，创建数量为

32，则 1 倍资源为 4g

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/fb4713f2c99faade818f04d92a8a1ad4faed221a80989e1cde6d2b23aa3a68dc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于批量创建虚拟机的配置界面截图。界面上有多个输入框和选择框，用于设置虚拟机的相关参数。具体包括：

1. 设备类型：当前选择的是“云服务器密码机_V5.2.7”。
2. 区域：需要用户选择一个区域，但目前还未选择。
3. 虚拟机网络：显示为“************”。
4. 资源配置：当前选择的是“一倍虚机资源”。
5. 创建数量：需要用户输入创建的虚拟机数量，目前还未输入。
6. 管理端口：显示为“443”。
7. 业务端口：显示为“8008”。
8. 连接密码：已设置，但具体内容被隐藏，并提供了一个“更改”链接以便修改密码。
9. 备注：提供了一个文本框供用户输入备注信息，目前还未输入。

在界面底部有两个按钮：“取消”和“确定”，分别用于取消操作或确认并提交配置。
```
  
图 3-4- 15 创建虚拟机页面

#### *******0. 云密码机开机关机重启

在复选框中选择要启动或停止的云密码机，点击开机按钮、关机按钮或者重启按钮，在弹出的提示框中点击“确定”按钮实现开机关机重启。

![](images/8bd26d853ea8e74fcd22d17d8ee865b5099882569d52e8c24f12568682a36d1f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个设备管理界面，列出了一个名为“131”的设备的详细信息。该设备属于三未信安科技股份有限公司，设备类型为云密码机_V4.1.X，管理IP地址为************，管理端口为8083，版本为4.1.1，序列号为48313942-3032-3031-...，支持带外管理，可创建虚拟机数量为1，当前设备状态为运行中。在操作列中，有一个下拉菜单，提供了创建虚拟机、编辑、删除、开机、关机和重启等选项。
```
  
图 3-4- 16 云密码机启动停止重启

### 3.5.2. 虚拟密码机管理

本模块主要对虚拟密码机的生命周期将进行管理

#### *******. 虚拟密码机列表

输入虚拟机名称、所属主机、管理 IP、业务IP、所属组，选择设备类型，点击查询按钮模糊查询符合条件的虚拟机。

![](images/ce7ba774ac1d5aa878718b8115a77c94c3a7b7fb4c9815b78ece85a5402b379f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示虚拟密码机管理界面的截图，界面上有多个标签页，包括首页、服务管理、服务类型管理、网关管理、数据库管理、云密码机、设备管理以及当前选中的虚拟密码机。在虚拟密码机页面中，有一个表格列出了多个虚拟密码机的信息，包括序号、名称、所属主机、所属厂商、设备类型、区域、所属租户、所属设备组、IP端口、管理、设备状态和操作等列。表格下方显示共有25条记录，当前显示第1页，每页显示20条记录。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### *******. 批量创建虚拟密码机

在虚拟机管理界面直接点击创建虚拟机按钮，打开批量创建虚拟机页面。

![](images/b2eb5fd027e511535920fa98079c1f049eacfc6d384eb35815c59bf2ff23bc82.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个虚拟密码机管理界面，界面顶部有多个标签页，包括首页、服务管理、服务类型管理、网关管理、数据库管理、云密码机、设备管理等。当前选中的标签页是“虚拟密码机”。在页面的中部，有一个表格列出了多个虚拟密码机的信息，包括序号、名称、所属主机、所属厂商、设备类型、区域、所属租户、所属设备组、IP端口、管理、设备状态和操作等列。表格上方有一个红色框标注的“创建虚拟机”按钮。表格下方显示了共25条记录，当前显示第1页，每页显示20条记录。
```
  
图 3-4- 17 虚拟密码机页面  
图 3-4- 18 创建虚拟密码机

在批量创建虚拟机页面，选择设备类型、区域（区域模式打开时选择）、虚拟机网络、资源配置、输入创建虚拟机的创建数量、管理端口、服务端口、可以更改连接密码（可选），点击“确定”按钮，后台异步创建虚拟机。  
打开虚拟机页面，可以查看新创建的虚拟机状态为创建中。  
后台异步创建成功后，虚拟机状态则会变成运行中。

![](images/cb582260e6b531cb1217ee84fba1155b09d6e09ac9a44ec53d0a1ff9ef3c369c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于批量创建虚拟机的配置界面截图。界面上有多个输入框和选择框，用于设置虚拟机的相关参数。具体包括：

1. 设备类型：选择了“云服务器密码机_V5.2.7”。
2. 区域：选择了“region1”。
3. 虚拟机网络：选择了“************”。
4. 资源配置：选择了“一倍虚机资源”。
5. 创建数量：输入了“2”，表示要创建两个虚拟机。
6. 管理端口：输入了“443”。
7. 业务端口：输入了“8008”。
8. 连接密码：显示为“******”，并有一个“更改”链接，可以修改密码。
9. 备注：有一个文本框，提示“请输入备注”，当前为空。

在界面底部有两个按钮：“取消”和“确定”，用户可以通过点击这两个按钮来取消操作或确认创建虚拟机。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图 3-4- 19 创建虚拟密码机页面

#### 3.5.2.3. 虚拟密码机详情

点击详情按钮，进入虚拟机详情信息界面，包括基础信息，和网络信息。

![](images/0538e977f9e75b8f25da0fb2a43c82502c4cd9fb36d1e471c0f6296ffb95fc86.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个云服务器密码机的基础信息和网络信息。基础信息部分包括设备的名称、所属主机、所属租户、管理端口、设备管理系统地址、创建时间、公钥指纹和备注信息等。具体来说，设备名称为vsm_10_20_36_225_19443，所属厂商是三未信安科技股份有限公司，设备类型是云服务器密码机_V6，序列号为a19b0201-2150-2583-0027-261c87865548，区域为region，管理IP为************，服务端口为8080，资源配置为一倍虚机资源，虚机镜像为服务器密码机。网络信息部分则列出了IP地址、网关和子网掩码，其中IP地址为************，网关为************，子网掩码为*************。
```
  
个返回 vsm_10_20_36_225_19443-虚拟机详情  
图 3-4- 20 虚拟密码机详情页面

#### *******. 虚拟密码机编辑

在虚拟机列表页，点击右侧操作列“编辑”按钮，打开虚拟机编辑页面，修改虚拟机信息(可编辑名称、密码和备注)后，点击“确定”按钮保存编辑的信息。

![](images/670a8ef58d03de3cdf3d687f5e9b0debfb5bfdb7a02f1ab9e7e4d3e407d19c4b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示虚拟机配置界面的图片，具体信息如下：

- 名称：vsm_10_20_36_225_19443
- 设备类型：云服务器密码机_V6
- 区域：region（此处可能需要选择具体的区域）
- 管理IP：************
- 管理端口：19443
- 业务端口：8008
- 连接密码：已隐藏，旁边有“更改”选项
- 备注：空白，提示“请输入备注”，当前字数为0/100

界面底部有两个按钮，分别是“取消”和“确定”。
```
  
图 3-4- 21 虚拟密码机编辑页面

#### *******. 虚拟密码机删除

在复选框中选择要删除的虚拟机，点击删除按钮，在弹出窗口中点击确定，删除虚拟机（必须处于停止状态的虚拟机才可删除）。如果虚拟机已经绑定了设备组，需要先从设备组解除绑定后再执行删除。

![](images/874b4d52fb87c190801839f9d89e0af6819f7abe990c7fb53ecf0391dec29b0a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个管理云服务器密码机的界面，具体来说是一个虚拟密码机和物理密码机的管理页面。界面上方有搜索栏，可以按照名称、所属主机、管理IP、服务IP等条件进行查询。下方是一个表格，列出了各个密码机的详细信息，包括序号、名称、所属主机、所属厂商、设备类型、区域、所属租户、所属设备组、IP端口、管理、设备状态和操作等。在操作列中，可以看到针对不同密码机的操作选项，如详情、编辑、启动、更多等。其中，第8行的“hsn_H19B0201215…”这一条记录的操作列中，有一个红色框标注的“删除”按钮，提示用户可以对这条记录进行删除操作。
```


#### *******. 虚拟密码机启动停止重启

在复选框中选择要启动或停止的虚拟机，点击启动按钮或停止按钮，在弹出的提示框中点击“确定”按钮启停虚拟机。

![](images/2c04e511d7aab35bed1ff477c0d357a4f0d2082c550d58ae37b3c5beb234be37.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个管理界面，主要用于管理和监控虚拟密码机和物理密码机。界面上方有搜索栏，可以输入名称、所属主机、管理IP、服务IP等信息进行查询。下方是一个表格，列出了各个设备的详细信息，包括序号、名称、所属主机、所属厂商、设备类型、区域、所属租户、所属设备组、IP/端口、端口、设备状态以及操作选项。在操作列中，可以看到“暂停”和“重启”的按钮，用户可以通过这些按钮对设备进行相应的操作。此外，表格底部还显示了当前页码和总页数，方便用户进行分页浏览。
```
  
图 3-4- 22 虚拟密码机删除

#### *******. 强 删除虚拟机

如果虚拟机删除失败，可以点击强制删除虚拟机，点击强制删除按钮，在弹出窗口中点击确定，删除虚拟机（只有删除失败和获取运行状态异常的虚拟机允许强制删除）。强制删除只会删除数据库中的记录，不会删除云密码机中的虚拟机实例，可能会引发其他问题。

![](images/c0de249e30334f22e2cc22508e8f2009e6cea72fd0ea8e5cec34316fcec6a955.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个虚拟密码机管理界面，界面上有多个输入框和按钮用于查询和创建虚拟密码机。表格列出了已有的虚拟密码机信息，包括序号、名称、所属主机、所属厂商、设备类型、区域、所属租户、所属设备组、IP端口、管理状态、设备状态和操作选项。
```
  
图 3-4- 23 虚拟密码机启动停止重启  
图 3-4- 24 强制删除虚拟密码机

#### *******. 跳转虚拟机管理系统

如果虚拟机所属设备类型支持跳转设备管理系统，点击虚拟机列表界面操作列中的管理系统按钮，可直接跳转至虚拟机管理系统。

![](images/b4df8f72d53cb90a998b9afe009b6423fab8004f58a555c9dceb55534b95c788.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示虚拟密码机管理界面的截图，界面上有多个标签页，当前选中的标签页是“虚拟密码机”。
```
  
图 3-4- 25 跳转虚拟密码机管理系统  
图 3-4- 26 生成虚拟机影像

#### *******. 生成虚拟机影像

在虚拟机列表页，点击右侧操作列“更多”按钮，点击“生成影像”，打开提示框“确认要生成虚拟机影像吗？”，点击确定生成虚拟机影像。

序号 名称 所属主机 所属厂商 设备类型 所属租户 所属设备组 IP端口 管理状态 完整性校验 备注 创 设备状态 操作

#### *******0. 下载虚拟机影像

在虚拟机列表页，点击右侧操作列“更多”按钮，点击“下载影像”，可将以 zip 文件的形式下载虚拟机影像文件。注：只有已生成影像的虚拟机可以下载影像，否则提示错误。

+创建虚拟机所属主机 所属厂商 设备类型 所属租户 所属设备组 IP端口 管理状态 完整性校验 备注 创建时间 设备状态 操作载影像

#### *******1. 导入虚拟机影像

在虚拟机列表页，点击右侧操作列“更多”按钮，点击“导入影像”，打开影像导入页面，上传影像文件和签名值，点击确定，导入影像。

![](images/b193c5c48c96dc827a530f67c038629790fae8f4900b4a4a5ba0a240c280a268.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个虚拟机影像上传界面。界面上有一个虚线框，提示用户可以将文件拖到此处或点击上传。虚线框内有一个云状图标，表示上传功能。下方有一行文字说明，指出只能上传.zip文件，并且文件大小不能超过10MB。在虚线框下方，有一个标有星号的“签名值”输入框，提示用户需要输入签名值。最后，在界面底部有两个按钮，分别是“取消”和“确定”，供用户选择是否确认上传操作。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图 3-4- 27 下载虚拟机影像  
图 3-4- 28 导入虚拟机影像

所属主机 所属厂商 设备类型 所属租户 所属设备组 IP端口 管理状态 完整性校验 备注 创建时间 设备状态 操作入彩像

图 3-4- 29 虚拟机影像导入页面

### 3.5.3. 物理密码机管理

物理密码机包括密码机、服务一体机、异构设备的管理

#### *******. 物理密码机列表

物理密码机列表展示已经注册到系统中物理密码机信息，包含密码机的名称、所属厂商、设备类型、所属设备组、管理 IP、管理端口、版本、序列号、完整性校验、备注等信息。

![](images/ab3c696fce7bb0aff07f631134951cb271ee8c096018b58a9e71e8ec190fab1f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示设备管理界面的截图，界面上方有多个搜索框和按钮，包括名称、所属厂商、设备类型、管理IP、业务IP、区域、所属账户、所属设备组等字段的输入框，以及查询和重置按钮。下方是一个表格，列出了设备的序号、名称、所属厂商、设备类型、区域、所属账户、所属设备组、IP端口、版本、序列号、设备状态和操作等信息。表格中有一条记录，显示了设备的详细信息，如名称为hsm_auto_add_extra，所属厂商为三未信安科技股…，设备类型为Extra_Hsm_Km…，区域为region，IP端口包括管理IP和业务IP，版本为V3.2.0.1，序列号为a0001，设备状态和操作栏为空。
```
  
图 3-4- 30 物理密码机页面

#### *******. 新增物理密码机

系统密码机是将机房已经上架部署完毕的密码机注册到系统中，交由管理平台进行统一管理。输入要注册到系统中密码机的名称、所属厂商、设备类型、管理 IP（v4）、管理端口、业务IP、业务端口、授权码、连接密码、版本、序列号、备注。  
点击确定、添加设备。

重要字段说明：

管理ip：密码机ip  
管理端口：三未的密码机默认443  
业务ip：默认与管理 ip 相同  
业务端口：三未的密码机默认8008  
连接密码：密码机的密文密码  
授权码：从密码机管理页面获取，如已经配置公钥，则可以不填写

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/4ddce9ec860c6a0532b59c0d52ef896f916a649ea297f3bf7536bb20ac0e4535.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于新增物理密码机的配置界面截图，界面上有多个输入框和选择框，用于填写密码机的相关信息。具体包括：

1. 名称：需要输入密码机的名称。
2. 所属厂商：当前选择的是“三未信安科技股份有限公司”。
3. 设备类型：当前选择的是“Extra_Hsm_Kms_Test”。
4. 区域：需要从下拉菜单中选择一个区域。
5. 管理IP：需要输入管理IP地址。
6. 管理端口：已经预设为“3333”。
7. 业务IP：需要输入业务IP地址。
8. 业务端口：已经预设为“3333”。
9. 版本：需要输入版本信息。
10. 序列号：需要输入序列号。
11. 备注：可以输入一些备注信息，当前显示为“请输入备注”，并且右下角显示“0/100”，表示备注内容的长度限制为100个字符。

在界面的底部有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来取消操作或确认输入的信息。
```
  
图 3-4- 31 添加物理密码机页面

#### *******. 编辑物理密码机

在密码机列表页，点击右侧操作列“编辑”按钮，打开物理密码机编辑页面，修改物理密码机信息(可编辑名称、备注、连接密码)后，点击“确定”按钮保存编辑的信息。

![](images/583dcd39888c1e17300bec93a8321c7dd50a84d9da51b1b8eda61e637398100d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“编辑物理机”界面的截图，界面上有多个输入框和选择框，用于填写物理机的相关信息。具体包括：

- 名称：已填写为“dadadas”
- 所属厂商：选择了“物理机测试”
- 设备类型：选择了“物理机测试”
- 业务IP：已填写为“**********”
- 业务端口：已填写为“8546”
- 连接密码：显示为“******”，并有一个“更改”链接
- 版本、序列号、备注：这些字段尚未填写，分别提示“请输入”

在界面底部有两个按钮：“取消”和“确定”，用户可以点击“确定”来保存所填写的信息，或者点击“取消”来放弃当前操作。
```
  
图 3-4- 32 编辑物理密码机页面

#### *******. 删除物理密码机

在密码机信息列表中可以删除已经注册到系统中的设备信息。

![](images/619cdb3a523ed7a9d51ad0507205ea8b426e1dbe98ca60cb6e270b61f2d1ac9e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示设备管理界面的截图，具体来说是物理密码机的管理页面。
```
  
图 3-4- 33 删除物理密码机页面

#### *******. 物理密码机详情

在密码机列表页，点击右侧操作列“详情”按钮，系统打开密码机详情页面。

![](images/71a7c5c2a9002d252a176b8ac5286b73b11714fce0067068c5fad81567533f2e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个设备的基础信息页面，具体包括以下内容：

- **名称**: hsm_auto_add_extra
- **所属厂商**: 三未信安科技股份有限公司
- **设备类型**: Extra_Hism_Kms_Test
- **设备组**: 无特定组名（显示为“region”）
- **管理端口**: 443
- **服务IP**: ************
- **管理IP**: ************
- **服务端口**: 8008
- **设备管理系统地址**: https://***********:19443/kmsweb/portal
- **创建时间**: 2023-10-13 19:09:02
- **公钥指纹**: 未提供具体信息
- **备注信息**: testremark

这些信息通常用于管理和监控特定设备的配置和状态。
```
  
图 3-4- 34 物理密码机详情页面

#### *******. 强 删除物理机

如果物理机无法正常删除，可以点击强制删除物理机，点击强制删除按钮，在弹出窗口中点击确定，删除物理机（如果物理机所属设备类型支持安全管理，只有运行状态获取错误的物理机才可以强制删除）。

![](images/e7ac3ef0e3ceed0fc6d4838ae4c0f92ec32a78c35e2bf0c569fa061481a4d770.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示设备管理界面的截图，界面上方有搜索栏和筛选条件，包括名称、所属厂商、设备类型、所属设备组、管理IP、业务IP和所属租户。下方是一个表格，列出了设备的序号、名称、所属厂商、设备类型、所属租户、所属设备组、IP端口、版本、序列号、完整性校验、设备状态和操作选项。表格中有三条记录，每条记录都包含了上述信息，并且在操作列中提供了详情、编辑、管理系统和更多选项。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### *******. 跳转物理机管理系统

如果物理机所属设备类型支持跳转设备管理系统，点击物理机列表界面操作列中的管理系统按钮，可直接跳转至物理机管理系统。

![](images/7d52d2498d72d00866187924c372e4afa9aa6e749ebe284f2b1a7fdb658c6bdf.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个名为“物理密码机”的管理界面，界面中包含了一个搜索栏和一个设备列表。搜索栏中有多个输入框，分别用于输入名称、所属厂商、设备类型、所属设备组、管理IP和业务IP等信息，以及一个下拉菜单用于选择所属租户。在搜索栏的右侧有两个按钮，一个是蓝色的“查询”按钮，另一个是白色的“重置”按钮。

设备列表中列出了三台设备的信息，包括序号、名称、所属厂商、设备类型、所属租户、所属设备组、IP端口、版本、序列号、完整性校验、设备状态和操作等字段。每台设备的“操作”列中都有“详情”、“编辑”、“管理系统”和“更多”四个选项，其中“管理系统”选项被红色边框突出显示。列表中的设备名称分别为“12_25”、“17_83”和“17_80”，所属厂商均为“三未信安科技股份有限公司”，设备类型分别为“web异构设备三”、“异构设备二”和“异构设备一”。所有设备的完整性校验状态均为“校验通过”。
```
  
图 3-4- 35 强制删除物理机  
图 3-4- 36 跳转物理机管理系统

#### *******. 物理密码机开机关机重启

在复选框中选择要启动或停止的物理密码机，点击开机按钮、关机按钮或者重启按钮，在弹出的提示框中点击“确定”按钮实现开机关机重启。

![](images/39eeeb5b2fbf8d10aff1642c076dce79f031effddd668432c49df7b0f32aa1d2.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个设备管理界面，列出了一个名为“131”的设备的详细信息。该设备属于三未信安科技股份有限公司，设备类型为云密码机_V4.1.X，管理IP地址为************，管理端口为8083，版本为4.1.1，序列号为48313942-3032-3031-...。该设备支持带外管理，可创建虚拟机数量为1，当前设备状态为运行中。在操作列中，有一个下拉菜单，提供了创建虚拟机、编辑、删除、开机、关机和重启等选项。其中，“开机”、“关机”和“重启”选项被红色框标出，可能是为了强调这些操作。
```
  
图 3-3- 1 物理密码机启动停止重启

### 3.5.4. 厂商管理

厂商管理默认内置了三未信安科技股份有限公司，如需增加异构设备则可添加新的厂商自行维护

#### *******. 厂商列表

厂商列表展示系统默认的厂商信息。

![](images/797d7eefe0155fde0a5fa65d9344cd9f4d70275fb8a55b8ae02b791510d9809a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示厂商管理界面的截图，界面上有一个搜索栏，可以输入厂商名称和厂商简称进行查询。下方有一个表格，列出了厂商的信息，包括序号、厂商名称、厂商简称、默认、备注、创建时间和操作。当前只有一条记录，厂商名称是“三未信安科技股份有限公司”，厂商简称是“sanec”，备注为“默认厂商”，创建时间为2023年3月1日14:08:18。
```
  
图 3-4- 37 厂商列表页面

#### 3.5.4.2. 厂商新增

点击厂商列表上方“新建”按钮，显示新建厂商弹出框，需输入厂商名称、简称、备注信息，点击“确定”按钮，新增厂商。

![](images/8fcb293bdd103eca3c97d691043dbcae024ac42cdeb89450e7ba4f6dd57b6a09.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个厂商管理系统的界面，当前正在打开一个名为“新建厂商”的弹窗。在这个弹窗中，用户可以输入新的厂商信息，包括名称、简称和备注。名称和简称是必填项，分别有对应的输入框提示用户“请输入名称”和“请输入简称”。备注栏则是一个可选的文本区域，用户可以在这里输入额外的信息，目前显示为“请输入备注”，并且右下角有一个计数器显示当前已输入字符数为0，最大限制为100个字符。

在弹窗的底部有两个按钮：“取消”和“确定”。用户可以通过点击“取消”按钮来关闭这个弹窗而不保存任何信息，或者通过点击“确定”按钮来提交填写的信息并创建新的厂商记录。

背景部分显示了厂商管理系统的主界面，左侧有一个搜索栏，用户可以在这里输入厂商名称进行查询。下方是一个列表，列出了已有的厂商信息，包括序号、厂商名称、创建时间和操作选项。当前只有一条记录，显示的是“三未信安科技股份有限”，创建时间为2023年3月1日14点08分18秒，操作选项包括“编辑”和“删除”。

此外，在页面的右上角还有两个按钮：“查询”和“重置”，用户可以使用它们来进行更复杂的查询操作或重置当前的查询条件。页面底部有一个分页导航，显示当前共有1条记录，每页显示20条，当前位于第1页，用户可以通过它来浏览更多的厂商信息。
```
  
图 3-4- 38 厂商新增页面

#### 3.5.4.3. 厂商编辑

点击厂商对象操作列下的“编辑”按钮，可修改已创建的厂商信息。

![](images/f4512ff9aaa9b04b357edbf3931c1cda663d9f8429f5e9a7e3143eb50af2025b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个厂商管理界面，具体是在进行厂商信息的修改操作。界面上有一个弹出窗口，标题为“修改厂商”，窗口内有三个输入框，分别用于填写厂商的名称、简称和备注信息。当前填写的信息如下：

- 名称：三未信安科技股份有限公司
- 简称：sanec
- 备注：默认厂商

在弹出窗口的底部有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来取消或确认修改操作。

背景中可以看到厂商管理的主界面，列出了厂商的序号、名称、创建时间和操作选项。当前显示的厂商信息与弹出窗口中的信息一致，创建时间为2023年3月1日14:08:18，操作选项包括编辑和删除。
```
  
图 3-4- 39 厂商编辑页面

#### 3.5.4.4. 厂商删除

点击厂商对象操作列下的“删除”按钮，可删除用户创建的厂商信息。系统默认创建厂商无法删除。

![](images/64d555c162da5b1a6d58caa7bc2e89febbdddf39fe053c5e7c377ebbf49d43d3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个厂商管理系统的界面，当前正在执行删除操作的确认提示。界面上方有“首页”和“厂商管理”的标签，表明用户正在厂商管理模块中。在厂商管理模块中，有一个搜索栏，可以输入厂商名称或厂商简称进行查询，并且有“查询”和“重置”按钮。

在界面的主体部分，有一个表格列出了厂商的信息，包括序号、厂商名称、厂商简称、默认、备注、创建时间和操作。当前表格中有两条记录，第一条记录的厂商名称和厂商简称都是“ceshi”，创建时间是2023-08-14 12:52:17；第二条记录的厂商名称是“三未信安科技股份有限公司”，创建时间是2023-03-01 14:08:18。

在表格的右侧，有一个“新建”按钮，可以添加新的厂商信息。每条记录的右侧都有“编辑”和“删除”两个操作选项。

当前图片的重点在于弹出的确认对话框，提示用户“确认要删除厂商ceshi吗？”并提供了“取消”和“确定”两个选项供用户选择。这表明用户正在尝试删除名为“ceshi”的厂商记录，系统要求用户确认这一操作。
```
  
图 3-4- 40 厂商删除页面

### 3.5.5. 设备类型管理

设备类型管理包括对设备的版本、服务支持类型，设备类型名称等基础信息进行维护

#### 3.5.5.1. 设备类型列表

设备类型列表展示系统默认设备类型信息。

![](images/5379f8a5d7142e891a89fe87946d76e65f57f2f6f82d42f8a8b4fb7b893ccf4b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个虚拟机管理界面，列出了多个虚拟机及其相关信息。具体内容如下：

1. **云密码机_V4.0.2 | 三未信安科技股份有限公司**
   - 虚拟机类型：云服务器密码机_V5.2.5

2. **云密码机_V4.0.1 | 三未信安科技股份有限公司**
   - 虚拟机类型：云服务器密码机_V6

3. **Extra_Hsm_Kms_Test | 三未信安科技股份有限公司**
   - 支持对外服务

4. **服务器密码机_V5.2.5 | 三未信安科技股份有限公司**
   - 支持安全管理

5. **华为服务器密码机 | 三未信安科技股份有限公司**
   - 支持安全管理

6. **华为租户服务器密码机 | 三未信安科技股份有限公司**
   - 支持安全管理

每个虚拟机条目右侧都有一个“更多”链接，可能用于查看或配置更多的详细信息。此外，界面左上角有一个“新建”按钮，可能用于创建新的虚拟机。
```
  
图 3-4- 41 设备类型页面

#### 3.5.5.2. 新增云服务器密码机设备类型

点击新建按钮，弹窗打开新增设备类型页面，选择设备类型：云服务器，完善名称、所属厂商、服务设备类型、管理规范、接口协议、支持虚机数量、管理端口、备注等信息。点击“确定”按钮，保存数据并刷新列表页。

![](images/b7ae5dd1a663d7f31cf1c9e208d8a51a50ecb507be6edb3e14285fe628eb00cc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新增设备类型”表单的图片。表单包含以下字段：

1. **设备类型名称**：一个输入框，提示用户输入设备类型名称。
2. **所属厂商**：一个下拉菜单，提示用户选择所属厂商。
3. **设备类型**：两个单选按钮，分别是“云服务器”和“物理机”，当前选择了“云服务器”。
4. **支持虚机镜像**：一个红色边框的下拉菜单，提示用户选择支持的虚机镜像，并且下方有红色提示文字“请选择支持的虚机镜像”。
5. **管理规范**：一个下拉菜单，提示用户选择管理规范。
6. **接口协议**：两个单选按钮，分别是“HTTPS”和“HTTP”。
7. **支持虚机数量**：一个输入框，提示用户输入支持虚机数量。
8. **管理端口**：一个输入框，提示用户输入管理端口。
9. **备注**：一个文本框，提示用户输入备注，右下角显示当前输入字数为0/100。

在表单底部有两个按钮：“取消”和“确定”。
```
  
图 3-4- 42 新增云服务器设备类型

#### 3.5.5.3. 新增物理机设备类型

点击新建按钮，弹窗打开新增设备类型页面，选择设备类型：物理机，完善名称、所属厂商、管理规范、接口协议、管理端口、业务端口、业务类型、支持安全管理、是否需要连接密码、是否支持对外服务，是否支持跳转设备管理系统等，填写备注信息等。点击“确定”按钮，保存数据并刷新列表页。

![](images/43f512a18e60e55b5083aa3d1aca6b53190153c659e3d339276ce40c69baca4e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于新增设备类型的配置界面截图，界面中包含多个输入框和选项，用于详细定义设备的属性和功能。具体包括：

1. **设备类型名称**：需要输入设备的名称。
2. **所属厂商**：通过下拉菜单选择设备所属的厂商。
3. **设备类型**：可以选择“云服务器”或“物理机”，当前选中的是“物理机”。
4. **管理规范**：通过下拉菜单选择管理规范。
5. **接口协议**：可以选择“HTTPS”或“HTTP”。
6. **管理端口**和**业务端口**：分别需要输入相应的端口号。
7. **业务类型**：有多个复选框供选择，包括数据加解密、签名验签、密钥管理、时间戳、协同签名、动态令牌、数据库加密、文件加密、电子签章、SSLVPN加密通道、数字证书认证等。
8. **支持安全管理**：可以选择“支持”或“不支持”。
9. **连接密码**：可以选择是否需要连接密码。
10. **对外服务**：可以选择是否支持对外服务。
11. **跳转设备管理系统**：可以选择是否支持跳转到设备管理系统。
12. **备注**：可以输入额外的备注信息。

在界面底部有两个按钮：“取消”和“确定”，用于取消操作或确认并保存设置。
```
  
图 3-4- 43 新增物理机设备类型

#### 3.5.5.4. 新增虚拟机设备类型

点击云服务器密码机类型，右侧更多->新建虚拟机类型 按钮，弹窗打开新增设备类型页面，完善名称、管理规范、虚拟机镜像类型、管理端口、业务端口、业务类型、支持安全管理、是否需要连接密码、是否支持对外服务、是否支持跳转设备管理系统、填写备注信息等。点击“确定”按钮，保存数据并刷新列表页。

![](images/d638ef0fc5a78de4c41629f9642082179fe11b8d401532c0e3477ff64b18a44d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个设备类型管理的界面，具体来说是关于云密码机和服务器密码机的管理。界面上列出了多个版本的云密码机和服务器密码机，包括它们的版本号、管理规范、接口协议、管理端口、业务端口以及支持的虚拟机数量等信息。

例如，第一个列出的是“云密码机_V4.0.2”，它由三未信安科技股份有限公司提供，管理规范为“三未标准云密码机0088管理规范”，接口协议为HTTPS，管理端口为8083，支持的虚拟机数量为32。此外，还有其他版本的云密码机和服务器密码机的信息。

在每个设备类型的右侧，有一个“更多”按钮，点击后可以进行编辑、启用、停用或删除等操作。在图片的右上角，有一个红色框标注了“新建虚拟机类型”的选项，表明用户可以在此创建新的虚拟机类型。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/f7b4fa74e99b0362571e2aa0b95d4b236b49c038fc00bf16d47483c5ef843765.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新增设备类型”表单的截图。表单包含多个输入字段和选项，用于配置新设备类型的详细信息。以下是表单的主要内容：

1. **设备类型名称**：一个输入框，提示用户输入设备类型名称。
2. **所属厂商**：一个下拉菜单，当前选择的是“三未信安科技股份有限公司”。
3. **管理规范**：一个下拉菜单，提示用户选择管理规范。
4. **接口协议**：两个单选按钮，分别表示HTTPS和HTTP。
5. **虚拟机镜像类型**：一个下拉菜单，提示用户选择虚拟机镜像类型。
6. **资源配置**：一个下拉菜单，提示用户选择资源配置，当前该字段被红色边框标记，表示这是一个必填项且尚未填写。
7. **管理端口**：一个输入框，提示用户输入管理端口。
8. **业务端口**：一个输入框，提示用户输入业务端口。
9. **业务类型**：多个复选框，包括数据加解密、签名验签、密钥管理、时间戳、协同签名、动态令牌、数据库加密、文件加密、电子签章、SSLVPN加密通道和数字证书认证。
10. **支持安全管理**：两个单选按钮，分别表示支持和不支持。
11. **连接密码**：两个单选按钮，分别表示需要和不需要。
12. **对外服务**：两个单选按钮，分别表示支持和不支持。
13. **跳转设备管理系统**：两个单选按钮，分别表示支持和不支持。

在表单底部有两个按钮：“取消”和“确定”，用于取消操作或确认提交表单。
```
  
图3-4-36 新增虚拟机设备类型  
图 3-4- 44 新增虚拟机设备类型页面

#### 3.5.5.5. 编辑设备类型

点击设备类型右侧 更多->编辑 按钮，弹窗打开修改设备类型信息页面。  
云服务器密码机支持修改名称、备注、支持虚机数量。  
物理机、虚拟机支持修改名称、管理规范、业务类型、是否支持安全管理、是否需要连接密码、是否支持跳转设备管理系统、备注。

![](images/2caa13ebf4630db1f005ecbeffda98496f4d5b553e08c1bbd3d61fab1274c82e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示设备类型编辑界面的截图，具体信息如下：

- 设备类型名称：云密码机_V4.0.2
- 所属厂商：三未信安科技股份有限公司
- 设备类型：选择了“云服务器”
- 支持虚机镜像：列出了多个选项，包括密钥管理服务器、服务器密码机、签名验签服务器、金融密码机、动态令牌服务器、数字证书认证服务器等，但都没有被选中
- 管理规范：三未标准云密码机0088管理规范
- 接口协议：选择了HTTPS
- 支持虚机数量：32
- 管理端口：8083
- 备注：空白，提示请输入备注

在界面底部有两个按钮，分别是“取消”和“确定”。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/b0b587c5e5bc8643d551ed71c2c02078fedf8bede0eccc052fd6537a0945987a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于编辑设备类型的配置界面截图，具体信息如下：

- 设备类型名称：服务器密码机_V5.2.5
- 所属厂商：三未信安科技股份有限公司
- 设备类型：选择了物理机（而非云服务器）
- 管理规范：三未物理密码机管理规范
- 接口协议：选择了HTTPS（而非HTTP）
- 管理端口：443
- 业务端口：8008
- 业务类型：选中了数据加解密、签名验签、密钥管理、时间戳、协同签名、动态令牌、数据库加密、文件加密、电子签章、SSLVPN加密通道（未选中数字证书认证）
- 支持安全管理：选择了支持
- 连接密码：选择了需要，默认连接密码为“******”，并有更改选项
- 对外服务：选择了不支持
- 跳转设备管理系统：选择了不支持

在界面底部有一个备注栏，提示可以输入备注，以及两个按钮，分别是“取消”和“确定”。
```
  
图 3-4- 45 编辑云服务器设备类型  
图 3-4- 46 编辑物理机设备类型

#### 3.5.5.6. 设备类型启用/停用

点击设备类型右侧 更多->启用/停用 按钮，可以对该设备进行启用/停用，停用后的设备类型不支持选择添加设备。

![](images/f7a6ca03ce20b17a07066d557822e5e7ef05d771d6449b7e5bcc29d7502828a3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个云服务器密码机的管理界面，具体是三未信安科技股份有限公司的产品。界面上方显示了该密码机的基本信息，包括管理规范、接口协议（HTTPS）、管理端口（8083）以及支持的虚拟机数量（32）。右侧有一个“新建虚拟机类型”的按钮和一个下拉菜单，其中包含了“启用”、“禁用”和“删除”的选项，其中“启用”和“禁用”被红色框标出，可能表示当前的操作状态或需要特别注意的功能。

在界面的左侧，有一个蓝色的区域，标题为“云服务器密码机”，并提供了更多的详细信息，如管理规范（统一web平台管理规范）、管理端口（19443）、业务端口（8008）以及业务类型，包括数据加解密、签名验签、密钥管理、时间戳、协同签名、动态令牌、数据库加密、文件加密、电子签章、SSLVPN加密通道等。下方还有一行备注信息，以及三个标签：“支持安全管理”、“支持主密钥”和“支持密钥生成”。整个界面设计简洁明了，方便用户进行管理和操作。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.5.5.7. 设备类型删除

点击设备类型右侧更多->删除按钮，删除该设备。

![](images/2ca7483ad675ac46f7ba0ccc98c2e08e4bd23c427b9684cc9fef47b3255e1cea.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个管理界面，列出了三台服务器密码机的详细信息。第一台是云密码机_0088，由三未信安科技股份有限公司提供，类型为云服务器密码机。第二台是服务器密码机_V5，同样由三未信安科技股份有限公司提供，支持安全管理、支持主密钥、支持密钥生成和持读取设备信息等功能。第三台是华为服务器密码机，也由三未信安科技股份有限公司提供。在图片的右上角，有一个“更多”按钮，点击后可以进行新建虚拟机类型、编辑、启用、禁用和删除等操作，其中“删除”选项被红色框标出，表示当前选中的操作。
```
  
图 3-4- 48 设备类型删除

### 3.5.6. 设备组管理

设备组是设备集群，可以通过添加、释放设备来动态管理组内设备并提供给服务使用

#### 3.5.6.1. 设备组列表

打开设备组管理功能，显示设备组信息列表。

![](images/169e5a2470dc66a8073d78b1309683a698651d095ed67554d93b65d446571c63.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示设备组管理界面的截图，界面上方有搜索栏和筛选选项，包括设备组名称、业务类型和区域。下方是一个表格，列出了设备组的详细信息，包括序号、名称、业务类型、区域、支持业务调用、设备类型、设备数量、业务地址、备注、创建时间和操作。表格中有三条记录，分别对应不同的设备组，每条记录都有对应的编辑和删除按钮。
```
  
图 3-4- 47 设备类型启用停用  
图 3-4- 49 设备组信息列表

#### 3.5.6.2. 添加设备组

点击新建按钮，弹出添加设备组界面，输入设备组名称、区域、选择共享模式和是否支持业务调用，如果支持业务调用，需要选择设备类型。

重要字段说明：

支持业务调用：普通的密码机只负责做密码计算，所以默认都选择不支持。只有服务一体机，  
比如时间戳一体机等才支持业务调用  
设备类型：选择了支持业务调用才可以选择设备类型

![](images/3c6043164bc0a874697806d839a55c56c6ba31e1ca64fc0bde56e898701d6054.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新增设备组”界面的截图。界面上有以下内容：

1. **设备组名称**：输入框中填写了“test”。
2. **区域**：选择框中选择了“region1”。
3. **共享模式**：有两个选项，分别是“专享”和“共享”。当前选中的是“共享”。
4. **支持业务调用**：有两个选项，分别是“是”和“否”。当前选中的是“否”。
5. **备注**：有一个文本框，提示“请输入备注”，当前为空。
6. **按钮**：底部有两个按钮，分别是“取消”和“确定”。

这些信息表明用户正在创建一个新的设备组，并设置了相应的参数。
```


![](images/a44ef88700f09e762e328e81a265a821c1a943b6cadaab51f245aaacbeba6fdc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新增设备组”界面的截图。界面上有多个输入框和选项，具体如下：

1. **设备组名称**：输入框中填写了“test2”。
2. **区域**：输入框中填写了“region1”，右侧有一个下拉箭头，表示可以选择其他区域。
3. **共享模式**：有两个单选按钮，分别是“专享”和“共享”。当前选择了“共享”。
4. **支持业务调用**：有两个单选按钮，分别是“是”和“否”。当前选择了“是”。
5. **设备类型**：有一个下拉框，当前显示为“异构设备类型”。
6. **备注**：一个文本输入框，提示“请输入备注”，当前为空。

在界面底部有两个按钮，分别是“取消”和“确定”。

这些信息表明用户正在创建一个新的设备组，并设置了相关的参数。
```
  
图 3-4- 50 添加设备组(不支持业务调用)

#### 3.5.6.3. 编辑设备组

选中一条设备组信息，点击操作列中的编辑按钮，弹出编辑界面，可以编辑设备组名称和备注。

![](images/ec63a6ce805f0fd3b03c3d9dc1effacc989d3dc59139b24a5025d31262828c59.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示设备组编辑界面的截图。界面上有以下内容：

1. **标题**：编辑设备组
2. **设备组名称**：区域一共享设备组（这是一个输入框，用户可以在这里输入设备组的名称）
3. **区域**：region1（这是一个下拉菜单，用户可以选择不同的区域）
4. **共享模式**：有两个选项，专享和共享，当前选中的是共享模式。
5. **支持业务调用**：有两个选项，是和否，当前选中的是否。
6. **备注**：这是一个文本框，用户可以在这里输入备注信息，当前没有输入任何内容。
7. **按钮**：底部有两个按钮，一个是取消，另一个是确定。

这个界面主要用于编辑设备组的信息，包括设备组的名称、所属区域、共享模式、是否支持业务调用以及添加备注等。
```
  
图 3-4- 51 添加设备组(支持业务调用)  
图 3-4- 52 编辑设备组

#### 3.5.6.4. 删除设备组

选中一条设备组信息，点击操作列中的删除按钮，弹出是否确认删除提示，点击确定，删除设备组信息。

![](images/cff42aee484ac83c03d86419c8eb232c22634e56ab01dbfec6f541d0c3890a81.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个设备组管理的界面，界面上有一个表格列出了不同的设备组信息，包括序号、名称、业务类型、区域、支持业务调用、设备类型、设备数量、业务地址、备注、创建时间和操作等字段。当前选中了“区域一共享设备组”这一行，点击了删除按钮，弹出了一个提示框，提示框中写着“删除后将无法恢复，是否确认删除？”并提供了“取消”和“确定”两个选项按钮。
```
  
图 3-4- 53 删除设备组

#### 3.5.6.5. 设备管理

选中一条设备组信息，点击操作列中的设备管理按钮，跳转到设备管理界面。

![](images/b5efcfceef494f2bd6f251e73f7c2127de717851a7f8f0382443d5ca7424187d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示设备管理界面的截图，具体来说是“zuhu01-专享设备组”的设备列表。表格中列出了一个设备的详细信息，包括序号、设备名称、厂商名称、设备类型、管理IP、管理端口、业务IP、业务端口、创建时间和备注。该设备的名称为“vsm_10_20_36_51_443”，由三未信安科技股份有限公司生产，设备类型为云服务器密码机_V5.2.7。管理IP和业务IP均为***********，管理端口为443，业务端口为8008，创建时间为2024年5月11日10时56分38秒。在操作列中，有一个“释放设备”的选项。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

点击绑定设备按钮，弹出界面显示所有可以绑定的设备，选中设备后点击确定按钮，被选择设备会被绑定到当前设备组下。  
注意：签名验签服务器需要独立创建设备组，并且添加的设备只能包含签名验签服务器，且只能给签名验签服务使用。

![](images/8171adee5f542ac6116e77feece95e4341889196401206dca60a47666b485bc7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个设备组管理界面，具体是一个弹出窗口，标题为“请选择密码设备”。窗口中列出了一个名为“vsm_10_20_36_52”的设备，其类型是云服务密码机V5，管理IP地址为***********，所属厂商是三未信安科技股份有限公司，创建时间为2024年5月11日14:41:20。窗口底部有两个按钮，分别是“取消”和“确定”。背景中可以看到设备组管理的其他部分，包括设备名称列表和一些操作选项。
```
  
图 3-4- 54 设备组绑定设备列表  
图 3-4- 55 可绑定设备列表

点击列表右侧的释放设备按钮，会将设备从当前设备组下释放。

![](images/0c8f32bd9bd3a93dabb38d1ba34eab54bb057ec80f48c13bc4fd2500def61048.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个设备管理界面，具体是“zuhu01-专享设备组”的设备列表。界面上有一个表格，列出了设备的详细信息，包括序号、设备名称、厂商名称、设备类型、管理IP、管理端口、业务IP、业务端口、创建时间和备注等。当前显示的设备信息如下：

- 序号：1
- 设备名称：vsm_10_20_36_51_443
- 厂商名称：三未信安科技股份有限公司
- 设备类型：云服务器密码机_V5.2.7
- 管理IP：***********
- 管理端口：443
- 业务IP：***********
- 业务端口：8008
- 创建时间：2024-05-11 10:56:38

在表格下方，有一个弹出的提示框，内容为“是否确认释放设备？”，并提供了“取消”和“确定”两个选项按钮。这表明用户正在尝试释放该设备，并需要进行确认操作。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

## 3.6.服务管理

服务管理负责维护与服务相关的模块，是密码服务平台的核心模块

### 3.6.1. 服务管理

以系统操作员身份登录系统，在服务管理中可查看各个服务的详细信息，并完成服务添加、编辑、启动、停止、删除等操作。

#### *******. 服务列表

打开服务管理菜单，页面展示服务信息列表。

![](images/325d5efec3c40a36cfd0c52ef6838f7c96e23c7ea974035edb674645d41fda8c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个服务管理界面，界面上有一个表格列出了五项服务的信息。
```
  
图 3-4- 56 释放设备  
图3-5-1 服务管理界面

#### 3.6.1.2. 服务添加

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

点击服务列表左上方的新建按钮，弹窗打开新增服务表单，添加服务信息后，点击确定按钮保存服务的信息。服务创建前，请确保该服务类型的镜像已经在镜像管理上传，服务新建会默认拉取最新版本的启用状态下的服务镜像。

重要字段说明：

服务组：在租户创建后会自行创建该租户独享组，或者可在服务组管理中新建服务组

设备组：所有服务都必须选择设备组进行，vpn 和数据库加密服务如果现场不支持密码机可

不选

宿主机：服务容器所在机器，需要提前在宿主机管理中维护

服务规格：根据现场需求选择服务的资源规格

管理端口：服务的管理端口，选择服务类型后自动填充，无需改动

业务端口：服务的业务端口，选择服务类型后自动填充，无需改动

管控端口：管控服务的端口，自动填充，无需改动

新增服务

![](images/f02e6b44cc46d00da6585b65fae65be8ab02127ad7283a7f57e7f23e603d601d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示服务配置界面的图片，界面上有多个输入框和选择框，用于填写服务的相关信息。具体包括：

1. **服务名称**：有一个输入框，提示用户“请输入服务名称”。
2. **服务类型**：有一个下拉选择框，提示用户“请选择”。
3. **服务组**：有一个下拉选择框，提示用户“请选择服务组”。
4. **设备组**：有一个下拉选择框，提示用户“请选择设备组”。
5. **宿主机**：有一个下拉选择框，提示用户“请选择宿主机”。
6. **服务规格**：有一个下拉选择框，提示用户“请选择”。
7. **管理端口**：有一个输入框，提示用户“请输入管理端口”。
8. **业务端口**：有一个输入框，提示用户“请输入业务端口”。
9. **管控端口**：有一个输入框，提示用户“请输入管控端口”。
10. **备注**：有一个多行文本输入框，提示用户“请输入备注”，并且显示当前输入字数为0/100。

在界面的底部有两个按钮，分别是“取消”和“确定”，用户可以点击“取消”来放弃当前操作，或者点击“确定”来提交填写的信息。
```
  
图3-5-2 服务添加界面

#### 3.6.1.3. 服务编辑

点击服务列表右侧操作列 编辑 按钮，弹窗打开编辑服务表单，可以修改服务名称和备注，修改服务信息后，点击确定按钮保存编辑的信息。

![](images/f9b1e93c56f37f579f30e97483f6a13bdbbc7310a2ea8082aa2691d4f310a77a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个服务管理界面，具体来说是一个编辑服务的弹窗。在弹窗中，我们可以看到以下信息：

1. **服务名称**：当前正在编辑的服务名称是“协同签名”。
2. **区域**：服务所在的区域被标记为“region1”。
3. **服务类型**：服务类型是“协同签名服务”。
4. **共享模式**：共享模式有两个选项，分别是“专享”和“共享”，当前选择的是“专享”。
5. **管理IP、业务IP、管控IP**：这些字段都填写了相同的IP地址“************”。
6. **管理端口、业务端口、管控端口**：管理端口和业务端口都是“24500”，而管控端口是“18086”。
7. **备注**：备注栏目前为空，提示用户可以输入备注。

在弹窗的底部有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来取消或确认对服务的编辑。

背景中可以看到服务管理的主界面，列出了多个服务的名称、类型、区域等信息，并且有操作列供用户进行编辑、启动、停止等操作。
```
  
图3-5-3 服务编辑界面

#### *******. 服务启动/停止/重启

点击服务列表右侧操作列 启动/停止 按钮，可启动或停止服务；点击服务列表右侧操作列更多 按钮，再点击 重启 按钮，可重启服务。停止服务后，服务的状态显示未启动；启动服务后，服务的状态显示已启动。

服务类型 Q查询序号 服务名称 服务类型 区域 管理IP 业务IP 管理端口 业务端口 管控IP 管控端口 关联服务组 镜像名称 运行状态 操作

图3-5-4 服务启动/停止/重启界面

#### *******. 服务删除

点击服务列表右侧操作列删除按钮，提示是否确认删除，点击确定删除，删除服务。  
删除服务前，需要先从服务组释放服务。

![](images/9233d730385200f4eadf2303b51fe38d10b09cb3fd008b38bc38c753c57dbea3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个服务管理界面，界面上列出了多个服务的详细信息。这些服务包括数据库加密、协同签名、动态令牌、加解密和kms共享等。每个服务的信息都包含了服务名称、服务类型、区域、管理IP、业务IP、管理端口、业务端口、管控IP、管控端口、关联服务组、镜像名称和运行状态等。在界面的底部，有一个弹出的提示框，提示用户删除后将无法恢复，并询问是否确认删除。这个提示框有两个按钮，一个是“取消”，另一个是“确定”。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### *******. 同步数据库密码

点击服务列表右侧操作列同步数据库密码按钮，可以将平台保存的数据库密码下发到服务。

![](images/41987fb40330f6b7865d627ea7aac68f929671bb245e44e9249c60ad6530e84d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个服务管理界面，界面上列出了五种不同类型的服务：数据库加密、协同签名、动态令牌、加解密和kms共享。每种服务都有其对应的服务名称、服务类型、区域、管理IP、业务IP、管理端口、业务端口、管控IP、管控端口、关联服务组、镜像名称和运行状态等详细信息。当前选中的服务是“动态令牌”，并且有一个弹出的提示框，询问是否确认要同步该服务数据库密码。用户可以选择“取消”或“确定”来响应这个提示。
```
  
图3-5-5 服务删除界面  
图3-5-6 服务同步数据库密码

#### *******. 更新规格

点击服务列表右侧操作列更新规格，可选择不同的服务规格对服务进行扩容和缩容。

![](images/77e2b4fbcb5abb1b5c437db105ea985a0e6fc1196ace443173be5c5149836721.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于服务扩容/缩容的配置界面截图。界面上有多个输入框和选择项，具体包括：

- 服务名称：已填写为“kms_share”。
- 区域：选择了“联通”。
- 服务类型：选择了“密钥管理服务”。
- 共享模式：选择了“共享”，而非“专享”。
- 服务规格：选择了“2C4G”。
- 管理端口、业务端口、管控端口分别填写了“23005”、“23006”、“18086”。

在界面底部有两个按钮，分别是“取消”和“确定”。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.6.2. 服务类型管理

以系统操作员身份登录系统，在服务类型管理中可查看各个服务类型的详细信息。

#### 3.6.2.1. 服务类型列表

打开服务类型管理菜单，页面展示服务类型信息列表。

![](images/4c891910db60374e13933b49130fc0e03eb1f05c13bdb4c79d8a81781cd314ea.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示服务类型管理界面的截图，界面上列出了10种不同的服务类型及其相关信息。每种服务类型都有一个序号、服务类型名称、服务类型简称、管理端口、业务端口、是否允许启停、是否创建管理路由、是否创建业务路由、连接超时（秒）、发送超时（秒）、接收超时（秒）以及操作选项。
```
  
图3-5-7 服务类型管理界面

#### 3.6.2.2. 服务类型编辑

点击服务类型列表右侧操作列 编辑 按钮，弹窗打开编辑服务类型表单，修改服务类型信息后，点击确定按钮保存编辑的信息。

![](images/7ec789654628a249067811a65148c6e5ac5433140474b962214f8b1cc85b00c7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“修改服务类型”界面的截图，界面上有多个输入框和选项。
```
  
图3-5-8 服务类型编辑界面

### 3.6.3. 网关管理

网关管理维护了平台的网关信息，默认的平台网关会自动内置，除区域模式外一般不需进行手动维护。以系统操作员身份登录系统，在网关管理中可查看网关和路由的详细信息，并完成网关添加、编辑、删除、路由管理等操作。

#### 3.6.3.1. 网关列表

打开网关管理菜单，页面展示网关信息列表。

![](images/497d3269d3b6b21cc839f855fd202fce5423bcb1f531bb3174129879bbe0a84c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个网关管理界面，界面上有多个输入框和按钮，以及一个表格。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.6.3.2. 网关添加

点击网关列表左上方的新建按钮，弹窗打开新建网关表单，添加网关信息后，点击确定按钮保存网关的信息。  
网关信息的填写根据实际部署网关的情况填写。

![](images/3e6b8f76713727ebf7ffb4390bca6faf85b79bd86b127eba1430b9fe2e0e2e25.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于新建网关的配置界面截图，界面上有多个输入框和选择框，用于填写网关的相关信息。具体包括：

1. 网关名称：需要输入网关的名称。
2. 网关组件标识：需要输入网关组件的标识。
3. 区域：需要选择网关所在的区域。
4. 网关IP：需要输入网关的IP地址。
5. API端口：需要输入API的端口号。
6. 管理端口：需要输入管理端口的号码。
7. 真实网关IP：需要输入真实网关的IP地址。
8. 真实网关端口：需要输入真实网关的端口号。
9. 反向代理管理节点：需要输入反向代理管理节点的IP和端口。
10. 备注：可以输入一些备注信息。

在每个需要输入信息的地方，都有提示文字，如“请输入网关名称”、“请选择区域”等。在页面底部有两个按钮，分别是“取消”和“确定”，用户可以点击“确定”来保存输入的信息，或者点击“取消”来放弃当前的操作。
```
  
图3-5-9 网关列表界面  
图 3-5-10 网关添加界面

#### *******. 网关编辑

点击网关列表右侧操作列 编辑 按钮，弹窗打开编辑网关表单，可以编辑网关名称、管理端口、备注，修改网关信息后，点击确定按钮保存编辑的信息。

![](images/154d0a03a08ea5a6b4d418bcbe2cf15f84ef9c65e1662a7a9a41a480e6fa2808.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示网关配置信息的图片，具体包括以下内容：

1. 网关名称：联通网关
2. 网关组件标识：hl_cu_busi
3. 网关IP：************
4. API端口：8867
5. 管理端口：9180
6. 真实网关IP：************
7. 真实网关端口：8867
8. 反向代理管理节点：************ 和 8866

此外，还有一个备注栏，目前为空，允许输入最多100个字符。在页面底部有两个按钮，分别是“取消”和“确定”。
```
  
图 3-5-11 网关编辑界面

#### *******. 路由管理

点击网关列表右侧操作列 路由管理 按钮，打开路由管理页面，可查看路由列表和详情。

![](images/8475abc3e9b67e67afac1d937491aed523c9a43b72cbe77098becff26e4bcdff.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个网关管理界面，界面上有一个表格列出了两个网关的信息。
```


图 3-5-12 网关路由管理

#### *******. 路由列表

点击网关列表右侧操作列 路由管理 按钮后，打开路由列表页面，可查看路由列表。

mgt-网关路由管理路由名称 Q查询号 路由名称 路由组件标识 服务类型 所属租户 所属组 URL路径 上游配置 匹配条件 超时时间 操作

#### 3.6.3.6. 路由详情

点击路由列表右侧操作列 详情 按钮后，弹窗打开路由详情表单，可查看路由详情。

![](images/9c4bfbc499e5e03cf921ff917ddf10e2ffb71a2d6fca3ec5dd64173748c82ec4.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个网关路由的详细信息页面，分为基本信息、服务信息和应用信息三个部分。

在基本信息部分，列出了路由名称、路由组件标识、URL路径、超时时间和匹配条件等信息。例如，路由名称为“Test_Tenant02_时间戳服务_Test_Tenant02_业务”，URL路径为“/tsa/**”，超时时间为“connect:60,read:60,send:60”。

服务信息部分展示了一个表格，包含了序号、服务名称、服务类型、所属租户、管理IP、业务IP和管理端口等列。例如，第一行的服务名称为“shijianchuo”，服务类型为“时间戳服务”，所属租户为“租户002”，管理IP和业务IP均为“************”，管理端口为“20300”。

应用信息部分也展示了一个表格，包含了序号、应用标识、应用名称、简称、业务描述和创建时间等列。例如，第一行的应用标识和应用名称均为“allapp”，创建时间为“2023-08-08 15:08:58”。

此外，在基本信息部分下方还有一个上游配置的代码块，包含了hash_on、nodes、pass_host、scheme和type等配置项。
```
  
图 3-5-13 路由列表界面  
图 3-5-14 路由详情界面

#### *******. 网关删除

点击网关列表右侧操作列 删除 按钮，删除网关。

点击网关删除按钮后，提示是否确认删除，点击确定删除。

![](images/14dbef770b8e77ab4d92a9c942e4b95bf55872bc30027c3f9eb9556142627a6e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个网关管理界面，界面上有一个表格列出了两个网关的信息。第一个网关名为“区域一业务网关”，类型为业务，位于region1区域，IP地址为************，网关端口为8867，API端口为9180，真实网关IP和端口与网关信息相同，反向代理管理节点为************:8866。第二个网关名为“mgt1”，类型为管理，没有指定区域，IP地址为************，网关端口为8866，API端口为9180，其他信息为空。在界面的右下角，有一个弹出的提示框，询问是否确认要删除“区域一业务网关”。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.6.4. 数据库管理

该模块维护了平台的数据库信息，租户、服务等模块依赖该模块的数据库信息进行数据库创建以及数据库下发等

#### *******. 数据库列表

系统操作员登录系统后，访问数据库管理功能，可以查询已有数据库信息，包括数据库名称、数据库类型、数据库实例、数据库 IP 端口、完整性校验、备注、创建时间。在列表尾部的操作列表中，gauss、opengauss、达梦、金仓数据库会显示模式管理按钮，MySQL 数据库会显示实例管理按钮。

![](images/3f18413f27746bebe16211a8c72db99e31de9a42ec08f897ec2b1ab2bd7a9c7d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个数据库管理界面，列出了两个数据库的详细信息。第一个数据库名为“dm”，类型为“dm”，位于“region1”区域，IP端口为“***********:5236”，完整性校验状态为“校验通过”，创建时间为“2024-05-11 17:58:24”。第二个数据库名为“区域一mysql”，类型为“mysql”，同样位于“region1”区域，IP端口为“************:3306”，完整性校验状态也为“校验通过”，创建时间为“2024-05-11 10:52:52”。每个数据库条目后都有操作选项，包括模式管理、编辑、修改密码和删除。
```
  
图 3-5-15 网关删除界面  
图3-5-16 数据库管理界面

#### *******. 数据库添加

点击界面中的新建按钮，弹出添加数据库信息界面，输入相关信息，点击确定按钮，数据库添加成功。

注意：

当数据库为mysql 以外的集群时，必须填写多个 ip+端口！这里一定注意，如果 ip 没有填完整，后续是无法从页面进行改动的。

![](images/f0a858aea36dc6818d6a4c000354b69430e57e4473f2ebc01fcb4c50f481d3a1.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于新增数据库的界面截图，界面上有多个输入框和选择框，用于填写数据库的相关信息。具体包括：

1. 数据库名称：需要输入数据库的名称。
2. 区域：需要从下拉菜单中选择一个区域。
3. 数据库类型：当前选择的是MySQL，也可以从下拉菜单中选择其他类型的数据库。
4. 数据库IP端口：需要输入数据库的IP地址和端口号，界面上有两个输入框，分别用于输入IP和端口，右侧有两个按钮，可能用于添加或删除IP端口。
5. 管理员账号：需要输入数据库的管理员账号。
6. 管理员密码：需要输入数据库的管理员密码。
7. 备注：可以输入一些备注信息，最大长度为100个字符。

在界面的底部有两个按钮，分别是“取消”和“确定”，用户可以点击“取消”按钮放弃操作，或者点击“确定”按钮确认并提交填写的信息。
```
  
图 3-5-17 mysql 数据库新增界面

新增数据库

![](images/d6755fd4947d1d153ce4936efb8bf8147d1d07ac5782bb8255d9aaadf1b08aa2.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示数据库配置界面的图片。界面上有多个输入框和选择框，用于填写数据库的相关信息。具体包括：

1. **数据库名称**：有一个输入框，提示用户“请输入数据库名称”。
2. **区域**：有一个下拉选择框，提示用户“请选择”。
3. **数据库类型**：有一个下拉选择框，当前选择的是“gauss”。
4. **数据库IP端口**：有两个输入框，分别提示用户“请输入IP”和“请输入端口”，并且有一个加号按钮，可能用于添加更多的IP端口信息。
5. **管理员账号**：有一个输入框，提示用户“请输入管理员账号”。
6. **管理员密码**：有一个输入框，提示用户“请输入管理员密码”。
7. **实例库名称**：有一个输入框，提示用户“请输入实例库名称”。
8. **备注**：有一个多行文本输入框，提示用户“请输入备注”，并且显示当前输入字数为0/100。

在界面的底部，有两个按钮，分别是“取消”和“确定”。
```
  
图 3-5-18 gauss 数据库新增界面

#### *******. 数据库模式管理/实例管理

点击数据库列表尾部的模式管理（GaussDB、DM、kingbase）或实例管理（MySQL）按钮，打开数据库模式或实例信息展示界面。

![](images/22ac55333a329a6bb10bea49bcdfd8de82e908e692a04bcabfc3e2b22c1fa3dd.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个名为“znstest数据库测试-实例管理”的界面，看起来像是一个数据库管理系统的操作页面。页面上有一个表格，列出了10个数据库实例的信息，包括序号、实例名称、数据库类型、服务类型、完整性校验和备注以及创建时间。

所有列出的实例都使用了MySQL作为数据库类型，并且每个实例都有不同的服务类型，例如加解密服务、签名验证服务、密钥管理服务等。每个实例的完整性校验状态都显示为“校验通过”，并且备注栏都是空的。创建时间显示这些实例都是在2023年7月27日的不同时间点创建的。

此外，页面顶部有一个搜索框，可以输入实例名称进行查询，右侧有查询和重置按钮。页面底部显示当前是第1页，共10条记录，每页显示20条。
```
  
图3-5-19 数据库实例管理界面

![](images/489796da676d6a77c1c22e69dda5894b9cc307c7f337451d876f347e884d8db0.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个名为“Auto_testGaussDB - 模式管理”的界面，看起来像是数据库管理系统的一部分。界面上有一个表格，列出了不同模式的详细信息，包括序号、模式名称、数据库类型、服务类型、完整性校验状态和备注，以及创建时间。

从表格中可以看到，共有60条记录，当前显示的是第1页的前15条记录。每条记录都包含一个唯一的模式名称，例如“ccsp_sms_5899071379581506973”，并且所有记录的数据库类型都是“Gauss”。服务类型包括协同签名服务、动态令牌服务、数据库加密服务、文件加密服务、电子签章服务、SSL VPN加密通道服务、加解密服务、签名验签服务、密钥管理服务和时间戳服务等。

在完整性校验一栏，所有的记录都显示为“校验通过”，表明这些模式的完整性已经得到了验证。备注一栏目前为空白，没有提供额外的信息。创建时间显示了每条记录创建的具体时间，例如第一条记录的创建时间为2023-07-28 19:00:00。

此外，界面底部有分页导航，允许用户查看其他页面的记录。
```
  
图3-5-20 数据库模式管理界面

#### 3.6.4.4. 数据库编辑

点击数据库列表尾部的编辑按钮，弹出数据库信息编辑界面，输入数据库信息，点击确定按钮，编辑成功。  
只允许修改数据库名称和备注。

![](images/cec84110e848553f0d544bcd4e76e046a4001ac7ccc79d3d1a6c714c0d6bb1b7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个数据库管理界面，具体是一个编辑数据库的弹窗。弹窗中有几个输入字段：

1. 数据库名称：当前填写的是“autoAddKingbase”。
2. 区域：选择框中显示的是“region”。
3. 数据库类型：选择框中显示的是“kingbase”。
4. 数据库IP端口：有两个输入框，分别填写了“***********”和“54321”。

此外，还有一个备注字段，但目前没有填写任何内容。

弹窗底部有两个按钮：“取消”和“确定”，用户可以通过点击这两个按钮来取消或确认编辑操作。

背景中可以看到一个数据表格，列出了序号、数据库名称和数据库类型等信息。当前只有一条记录，数据库名称为“autoAddKingbase”，数据库类型为“kingbase”。右侧还有一些操作按钮，如“刷新”、“重置”等。
```


#### *******. 数据库删除

点击数据库列表尾部的删除按钮，弹出是否确认删除提示，点击确认按钮，删除数据库信息。

![](images/ceceba78e4ef52ba1d547a4992029e7bcebda7f816ab862bb24873ae8f9535e5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个数据库管理界面，具体来说是一个名为“数据库管理”的页面。界面上方有一个搜索栏，用户可以输入数据库名称和IP地址进行查询。页面主体部分是一个表格，列出了多个数据库的信息，包括序号、数据库名称、数据库类型、实例库名称、数据库IP端口、完整性校验、备注、创建时间以及操作选项。

在表格中，我们可以看到四个数据库的详细信息：
1. 数据库名称为Auto_test_DB，数据库类型为gauss，实例库名称为test04，数据库IP端口为***********:5432，完整性校验状态为校验通过，备注为自动化测试预置数据，创建时间为2023-08-07 11:53:52。
2. 数据库名称为Auto_test_DBeeeee，数据库类型为gauss，实例库名称为空，数据库IP端口为空，完整性校验状态为校验通过，备注为空，创建时间为2023-08-03 19:52:26。
3. 数据库名称为TestDB01，数据库类型为gauss，实例库名称为空，数据库IP端口为空，完整性校验状态为校验通过，备注为空，创建时间为2023-08-03 11:42:00。
4. 数据库名称为test_pki，数据库类型为gauss，实例库名称为空，数据库IP端口为空，完整性校验状态为校验通过，备注为空，创建时间为2023-08-03 11:40:55。

每个数据库条目后面都有操作选项，包括模式管理、编辑和删除。当前，用户正在尝试删除一个数据库，弹出的提示框询问是否确认删除，警告删除后将无法恢复。用户可以选择取消或确认删除操作。
```
  
图3-5-21 数据库编辑界面

#### *******. 数据库修改密码

点击数据库列表尾部的修改密码按钮，弹出修改密码界面，输入原始密码和新密码，点击确定按钮，修改数据库密码。修改数据库密码为修改密码后的密码同步，不可直接修改数据库密码。

![](images/475a1eeb140f40880b5c9584b25a6199d1769cdaf9c31520100af15c33ef9ec8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个数据库管理界面，当前正在打开一个修改密码的弹窗。弹窗中有三个输入框，分别用于输入旧密码、新密码和确认新密码。弹窗底部有两个按钮，一个是“取消”，另一个是蓝色的“保存”按钮。

在背景中，可以看到数据库管理页面的部分内容。页面顶部有一个标签栏，当前选中的标签是“数据库管理”。页面左侧有一个“新建”按钮，右侧有两个操作按钮：“查询”和“重置”。

页面中间部分是一个表格，列出了数据库的相关信息，包括序号、数据库名称、数据库类型等。表格中有两条记录，分别是名为“dm”的数据库和名为“区域一-mysql”的数据库。每条记录后面都有“实例管理”、“编辑”、“修改密码”和“删除”等操作选项。

整体来看，这个界面用于管理和操作数据库，当前正在进行密码修改的操作。
```
  
图3-5-22 数据库删除界面  
图3-5-23 修改数据库密码

### 3.6.5. 服务组管理

服务组是服务集群，可以通过释放和添加服务来动态管理服务组。在非单租户模式下，创建租户后，会自动创建一个该租户的独享服务组。

#### 3.6.5.1. 服务组列表

打开服务组管理功能，显示服务组信息列表，可以通过服务组标识、服务组名称、区域搜索列表信息。共享服务组如果绑定KMS 共享服务组，会在列表KMS 服务组列显示对应的KMS服务组，否则不显示。

![](images/1ff2932009dba07bbdf84831b1fcbbeece601861a87257b8685b4172c60bfa9b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个服务组管理界面，界面上有一个表格列出了五个服务组的信息。每个服务组的信息包括序号、服务组标识、服务组名称、区域、所属租户、共享模式、KMS服务组、业务类型、服务数量、备注、状态和操作。例如，第一个服务组的标识是lwbtest，名称也是lwbtest，位于广电机房-政务外网区域，所属租户是tenant_ccn，共享模式为专享，业务类型是数据库加密，服务数量为0，状态为创建完成，操作选项有编辑、服务管理和删除。其他服务组的信息也以类似的方式列出。
```
  
图3-5-24 服务组信息列表

#### 3.6.5.2. 添加服务组

点击列表上方的新建按钮，弹出服务组新建界面，输入服务组信息，点击确定按钮，新建成功。  
如果新建共享服务组，需要选择业务类型，其中加解密服务和签名验签服务类型需要选择共享的KMS 服务组。

![](images/44014f18ba5c8de40d162954e729720a0d7283310d5a04eef08edb3e3f9fbf4b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新增服务组”界面的截图。界面上有多个输入框和选项，具体如下：

1. **服务组标识**：输入框中填写了“zuhu01_group1”。
2. **服务组名称**：输入框中填写了“zuhu01_group1”。
3. **区域**：下拉菜单中选择了“region1”。
4. **共享模式**：有两个选项，“专享”和“共享”，当前选中的是“专享”。
5. **所属租户**：下拉菜单中选择了“zuhu01”。
6. **数据库**：下拉菜单中选择了“区域一mysql”。
7. **备注**：输入框中提示“请输入备注”，目前为空。

在界面底部有两个按钮：“取消”和“确定”。
```


![](images/b3cfe4841513aa7a638e426b433a2b83663050c97de7fbdb8c3db1adcd41052b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新增服务组”界面的截图，界面上有多个输入框和选择项，具体包括：

- 服务组标识：已填写为“pki_share”
- 服务组名称：已填写为“pki_share”
- 区域：已选择“region1”
- 共享模式：选择了“共享”选项
- 业务类型：已选择“数据加解密”
- KMS服务组：已填写为“kms_share”
- 数据库：已选择“区域一mysql”
- 备注：提示“请输入备注”，目前为空

在界面底部有两个按钮，分别是“取消”和“确定”。
```
  
图3-5-25 新建专享服务组

#### 3.6.5.3. 编辑服务组

点击列表右侧操作列中的编辑按钮，弹出编辑界面，可以修改服务组名称和备注，点击确定按钮，修改成功

![](images/2afa4fc78d6df75251aa1a7fc330f209cfbd1e33d72f7bc2b3b260031f1648d6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“编辑服务组”界面的截图，界面上有多个输入框和选择项。具体包括：

- 服务组标识：已填写为“pki_share”
- 服务组名称：已填写为“pki_share”
- 区域：已选择“region1”
- 共享模式：选择了“共享”，未选择“专享”
- 业务类型：已选择“数据加解密”
- 数据库：已选择“区域一-mysql”
- 备注：提示“请输入备注”，目前为空白

在界面底部有两个按钮，分别是“取消”和“确定”。
```
  
图3-5-26 新建共享服务组  
图 3-5-27 编辑服务组

#### 3.6.5.4. 删除服务组

点击列表右侧操作列中的删除按钮，可以删除服务组信息。

![](images/caf3630d5554efda88926de2593930abb57ef13df5cbb8430642ba1ff1fe150d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个服务组管理界面，界面上有一个弹出的提示框，询问用户是否确认要删除名为“pki_share”的服务组。背景中可以看到一个表格，列出了多个服务组的信息，包括序号、服务组标识、服务组名称、区域、业务类型、服务数量、备注、创建时间、状态和操作选项。当前选中的服务组是“pki_share”，其业务类型为数据加解密，服务数量为0，创建时间为2024年5月11日17:11:53，状态为创建完成。
```
  
图 3-5-28 删除服务组

#### *******. 服务管理

进入服务组管理功能，点击服务组信息列表右侧操作列中的服务管理按钮，跳转至服务管理界面，显示该服务组下的服务信息列表。

![](images/94f9f9018b89bbb643adc26d7844efa4596b910893983b3dd913427e08ad742f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示服务列表的截图，具体信息如下：

- **服务名称**：加解密
- **服务类型**：加解密服务
- **服务IP**：***********
- **业务端口**：20001
- **创建时间**：2024-05-11 17:19:01
- **备注**：无
- **运行状态**：已启动
- **操作**：释放服务

此外，页面顶部显示了许可证有效期为2034年5月11日，并且当前用户为“操作员 | oper”。
```
  
图3-5-29 服务组下服务信息列表

点击服务信息列表右侧的释放服务按钮，可以将服务从此服务组下释放。注意释放后的服务将无法再被使用。

![](images/775e0d097d3d20ae928ce61a11655bfc14a76eab9e2bbf635b7ea03ef0e0cfb7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“pki_share - 服务列表”的界面，属于服务组管理的一部分。界面上列出了一个名为“加解密”的服务，其类型为加解密服务，服务IP为***********，业务端口为2001，创建时间为2024-05-11 17:19:01，当前运行状态为已启动。

在界面的右下角，有一个弹出的提示框，内容为：“释放服务将会重新初始化该服务，是否确认释放？” 提示框中有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来选择是否进行服务的释放操作。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.6.6. 镜像管理

服务使用容器化技术启动，该模块维护了所有类型服务的容器镜像，在进行服务创建前，需要将对应服务类型的镜像进行上传

#### *******. 镜像列表

打开镜像管理功能，显示镜像列表信息，可根据镜像名称和服务类型过滤查询。

![](images/adb09d619601bdbeb093230c7fb18873ffae55331f497fa5ed464702e477f3c0.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“镜像管理”的网页界面，主要用于管理和查看各种服务的镜像信息。界面上方有一个搜索栏，用户可以输入镜像名称和选择服务类型进行查询。右侧有两个按钮，分别是“查询”和“重置”。

在页面的主要部分，有一个表格列出了多个镜像的详细信息。表格的列标题包括序号、文件名称、服务类型、镜像名称、镜像版本、文件大小（MB）、完整性校验、创建时间、备注、状态和操作。

表格中列出了六个镜像的信息：
1. image-ccsp-secdb-openeuler-x86：数据库加密服务，镜像版本3.4.0.2，文件大小674MB，状态为启用。
2. image-ccsp-sms-openeuler-x86：协同签名服务，镜像版本4.6.5.1，文件大小561MB，状态为启用。
3. image-ccsp-secauth-openeuler-x86：动态令牌服务，镜像版本3.2.2.1，文件大小566MB，状态为启用。
4. image-ccsp-kms-openeuler-x86：密钥管理服务，镜像版本3.3.1.4.30，文件大小1056MB，状态为启用。
5. image-ccsp-pki-openeuler-x86：加解密服务，镜像版本3.3.1.4.30，文件大小772MB，状态为启用。
6. image-ccsp-tsc-openeuler-x86：电子签章服务，镜像版本3.3.1.2，文件大小820MB，状态为启用。

每个镜像的状态都显示为“启用”，并且在操作列中提供了编辑、启用、禁用和删除的选项。
```
  
图3-5-30 服务组释放服务  
图 3-5-31 镜像管理列表

#### 3.6.6.2. 上传镜像

点击上传按钮，弹出上传镜像界面，选择本地镜像文件，输入镜像文件摘要值（SHA256），备注信息。

![](images/c18d25c4cbb24f739930a76ae2b806bc54f7d71e55b303c9e3f6a650c45929f2.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个文件上传界面，具体来说是用于上传镜像文件的。界面上有一个提示框，里面写着“将文件拖到此处，或点击上传”，并有一个云朵图标和一个向上的箭头，表示可以将文件拖放到这里或者点击进行上传。下方有一行小字说明，指出只能上传.tar.xz格式的文件，并且文件大小不能超过3GB。在说明下方，有一个已经选择好的文件名“image-ccsp-kms-openeuler-x86-3.3.1.4.30.tar.xz”。再往下是一个标有“* 文件摘要”的区域，里面显示了一串字符，这可能是文件的哈希值或摘要信息。接着是一个备注输入框，提示用户可以在此处输入备注信息，目前这个框内为空。最后，在页面底部有两个按钮，一个是灰色的“取消”按钮，另一个是蓝色的“确定”按钮，用户可以通过点击这两个按钮来取消操作或确认上传。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.6.6.3. 编辑镜像

选中一条镜像信息，点击右侧操作栏中的编辑按钮，弹出镜像编辑界面，可以编辑镜像的备注信息。

![](images/8c2d264d9b3f9dadfe0f5bd46f377a05dcbcd228b4f33383973db16c21d3df6e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“镜像管理”的网页界面，主要用于管理和编辑镜像。界面上有一个弹出窗口，标题为“编辑镜像”，其中包含一个文本框用于输入备注信息，以及两个按钮：“取消”和“确定”。在弹出窗口的背景中，可以看到一个表格列出了多个镜像文件的信息，包括序号、文件名称、服务类型、版本号、大小、校验状态、创建时间、备注、状态和操作选项。每个镜像文件的状态都显示为“启用”，并且提供了编辑、启用、禁用和删除的操作选项。
```
  
图 3-5-32 上传镜像  
图 3-5-33 编辑镜像

#### 3.6.6.4. 镜像启用、禁用

选中一条镜像信息，点击右侧操作栏中的启用、禁用按钮，可以将镜像状态置为启用、禁用状态。禁用状态的镜像不能用于创建服务实例，启用后，可以创建服务实例。

镜像名称 服务类型 Q查询序号 文件名称 服务类型 镜像名称 镜像版本 文件大小(MB) 完整性校验 创建时间 备注 状态 操作偏轴

#### 3.6.6.5. 删除镜像

选中一条镜像信息，点击右侧操作栏中的删除按钮，删除镜像信息，只有禁用状态的镜像可以删除。

![](images/acaf0e68a1c626f3bbdec492a8e1c9ec9d7641fc3d6e6e68a6deaef6a4d80466.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个镜像管理界面，界面上列出了多个镜像文件的详细信息。每个镜像文件的信息包括序号、文件名称、服务类型、镜像名称、镜像版本、文件大小（MB）、完整性校验、创建时间、备注、状态和操作选项。当前选中的镜像是“image-ccsp-tsc-openeuler”，它提供电子签章服务，镜像版本为3.4.0.2，文件大小为674MB，状态为未启用。

在界面的中央，有一个弹出的提示框，内容是“删除后将无法恢复，是否确认删除？”，这表明用户正在尝试删除选中的镜像文件，并且系统要求用户确认这一操作。提示框中有两个按钮：“取消”和“确定”，用户可以通过点击这两个按钮来选择是否继续执行删除操作。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图3-5-34 启用、禁用镜像  
图 3-5-35 删除镜像

### 3.6.7. 宿主机管理

该模块维护了服务所在宿主机信息，在添加宿主机之前，请确保服务环境已经在该机器部署完毕。系统操作员具有宿主机管理的权限，可对宿主机信息进行新增、编辑和删除。

#### *******. 宿主机列表

点击宿主机管理菜单，界面展示宿主机列表。

![](images/7c04903e78f7da0bca24554b3cbd69626a05d118ba941355e4af5957c8514e72.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个宿主机管理界面，列出了三台宿主机的详细信息。每台宿主机的信息包括序号、宿主机名称、IP地址、CPU架构、CPU核数、内存大小、磁盘大小、操作系统、备注、创建时间和操作选项。具体信息如下：

1. 第一台宿主机：
   - 宿主机名称：ccn_host
   - IP地址：***********
   - CPU架构：arm
   - CPU核数：16核
   - 内存大小：32GB
   - 磁盘大小：100GB
   - 操作系统：UOS
   - 创建时间：2024-09-04 10:13:28

2. 第二台宿主机：
   - 宿主机名称：cu_host
   - IP地址：************
   - CPU架构：arm
   - CPU核数：16核
   - 内存大小：32GB
   - 磁盘大小：100GB
   - 操作系统：UOS
   - 创建时间：2024-09-04 10:13:05

3. 第三台宿主机：
   - 宿主机名称：cm_host
   - IP地址：************
   - CPU架构：arm
   - CPU核数：16核
   - 内存大小：32GB
   - 磁盘大小：100GB
   - 操作系统：UOS
   - 创建时间：2024-09-04 10:12:30

每台宿主机的操作选项包括“编辑”和“删除”。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### *******. 宿主机新增

点击宿主机列表左上角的新建按钮，输入宿主机信息点击确定。

重要字段说明：

cpu 架构：该选项要根据实际情况选择，否则会导致无法正常拉取服务镜像cpu 核数、 存大小：如实填写，创建服务会根据该信息进行剩余资源校验

![](images/03d559f1fc5d473db544e37edc71affdf9d642ddc3905479dfea718c6b99fd3e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于新增宿主机的配置界面截图。界面上有多个输入框和选项，用于填写宿主机的相关信息。具体包括：

1. 宿主机名称：有一个输入框，提示用户请输入宿主机名称。
2. CPU架构：有两个单选按钮，分别是arm和x86，当前选择的是arm。
3. IP：有一个输入框，提示用户请输入IP。
4. CPU核数：有一个输入框，提示用户请输入CPU核数，并且旁边有一个标签显示“核”。
5. 内存大小：有一个输入框，提示用户请输入内存大小，并且旁边有一个标签显示“GB”。
6. 磁盘大小：有一个输入框，提示用户请输入磁盘大小，并且旁边有一个标签显示“GB”。
7. 操作系统：有一个输入框，提示用户请输入操作系统。
8. 备注：有一个多行文本框，提示用户请输入备注，并且右下角显示当前输入字数为0/100。

在界面的底部有两个按钮，分别是“取消”和“确定”，用户可以点击这两个按钮来取消或确认操作。
```
  
图 3-5-36 宿主机列表

#### *******. 宿主机编辑

点击宿主机列表数据后的编辑按钮，进入编辑界面。可修改宿主机的名称以及备注。

![](images/681dfdb7f25406149f3bbc1d2994cee900e6cba513724402c51e0454250936de.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“编辑宿主机”界面的图片，具体内容如下：

1. **宿主机名称**：输入框中填写了“ccn_host”。
2. **CPU架构**：选择了“arm”，旁边还有一个未选中的“x86”选项。
3. **IP**：输入框中填写了“***********”。
4. **CPU核数**：输入框中填写了“16”，单位是“核”。
5. **内存大小**：输入框中填写了“32”，单位是“GB”。
6. **磁盘大小**：输入框中填写了“100”，单位是“GB”。
7. **操作系统**：输入框中填写了“UOS”。
8. **备注**：输入框中提示“请输入备注”，目前为空。

在界面底部有两个按钮，分别是“取消”和“确定”。
```
  
图3-5-37 新增宿主机界面

#### *******. 宿主机删除

点击宿主机列表数据后的删除按钮，可删除该条记录。

![](images/447a5b47f3ec591054233607885f88851f788c4b0e2f292808929a5cce60a21f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“宿主机管理”的网页界面，用于管理和查看宿主机的信息。界面上有一个搜索栏，可以输入宿主机名称和IP进行查询，并有“查询”和“重置”按钮。下方是一个表格，列出了宿主机的详细信息，包括序号、宿主机名称、IP、CPU架构、CPU核数、内存大小、磁盘大小、操作系统、备注、创建时间和操作选项。表格中有三条记录，每条记录都显示了宿主机的名称、IP地址、CPU架构为arm、CPU核数为16核、内存大小为32GB、磁盘大小为100GB、操作系统为UOS，以及创建时间。每条记录的操作列提供了“编辑”和“删除”两个选项。
```
  
图3-5-38 编辑宿主机界面  
图3-5-39 删除宿主机按钮

## 3.7.用户管理

系统操作员具有用户管理的权限，系统管理员具有流程审核的权限，使用系统初始化时添加的系统操作员登录平台后进行用户管理功能操作，使用系统初始化时添加的系统管理员登录平台后进行流程审核功能操作。

### 3.7.1. 人员管理配置

#### *******. 登录配置展示

系统管理员登录，点击系统管理-》人员管理配置，打开登录配置菜单，页面展示登录配置参数。

![](images/a1e0943fa81923fad6782b9d8d850bcef7b3bcd4297c129b7a7c10cb53d9a572.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个登录配置界面，包含了多个与用户登录相关的设置选项。具体包括：

1. 是否开启口令登录：设置为“是”，表示允许使用口令进行登录。
2. 是否开启UKey登录：设置为“否”，表示不允许使用UKey进行登录。
3. 长时间未登录禁用账户：设置为“365天”，表示如果账户在365天内没有登录，将会被禁用。
4. 口令有效期告警：设置为“1天”，表示在口令到期前1天会发出告警通知。
5. 登录失败锁定时长（分钟）：设置为“5分钟”，表示如果登录失败，账户将在接下来的5分钟内被锁定。
6. SIM盾登录配置：设置为“否”，表示不允许使用SIM盾进行登录。
7. 默认口令：显示为“******”，表示默认口令已被隐藏。
8. 历史口令限制：设置为“1次”，表示用户不能重复使用最近一次使用过的口令。
9. 口令有效期：设置为“365天”，表示口令的有效期为365天。
10. 登录失败次数限制：设置为“5次”，表示如果连续登录失败5次，账户将被锁定。
11. 是否强制修改默认口令：设置为“否”，表示用户可以选择是否修改默认口令。

这些设置旨在提高系统的安全性和用户体验。
```
  
图3-7-5 人员管理配置页面

#### 3.7.1.2. 修改登录配置

点击登录配置项右侧修改图标，弹窗输入对应的内容或选择配置项，点击保存。

![](images/4aad55bdc80514c9ec727499d985952749f4f6b8e1aeb286aab5e10fd00b9a24.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个系统配置界面，具体是关于人员管理配置的登录配置部分。界面上有一个弹出窗口，标题为“修改配置参数”，内容是关于是否开启口令登录的选项，当前选择的是“是”。弹出窗口下方有两个按钮，分别是“取消”和“保存”。

在弹出窗口的背景中，可以看到一些其他的登录配置选项，包括：
- 是否开启UKey登录：是
- 长时间未登录禁用账户：365天
- 口令有效期：90天
- 口令有效期告警：30天
- 登录失败次数限制：5次
- 登录失败锁定时长（分钟）：30分钟
- 是否强制修改默认口令：否

这些配置选项允许管理员根据需要调整系统的登录安全设置。
```
  
图3-7-6 修改是否开启口令登录

![](images/713b9be5a284105d9dd9cf40d4137146935c590b07408071abfacf5e308ee4a2.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个系统管理界面，具体是“人员管理配置”页面。页面左侧列出了登录配置的多个选项，包括是否开启口令登录、是否开启UKey登录、长时间未登录禁用账户的时间、口令有效期告警时间、登录失败锁定时长等。右侧弹出一个“修改配置参数”的对话框，当前正在修改的是“口令有效期”，设置为90天，并提供了“取消”和“保存”两个按钮供用户选择。此外，页面底部还显示了其他配置信息，如口令有效期、登录失败次数限制以及是否强制修改默认口令等。
```
  
图 3-7-7 修改口令有效期

### 3.7.2. 用户管理

以系统管理员身份登录平台，在用户管理中可查看每个用户的详细信息，并完成用户启用、禁用、解锁、重置密码、设置用户有效期、设置UKey、删除等操作。

![](images/6270e205caaceac96260566bd9ce63bbd5931a4b61204afebb67842bdc33264d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示用户信息管理界面的截图，界面上方有“首页”和“用户信息”的标签，用户信息标签被选中。在用户信息页面中，有一个表格列出了用户的详细信息，包括序号、账号名、姓名、所属角色、所属租户、账号有效期、状态和操作选项。表格中有12条记录，每条记录都显示了用户的账号名、姓名、角色（如租户审计员、租户操作员、租户管理员等）、所属租户、账号状态（所有账号状态均为“启用”）以及操作选项（包括启用、禁用、重置和更多）。此外，页面顶部还有搜索框，可以输入账号名或姓名进行查询，以及一个重置按钮。页面底部显示了当前页码和总页数，当前显示的是第1页，共1页。
```
  
图3-6-1 用户信息页面

#### 3.7.2.1. 设置账户有效期

点击列表中右侧操作列更多->设置有效期按钮，弹窗选择账号有效期起止时间，点击保存，系统保存该账号有效期。

设置完成之后，该账户只允许在有效期内登录平台成功。过期之后，该账户将被禁用。

![](images/5f956cd048c300bc3547524ca55e40d66f9e6127a2eb663a2af5f5ead5f36793.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户信息管理界面，具体包括以下内容：

1. **用户信息列表**：
   - 列表中有四个用户，分别是`audit`、`audit`、`oper`和`admin`。
   - 每个用户的信息包括序号、账号名、姓名、所属角色、所属租户和账号有效期。
   - `audit`用户的姓名是`auditt_edit`，角色是租户审计员，所属租户是`Test_Tenant01`。
   - 另一个`audit`用户的姓名是`审计员-02`，角色也是租户审计员，所属租户是`Test_Tenant02`。
   - `oper`用户的姓名是`操作员-pki`，角色是租户操作员，所属租户是`Test_pki`。
   - `admin`用户的姓名是`管理员-pki`，角色是租户管理员，所属租户是`Test_pki`。

2. **状态和操作**：
   - 每个用户的状态都是“启用”。
   - 操作列提供了禁用、重置和更多选项。

3. **设置有效期弹窗**：
   - 弹窗标题为“设置有效期”，包含有效期的输入框，可以选择开始时间和结束时间。
   - 弹窗底部有“取消”和“确定”按钮。

4. **其他功能**：
   - 页面顶部有“首页”和“用户信息”标签。
   - 右上角有查询和重置按钮。

这个界面主要用于管理和配置用户的角色、权限和有效期等信息。
```
  
图 3-6-2 设置账户有效期

#### 3.7.2.2. 设置账户 UKey

点击列表中右侧操作列更多->设置 UKey 按钮，弹窗输入 UKey 口令，电脑插上 USB-Key设备，点击确定，验证UKey 口令成功后，系统关联当前用户与UKey 的绑定。

![](images/ea2a2ca7f59ba96bcf0511323b50521c960428cac72b126443ed93f53336f92e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户信息管理界面，具体来说是一个设置UKey的弹窗。弹窗中有输入UKey口令的文本框和“确定”、“取消”两个按钮。背景中可以看到一个表格，列出了用户的账号名、姓名、所属角色、所属租户、账号有效期、状态以及操作选项。表格中的用户包括审计员、操作员和管理员等不同角色，状态均为启用，并且每个用户后面都有启用、禁用、重置和更多操作的选项。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.7.2.3. 编辑账户

以租户操作员身份登录，单击列表中右侧操作列编辑按钮，弹出用户信息编辑界面，可以修改用户的姓名和备注

![](images/545e8ea9965949cb3514ac2f70928098a7cb381c6c5268e4a98f3672a7bbf107.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户信息管理界面，具体来说是一个修改账号信息的弹窗。弹窗中有几个输入框和选择框，包括账号名、姓名、所属角色和备注等字段。账号名和姓名是必填项，用星号标记。所属角色有一个下拉菜单供选择，当前选中的是“租户管理员”。备注栏可以输入一些额外的信息。

在弹窗的底部有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来取消或确认修改操作。

背景中可以看到一个用户列表，列出了三个用户的账号名、姓名和所属角色，分别是“audit”（审计员）、“oper”（操作员）和“admin”（管理员），他们都属于“租户”角色。右侧还有一些操作选项，如编辑、重置和更多操作，以及创建时间、状态等信息。当前选中的状态是“启用”，并且有一个红色的框标出了这个状态。
```
  
图 3-6-4 编辑账户

#### 3.7.2.4. 启用、禁止、解锁、重置用户

点击列表中右侧操作列启用按钮，用户被置为启用状态。当前已启用的人员不可再次启用。点击列表中右侧操作列禁用按钮，用户被置为禁用状态。当前已禁用的人员不可再次禁用。当登录次数超过特定次数要求后，用户将被锁定，登录平台管理员进入系统，可以解锁。点击列表中锁定状态的用户右侧操作列解锁按钮，用户被置为启用状态。当前非锁定状态的人员不可解锁。

点击列表中右侧操作列重置按钮，可以重置用户的登陆密码。

![](images/11ea6d540bd8a2383b5e6f2bde2afee3a3eb9351f3f34530ad990812785dad45.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户信息管理界面，包含一个表格和一些操作按钮。表格列出了用户的序号、账号名、姓名、所属角色、所属租户、账号有效期和状态等信息。在“操作”列中，有多个选项，如启用、禁用、重置、更多等，用于对用户进行不同的管理操作。例如，对于第一个用户“audit”，可以进行启用、禁用、重置和更多操作；对于第二个用户“audit”，除了上述操作外，还可以进行解锁操作。此外，还有一些用户可以设置有效期、设置UKey或删除等操作。
```
  
图 3-6-3 设置账户 UKey  
图3-6-5 账户操作按钮

### 3.7.3. 用户注册

#### 3.7.3.1. 用户注册

系统操作员登录平台，在人员管理-用户信息界面，点击用户信息列表左上方的注册按钮，跳转到注册账号页面，输入内容包含账户名，姓名、备注，其中账户名、姓名是必填项。

![](images/a74854e559cc3b86314bbe84b7f7a80ae8afc86a9b92f084d6624c5cb49ea31f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示注册账号界面的图片。界面上有三个输入框，分别用于输入账号名、姓名和备注信息。账号名和姓名是必填项，用红色星号标记。备注信息是一个多行文本框，可以输入最多100个字符。在界面底部有两个按钮，一个是灰色的“取消”按钮，另一个是蓝色的“确定”按钮。
```
  
图 3-6-6 用户注册

UKey 登录模式下，用户注册时需要插入本次用户注册的 UKey，并输入 UKey 口令进行校验。

![](images/8b549e82bbac25acb5fd3d1418e3037e29a03612beaece838e637530e01e16d0.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示注册账号界面的图片。界面上有四个输入框，分别用于输入账号名、姓名、UKey口令和备注信息。每个输入框旁边都有相应的提示文字，指示用户需要填写的内容。其中，账号名、姓名和UKey口令是必填项，用红色星号标记。备注信息是一个可选的输入框，用户可以在这里输入最多100个字符的备注内容。在界面的底部有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来取消或确认注册操作。
```
  
图 3-6-7 用户 UKey 注册

#### 3.7.3.2. 用户注册记录删除

系统操作员登录系统后，打开注册记录功能，显示用户注册记录列表，点击列表右侧的删除按钮，可以删除注册记录。

![](images/75b1a45e99e2f06a15091df9e3bcd525ba48f09b01a8d6846ab5e70aa92cb352.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个用户账户审核的界面，界面上方有搜索栏，可以输入账号名和姓名进行查询，并且可以选择审核状态。下方是一个表格，列出了序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见、完整性校验以及操作等信息。在表格中，有一条记录显示账号名为“fanji”，姓名为“fanji”，申请人是“oper”，申请时间为2023年8月15日18:25:55，审核状态为“已通过”，审核时间为2023年8月15日18:26:10，审核人为“admin”，审核意见为“1”，完整性校验为“审核通过”，并且有“删除”操作按钮。右下角显示当前页面共有1条记录，每页显示20条，当前为第1页，共1页。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/bc3aa3421569b12d4405c71b084be8982de56117dbb3733b85fd7e17235121de.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户界面，其中包含一个表格和一个弹出的提示框。表格列出了用户的详细信息，包括序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见、完整性校验和操作等字段。表格中有一条记录，显示了账号名为“fangj”的用户信息，包括申请时间为2023-08-15 18:25:55，审核状态为“已通过”，审核时间为2023-08-15 18:26:10，审核人为“admin”，完整性校验为“校验通过”。

弹出的提示框位于表格下方，标题为“提示”，内容为“是否确认删除？”，并有两个按钮：“取消”和“确认”。这表明用户正在尝试删除某条记录，并需要确认这一操作。
```
  
图3-6-8 用户注册记录页面

#### 3.7.3.3. 修改密码

所有用户登录后，在右上角均有退出按钮，并显示当前登录人账号和所属角色。点击用户信息，右上角有修改密码按钮。

![](images/5b2d39bdc51e1b65c24adc34341ed62c3274dc330c09a536da3f4c453d9023c2.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示用户信息管理界面的截图，界面上方有“首页”和“用户信息”的标签，右上角有一个红色框标注的“修改密码”按钮。
```
  
图3-6-9 用户注册记录删除  
图 3-6-10 用户修改密码

点击修改密码链接，系统弹出修改密码页面。

![](images/afb13f05699169587fff3563067369fce28ea8deb5b7eb699dcd0ab99d36e011.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户信息管理界面，当前有一个弹出窗口用于修改密码。弹出窗口中有三个输入框，分别用于输入旧密码、新密码和确认新密码。在背景中，可以看到一个表格列出了多个用户的账号名、姓名、角色等信息，以及他们的状态和操作选项。表格中的用户包括审计员、操作员和管理员等不同角色，每个用户的状态都是“启用”，并且都有“编辑”按钮供进一步操作。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

修改密码，需要输入旧密码，两遍新密码，新密码必须满足：大小写字母、数字、特殊字符， 四选三。

#### 3.7.3.4. 审核用户注册

以系统管理员的身份登录平台。

#### 3.7.3.5. 用户注册列表

在注册记录中查看待审核用户注册信息，可根据审核状态筛选待审核状态用户信息。

![](images/5244f80faebaa2632210ef150e92217c902eda48a3c11bd6c3dd1eb36c5f0d07.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户账户审核的界面，列出了多个账户的详细信息。表格中包含了序号、账号名、姓名、邮箱、申请人、申请时间、备注、审核状态、审核时间和操作等列。从审核状态来看，大部分账户已经被审核通过或拒绝，只有第一个账户的状态是“待处理”。每个账户旁边都有一个“审核”按钮，可能用于进一步的操作或查看详细信息。
```
  
图3-6-11 用户修改密码页面  
图3-6-12 用户注册记录列表页面

#### 3.7.3.6. 审核用户注册

点击注册记录页面右侧操作列->审核按钮，弹窗打开审核用户信息页面。  
选择审核通过或拒绝，并给出相应的审核意见，审核同意的需要选取所属角色。

![](images/31c0ca32f08c1092ca968c82b781ebfdd4347833d2a126b7f62bf72ccdb0847f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个注册记录审核界面，具体来说是一个弹出窗口，用于管理员审核新用户的注册信息。在弹出窗口中，我们可以看到以下信息：

- 账号名：fdtrtrtrtrt
- 姓名：rrrrrere
- 注册时间：2023-08-11 18:13:45

管理员可以选择“通过”或“拒绝”该注册申请，并且需要选择该用户所属的角色以及输入审批意见。目前，“通过”选项已被选中，但所属角色和审批意见尚未填写。

在背景中，我们可以看到一个表格，列出了其他用户的注册记录，包括序号、账号名、操作员、操作时间、审核状态、审核时间以及操作按钮。这些记录显示了不同的审核状态，如“已通过”、“已拒绝”和“待处理”，并且每个记录都有一个“审核”按钮，供管理员进行进一步的操作。

此外，页面顶部有一个搜索框和重置按钮，方便管理员快速查找特定的注册记录。页面底部显示了当前页码和总页数，以及导航按钮，以便于分页浏览所有的注册记录。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图 3-6-13 审核用户注册

### 3.7.4. 单位管理

#### 3.7.4.1. 单位信息列表

单位管理功能在系统支持组织管理时显示，进入单位管理功能，显示单位信息，可以根据单位名称过滤查询。

![](images/5a84360be9b3ba28572c26825e93920abd64ba2e48e556048fe4fa3047549478.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示单位管理界面的截图，界面上有一个搜索框用于输入单位名称进行查询，以及一个“查询”按钮和一个“重置”按钮。在搜索框下方，有一个表格列出了单位的相关信息，包括单位名称、单位编码、备注、创建时间和更新时间等字段。当前表格中只有一条记录，单位名称为“sansec”，创建时间为2024年5月11日11点31分51秒，没有填写单位编码和备注，更新时间为空。在操作列中，有编辑、新增和删除三个选项，用户可以对这条记录进行相应的操作。
```
  
图 3-6-14 单位管理界面

#### 3.7.4.2. 新增单位信息

点击界面左上角或操作列中的新增按钮，弹出新增界面，选择上级单位，输入单位名称、编码、备注。

![](images/167eff6c82961556e4c4ce231d156465c9367a5789c3bd9831a9dae37ddf4489.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个单位管理系统的界面，具体是一个“添加单位”的弹窗。弹窗中有四个输入框和一个文本区域，分别是：

1. 上级单位：当前选择的是“单位根目录”。
2. 单位名称：这是一个必填项，提示用户“请输入单位名称”。
3. 单位编码：提示用户“请输入单位编码”。
4. 备注：这是一个多行文本输入框，提示用户“请输入备注”，并且显示当前输入字数为0/100。

在弹窗的底部有两个按钮，分别是“取消”和“确定”。

背景中可以看到单位管理的主界面，有一个名为“sansec”的单位已经存在，显示了它的创建时间为2024-05-11 11:31:51，并且有编辑、新增和删除的操作选项。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.7.4.3. 编辑单位信息

点击操作列中的编辑按钮，弹出单位信息编辑界面，可以编辑上级单位、单位名称、单位编码、备注。

![](images/15524b3441b4806fdefcac64f28f66676aeab4b90b4c7e5bbc3092f4e334b7aa.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个单位管理系统的界面，具体是在进行单位信息的修改操作。弹出的对话框标题为“修改单位”，其中包含了几个输入字段：

- 上级单位：当前填写的是“sansec”。
- 单位名称：这是一个必填项，当前填写的是“密码平台部”。
- 单位编码：当前填写的是“mfpt”。
- 备注：这是一个可选的文本区域，当前为空，提示用户可以输入备注信息，最大长度为100个字符。

在对话框的底部有两个按钮：“取消”和“确定”，用户可以通过点击这两个按钮来取消或确认修改操作。

背景中可以看到单位管理的主界面，左侧有一个树状结构的单位列表，当前选中的单位是“密码平台部”。右侧是一个表格，列出了各个单位的创建时间和更新时间，并提供了编辑、新增和删除的操作选项。
```
  
图 3-6-15 新增单位信息

#### 3.7.4.4. 删除单位信息

点击操作列中的删除按钮，删除单位信息。

![](images/bbc34988a7bd4f55979e853a1fed0e0992d6e7a1074261c7cb7618b4c5780877.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个单位管理界面，界面上有一个弹出的提示框。提示框的内容是询问用户是否确认删除名称为“密码平台部”的数据项。提示框中有两个按钮，分别是“取消”和“确定”。背景中可以看到单位管理的表格，列出了单位名称、单位编码、备注、创建时间、更新时间和操作等信息。当前选中的单位名称是“密码平台部”，单位编码是“mfpt”，创建时间是2024-05-13 10:37:39。
```
  
图 3-6-16 编辑单位信息  
图 3-6-17 删除单位信息

## 3.8.系统管理

系统管理员具有设置口令黑名单、人员管理配置、系统配置等操作权限。

### 3.8.1. 口令黑名单

#### 3.8.1.1. 弱口令配置列表展示

打开口令黑名单菜单，页面展示弱口令列表。

![](images/c80079abb81dd3c8f4c2b2ee4cff70cdb74da2ae93e659379249f7813cf2b5aa.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示口令黑名单管理界面的截图。界面上有一个搜索框，用户可以输入弱口令进行查询。右侧有两个按钮，分别是“查询”和“重置”。在搜索框下方，有一个表格，列出了序号、弱口令、创建时间和操作选项。当前表格中只有一条记录，弱口令为“swxa@111111”，创建时间为2023-08-11 18:18:12。操作列中有“修改”和“删除”两个选项。
```
  
图 3-7-1 口令黑名单页面

#### 3.8.1.2. 新建弱口令

点击新建按钮，弹窗录入弱口令，点击确定添加弱口令。  
弱口令列表中的口令，不允许作为用户修改的新口令。

![](images/6a80c5864358bdb79b2853e0b2db9a3d7ff97ad887596cd60577363f016bfab8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机界面，具体来说是一个用于管理弱口令黑名单的系统页面。页面顶部有一个标签栏，当前选中的标签是“口令黑名单”。在页面的中央部分，有一个弹出窗口，标题为“新增弱口令”，提示用户输入新的弱口令，并提供了“取消”和“确定”两个按钮供用户选择。

在弹出窗口下方，有一个表格，列出了已有的弱口令信息。表格中有三列：序号、弱口令和创建时间。当前只有一条记录，序号为1，弱口令为“swxa@111111”，创建时间为“2023-08-11 18:18:12”。在这一行的最右侧，有两个操作链接：“修改”和“删除”，允许用户对这条记录进行编辑或移除。

此外，在页面的左上角，有一个“新建”按钮，可能用于打开“新增弱口令”的弹出窗口。在页面的右上角，有一个“查询”按钮和一个“重置”按钮，可能用于搜索特定的弱口令记录或清除当前的搜索条件。在页面的底部，有一个分页导航栏，显示当前共有1条记录，每页显示20条，当前位于第1页。
```
  
图 3-7-2 新增弱口令

#### 3.8.1.3. 修改弱口令

点击弱口令对象操作列“修改”按钮，弹窗修改弱口令。

![](images/9fa62244166c625b03f623dff679ae0561201887af6d8f1b944c6a0c8ba9d734.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个弱口令黑名单管理系统的操作窗口。在界面上方有一个标签栏，当前选中的标签是“口令黑名单”。在标签下方，有一个搜索框和两个按钮，分别是“查询”和“重置”。

在界面的中间部分，有一个弹出的对话框，标题为“编辑弱口令”。在这个对话框中，有一个输入框，里面已经填写了一个弱口令示例“swxa@111111”。对话框底部有两个按钮，分别是“取消”和“确定”。

在界面的下半部分，有一个表格，列出了一个弱口令的详细信息。表格的第一行是表头，分别标有“序号”、“弱口令”、“创建时间”和“操作”。在表格的数据行中，显示了序号为1的弱口令“swxa@111111”，创建时间为2023年8月11日18时18分12秒，并且提供了“修改”和“删除”的操作选项。

此外，在界面的左上角，还有一个“新建”的按钮，可能用于添加新的弱口令到黑名单中。整个界面的设计简洁明了，功能分区清晰，方便用户进行弱口令的管理和操作。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.8.1.4. 删除弱口令

点击弱口令对象操作列“删除”按钮，弹窗删除弱口令。

![](images/c16e59563b904687fe95021e85166c39d2bff9285e677563de5d6d3bc1998fef.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机界面，具体来说是一个管理弱口令黑名单的系统页面。在页面的顶部有一个标签栏，当前选中的标签是“口令黑名单”。在标签下方，有一个搜索框，提示用户可以输入弱口令进行查询，并且有“查询”和“重置”两个按钮。

在页面的主要部分，有一个表格列出了弱口令的信息，包括序号、弱口令、创建时间和操作选项。在这个例子中，只有一条记录，弱口令为“swxa@111111”，创建时间为2023年8月11日18:18:12，操作选项包括“修改”和“删除”。

当前，用户似乎正在尝试删除这条弱口令记录，因此弹出了一个确认对话框，询问是否确认要删除。对话框中有两个按钮：“取消”和“确定”，用户可以选择其中一个来决定是否继续执行删除操作。
```
  
图 3-7-3 修改弱口令

### 3.8.2. 系统配置

#### 3.8.2.1. 系统配置展示

系统管理员登录，点击系统管理-》系统配置，打开系统配置菜单，页面展示可配置的系统属性，包含大屏滚动告警是否开启、平台许可告警天数、服务许可告警天数、业务接口 token有效期、管理页面token 有效期、设备主密钥的备份方式、平台主备模式等。

![](images/c9b52be8cf92593e7f06a7ce14fa11ab4f70333ec05c22ef9debae9f2d1701fe.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统参数配置界面，包含了多个配置选项及其对应的设置值。具体内容如下：

1. 大屏滚动告警是否开启：是
2. 服务许可告警天数：30天
3. 平台从HDFS下载文件时的暂存目录：/opt/ccsp/temp/
4. 数据库监控连接方式：https
5. 接口token有效期：9999分钟
6. 连接数据库是否需要映射地址：否
7. 平台许可告警天数：30天
8. 平台是否包含多区域：是
9. 设备主密钥备份方式：口令加密
10. 平台主备模式：主备-主平台
11. 页面token有效期：9999分钟

这些配置项涵盖了系统的告警设置、服务许可、文件存储、数据库连接、安全加密以及主备模式等方面，确保系统的正常运行和数据的安全性。
```
  
图 3-7-4 删除弱口令  
图3-7-8 系统配置页面

#### 3.8.2.2. 修改系统配置

点击系统配置项右侧修改图标，弹窗输入对应的内容或选择配置项，点击保存。

![](images/24bf3eb36c6e990dfd394c67d4ac5c361c298a430642d1f9bbd0ceb47da4c338.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个系统配置界面，具体来说是一个修改配置参数的弹窗。弹窗中有一个输入框，标有“*平台许可告警天数”，当前值为30天。弹窗底部有两个按钮，分别是“取消”和“保存”。背景部分显示了系统参数配置的一些信息，包括大屏滚动告警是否开启（当前设置为“是”），服务许可告警天数（当前设置为90天），以及页面token有效期（当前设置为10080分钟）。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.8.3. 关于平台

关于平台功能界面，展示密码服务平台系统版本及各服务版本。

![](images/5f9559fa3aade3e161cb09496059cb52a5e54750b92790febec4a9d597cc6b02.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个名为“密码服务管理平台”的系统信息界面。该系统的型号为CCSP，版本号是V3.3.2.1-NMG。在图片的中央，有一个蓝色的盾牌形状的图标，象征着安全和保护。

在图标下方，列出了该系统所包含的各种组件及其版本信息：

- 密钥管理：V4.1.3
- 数据加解密：V1.0.4
- 签名验签：V1.0.4
- 协同签名：V4.6.6
- 数据库加密：V4.2.0-ccsp
- 文件加密：4.1.3
- 统一身份认证：V1.0.0

这些组件共同构成了这个密码服务管理平台的核心功能，提供了从数据加密、解密到身份认证等一系列的安全服务。
```
  
图3-7-9 修改系统配置  
图 3-7-10 关于平台页面

### 3.8.4. 主密钥备份

注：密服平台设备资源分配好后备份主密钥，这样可以防止当平台使用的所有密码机都发生无法修复故障后，主密钥无法找回，从而影响使用密钥无法正常解密使用。

#### 3.8.4.1. 主密钥备份

系统操作员登录后，进入主密钥备份功能，点击备份按钮，弹出备份界面，选择备份密钥的租户，选择备份类型，输入口令点击确定按钮，界面新增一条备份记录。备份类型支持口令备份与 ukey 备份，可在系统管理员登陆后的系统管理-》系统配置中修改设备主密钥的备份方式。

![](images/c90cc74798af3f3e6bffec3ea5513b8773e226d5eb446e8668b2cbb569bd8d1f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个备份主密钥的界面，界面中有一个弹出窗口，标题为“备份主密钥”。在弹出窗口中，有以下内容：

1. 所属租户：当前选择的是“平台共享设备”。
2. 备份类型：当前选择的是“口令备份”。
3. 备份口令：有一个输入框，提示用户输入备份口令。
4. 确认备份口令：有一个输入框，提示用户确认备份口令。
5. 备注：有一个文本框，提示用户输入备注，当前为空。

在弹出窗口的底部有两个按钮，分别是“取消”和“确定”。

在弹出窗口的背景中，可以看到一个表格，列出了序号、所属租户、备份状态和备注等信息。表格中有几行数据，显示了不同的租户名称和备份状态，其中大部分的备份状态为“备份成功”，并且有一些备注信息。

此外，在界面的顶部还有一些标签页，如“首页”、“租户管理”、“用户信息”、“注册记录”等，以及一些操作按钮，如“备份”和“导入密钥文件”。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.8.4.2. 主密钥恢复

系统操作员登录后，进入主密钥管理功能，点击列表数据后的恢复按钮，弹出恢复界面，输入备份时的口令点击确定按钮，系统开始恢复该租户下设备的主密钥，恢复设备列表会实时显示设备的恢复状态。

![](images/db2741ec2b7d64e620ba42a910846bc5820bc37e6d3e3875abb2ddf6217f28ca.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个弹出的对话框，标题为“恢复主密钥”。对话框中有两个输入框，分别标有“*恢复口令”和“*确认恢复口令”，提示用户输入和确认恢复口令。在输入框下方有两个按钮，一个是灰色的“取消”按钮，另一个是蓝色的“确定”按钮。

背景中可以看到一些模糊的界面元素，包括顶部的标签栏，上面有“用户信息”和“注册记录”等选项卡，以及左侧的一些操作按钮和列表。右下角显示了备份状态和备注信息，其中备份状态显示为“备份成功”，时间为2024年9月7日22:30:04。
```
  
图 3-7-12 主密钥恢复

![](images/7c7f5f2ae9acd42d3b9487c22446596119235b68f2a13547a2cb5f4690fc61f9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个设备恢复列表的界面，具体信息如下：

- **许可证有效期**：2025年8月29日。
- **操作员**：oper。

界面上有多个标签页，包括首页、许可申请、许可管理、产品信息、区域管理、租户管理、待审核租户、已审核租户、服务管理、服务类型管理、主密钥备份、设备恢复列表等。

在“设备恢复列表”标签页中，列出了以下设备信息：

1. **序号**：1
2. **设备名称**：vsm_10_20_18_100_443
3. **设备类型**：云服务器密码机_V5.2.7.1
4. **所属租户**：平台共享设备
5. **所属设备组**：sansec
6. **管理IP**：************
7. **管理端口**：443
8. **所属厂商**：三未信安科技股份有限公司
9. **恢复时间**：2024-08-29 16:29:35
10. **创建时间**：2024-08-29 16:29:31
11. **恢复状态**：恢复成功

这些信息表明该设备已经成功恢复，并且详细记录了设备的类型、所属租户、管理IP、端口、厂商以及恢复和创建的时间。
```
  
图 3-7-11 主密钥备份  
图3-7-13 主密钥恢复成功

#### *******. 备份文件下载

系统操作员登陆备份好主密钥后，点击数据后的下载按钮，可将备份的主密钥以文件的形式下载。

![](images/239fefaba2a8d87c3ec46721e5749584a2ba4621c0e3362886c9ae662f0e8554.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个名为“主密钥备份”的管理界面，界面中列出了多个租户的主密钥备份记录。表格中有以下几列信息：

1. **序号**：表示每条记录的顺序编号。
2. **所属租户**：显示了每个备份记录对应的租户名称，例如“zuhu_kms”、“zuhusms”等。
3. **备份方式**：列出了备份的具体方式，包括“自动备份”、“外部文件导入”、“UKEY备份”和“口令备份”。
4. **备份时间**：记录了每次备份操作的时间戳，例如“2024-09-04 17:32:45”。
5. **备份状态**：显示了备份操作的结果，所有记录的状态都是“备份成功”。
6. **备注**：提供了关于备份的一些额外信息，如“主密钥自动备份”或具体的设备编号如“508-509”。
7. **操作**：这一列包含了对每条记录进行操作的链接，如“下载”、“恢复”和“恢复设备”。

此外，界面顶部有多个标签页，如“首页”、“许可申请”、“许可管理”、“产品信息”、“区域管理”、“租户管理”、“待审核租户”、“已审核租户”、“服务管理”、“服务类型管理”、“设备恢复列表”等，表明这是一个多功能的管理平台。在表格上方还有两个按钮：“备份”和“导入密钥文件”，以及一个搜索框和重置按钮，用于用户进行相关操作和查询。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

图 3-7-14 备份文件下载

#### 3.8.4.4. 密钥文件导入

系统操作员登录，点击主密钥备份列表左上角的导入密钥文件，弹出导入密钥文件界面，选择所属租户，上传备份文件，点击确定，列表显示导入的密钥文件，备份方式为外部文件导入。

![](images/5b284f2bc26d00647813d1180f96c38d1d343d090e26862f8ef64e4812107448.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个文件上传界面，具体来说是一个备份文件上传的表单。界面上方有一个下拉菜单，标题为“所属租户”，当前选择的是“平台共享设备”。接下来是一个带有云朵和向上箭头图标的区域，提示用户可以将备份文件拖到此处或点击上传。下方有一行红色提示文字，说明只允许上传“.bk”格式的文件，并且文件大小不能超过5MB。

在文件上传区域下方，有一个备注输入框，提示用户可以在此处输入备注信息，目前输入框内为空，右下角显示已输入字符数为0/100。

最后，在界面底部有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来取消操作或确认上传。整个界面设计简洁明了，功能分区清晰，方便用户进行操作。
```
  
导入密钥文件

图 3-7-15 导入密钥文件

#### *******. 恢复设备列表

系统操作员登录，点击主密钥备份列表数据后的恢复设备按钮，展示该租户下进行恢复操作的设备以及恢复的状态。

序号 设备名称 设备类型 所属租户 所属设备组 管理IP 管理端口 所属厂商 恢复时间 创建时间 恢复状态

### 3.8.5. syslog 上报

系统操作员登录平台，配置好syslog 服务器的ip、端口、协议类型，可将平台的操作日志上报到指定的syslog 服务器上。

#### *******. syslog 列表

系统操作员登录平台，点击系统管理下的 syslog 上报菜单，列表展示添加的 syslog 服务器配置。

![](images/f4ac2b368429f1f33578420601983a1fee6d0b46fa396cfebc5ea95e4532d9be.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“syslog上报”的网页界面，界面顶部有多个标签页，包括“首页”、“租户管理”、“用户信息”、“注册记录”、“主密钥备份”以及多个“设备恢复列表”。当前选中的标签页是“syslog上报”。

在页面的主要部分，有一个表格，标题为“新建”，表格中有以下列：

- 序号：显示了记录的顺序编号。
- 服务器IP：显示了服务器的IP地址，例如***********。
- 服务器端口：显示了服务器的端口号，例如123。
- 上报类型：显示了上报的日志类型，例如操作日志。
- 频率：显示了上报的频率，例如55分。
- 协议类型：显示了使用的协议类型，例如UDP。
- 备注：目前为空。
- 创建时间：显示了记录创建的时间，例如2024-09-09 15:30:37。
- 操作：提供了编辑和上报记录的选项。

在表格上方，有两个输入框，分别用于输入服务器IP和服务器端口，以及两个按钮，一个是查询按钮，另一个是重置按钮。在页面底部，显示了当前记录的总数、每页显示的记录数以及当前页码。
```
  
图 3-7-16 恢复设备列表  
图 3-7-17 syslog 列表

#### *******. 新建 syslog 服务

系统操作员登录平台，点击 syslog 列表左上角的新建按钮，弹出新建SYSLOG 服务界面，输入信息点击确定。

![](images/ad4a699b59829c75980105eac786515dcd5b4e1cbf2a194cc55bc895ebf8e0fb.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于新建SYSLOG服务的配置界面截图。在界面上，有多个输入框和选项供用户填写和选择，以完成SYSLOG服务的设置。具体包括：

1. **日志上报类型**：这是一个下拉菜单，用户需要从中选择日志上报的类型。
2. **IP**：这是一个文本输入框，用户需要在此输入目标服务器的IP地址。
3. **端口**：这也是一个文本输入框，用户需要输入用于通信的端口号。
4. **协议类型**：这里有两个单选按钮，分别是UDP和TCP，用户需要选择一种协议类型。当前选择的是UDP。
5. **上报频率**：这是一个组合输入框，用户需要输入上报的日志频率，并从右侧的下拉菜单中选择时间单位（如分钟、小时等）。
6. **备注**：这是一个多行文本输入框，用户可以在此输入一些额外的说明或备注信息，当前显示已输入0个字符，最大可输入100个字符。

在界面的底部，有两个按钮：“取消”和“确定”。用户可以点击“取消”来放弃当前的设置，或者点击“确定”来保存并应用这些设置。整个界面设计简洁明了，方便用户进行操作。
```
  
图 3-7-18 新增 syslog 服务

#### *******. 编辑 syslog 服务

系统操作员登陆平台，点击 syslog 列表数据后的编辑按钮，可修改列表数据的上报频率和备注。

![](images/28a02636d441b98ab6538939916e5f82f447ba94d39a7f44a7e7d1c685d4e2de.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示SYSLOG服务编辑界面的图片。界面上有多个输入框和选项，具体如下：

1. **日志上报类型**：有一个下拉菜单，当前选择的是“操作日志”。
2. **IP**：输入框中填写了IP地址“***********”。
3. **端口**：输入框中填写了端口号“123”。
4. **协议类型**：有两个单选按钮，分别是UDP和TCP，当前选择了UDP。
5. **上报频率**：输入框中填写了“55”，旁边有一个下拉菜单，当前选择的是“分”。
6. **备注**：有一个多行文本框，提示“请输入备注”，当前为空。

在界面底部有两个按钮，分别是“取消”和“确定”。
```
  
图 3-7-19 编辑 syslog 服务

#### *******. 删除 syslog 服务

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/316fd4c363ea849aa96eee336dd634c8cdbec9f7e9569a89603cb55d812fb2f4.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机系统管理界面，具体来说是一个syslog上报的配置页面。页面顶部有多个标签页，包括首页、租户管理、用户信息、注册记录、主密钥备份以及多个设备恢复列表等。当前选中的标签页是“syslog上报”。

在页面的主要部分，有一个表格列出了syslog上报的详细信息，包括序号、服务器IP、服务器端口、上报类型、频率、协议类型、备注、创建时间和操作选项。表格中有一条记录，其服务器IP为***********，服务器端口为123，上报类型为操作日志，频率为55分，协议类型为UDP，创建时间为2024-09-09 15:30:37。在操作列中，有编辑、上报记录和删除三个选项。

此外，在页面的上方，还有两个输入框，分别用于输入服务器IP和服务器端口，以及两个按钮，一个是查询，另一个是重置。在表格的左上角，还有一个“新建”的按钮，可能用于添加新的syslog上报配置。
```
  
系统操作员登录，点击列表数据后的删除按钮，可删除对应的syslog 配置信息  
图 3-7-20 删除 syslog 服务

#### *******. 上报记录

系统操作员登录，点击列表中数据信息后的上报记录，界面展示上报操作日志记录。

![](images/fd89a32c339fdbb436c028abd74c0ad2167527aa3bcd20db33cb8acaf6fbdcfa.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个日志记录的表格，表格中有四列：序号、日志数量、上报时间、状态和备注。具体信息如下：

1. 序号为1的记录：
   - 日志数量：0
   - 上报时间：2024-09-09 16:00:01
   - 状态：成功
   - 备注：当前时间节点没有可推送的日志记录

2. 序号为2的记录：
   - 日志数量：0
   - 上报时间：2024-09-09 15:55:04
   - 状态：成功
   - 备注：当前时间节点没有可推送的日志记录

表格底部显示共有2条记录，每页显示20条记录，当前页码为1。右下角有一个“关闭”按钮。
```
  
***********上报记录  
图 3-7-21 上报记录

### 3.8.6. 白名单管理

系统操作员登录平台，配置白名单策略后，只能指定 IP 地址可以访问平台管理web 页面和业务接口。

#### *******. 白名单列表

系统操作员登录平台，点击系统管理下的白名单管理菜单，列表展示添加的白名单配置，界面展示序号、客户端标识、IP、开启状态、创建时间、备注和操作。

![](images/db7cae84528dd57e7bf9e574ecdaf10ee756ae312ec80662ff462bab2a4a854e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个客户端标识管理界面，界面中包含一个表格，列出了客户端标识的相关信息。表格的列标题包括序号、客户端标识、IP、开启状态、创建时间、备注和操作。具体来说：

1. 第一行数据的客户端标识为"default-当前登录IP-勿删"，IP地址为*************，开启状态为开启，创建时间为2025-05-13 14:58:54，备注为"当前登录ip自动添加为白名单"。
2. 第二行数据的客户端标识为"test"，IP地址为************，开启状态为开启，创建时间为2025-05-13 14:58:54，没有备注。

在表格的上方有一个搜索框，可以输入客户端标识进行查询，右侧有两个按钮，分别是查询和重置。在表格的左上角有一个新增按钮，可以添加新的客户端标识。在表格的右下角有分页信息，显示当前页面共有2条记录，每页显示20条，当前是第1页，总共有1页。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### *******. 新增白名单

系统操作员登录平台，点击白名单列表左上角的新增按钮，弹出新增白名单界面，输入客户端标识、IP、备注等信息，点击确定。

![](images/c94076bbc81cac8aee8288e436be8f4f98efe37dbe74be91f25aabc2800adecb.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个表单界面，包含三个输入框和两个按钮。第一个输入框标有“客户端标识”，提示用户“请输入客户端标识”；第二个输入框标有“IP”，提示用户“请输入IP”；第三个输入框标有“备注”，提示用户“请输入备注”，并且在右下角显示当前输入字数为0/100。在表单的底部有两个按钮，分别是“取消”和“确定”。
```
  
图 3-7- 1 白名单列表  
新增白名单  
图 3-7-23 新增白名单

#### *******. 编辑白名单

系统操作员登陆平台，点击白名单列表数据后的编辑按钮，可修改备注信息。

![](images/c52dd7e62050c9eaa0207d374b3002f1154ee1889e09fda5179d6938e4bc5d12.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个编辑白名单的界面。界面上有三个主要部分：

1. **客户端标识**：这是一个输入框，当前显示的内容是“default-当前登录IP-勿删”。这个字段似乎是必填的，因为前面有一个星号（*）。

2. **IP**：这是一个输入框，当前显示的内容是“*************”。这个字段也是必填的，前面也有一个星号（*）。

3. **备注**：这是一个文本区域，当前显示的内容是“当前登录ip自动添加为白名单”。这个字段不是必填的，因为它前面没有星号（*）。在文本区域的右下角，显示了当前输入的字符数（14/100），表示最多可以输入100个字符。

在界面的底部，有两个按钮：“取消”和“确定”。用户可以通过点击“确定”按钮来保存更改，或者点击“取消”按钮来放弃更改。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### *******. 关闭白名单

系统操作员登录，选在要关闭的白名单（开启状态为开启），点击关闭，系统弹出提示框“确定要关闭白名单”，点击确定，关闭白名单，白名单关闭后，该 IP 将无法访问平台管理web页面和业务接口。

![](images/26937350c3b0a1a516b12f97e3f2b8d3df3c4eadad8702e4375dd7730b930834.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个管理界面，主要用于管理和配置客户端标识及其相关设置。界面上有一个表格，列出了客户端标识、IP地址、开启状态、创建时间和备注等信息。当前有两个客户端标识被列出，分别是“test”和“default-当前登录IP-勿删”，它们的IP地址分别为***********和************，状态都是“开启”，并且有相应的创建时间。

在表格的右侧，有一个操作列，其中包含了编辑、开启、关闭和更多选项。当前选中了“关闭”选项，并且弹出了一个提示框，询问是否确定要关闭白名单。提示框中有两个按钮，分别是“取消”和“确定”。

此外，在界面的顶部，有一个搜索框和一个重置按钮，用户可以输入客户端标识进行查询。左侧还有一个“新增”按钮，用于添加新的客户端标识。
```
  
图 3-7-24 编辑白名单  
图 3-7- 25 关闭白名单

#### *******. 开启白名单

系统操作员登录，选在要关闭的白名单（开启状态为关闭），点击关闭，系统弹出提示框“确定要开启白名单”，点击确定，开启白名单。白名单开启后，该IP 可以访问平台管理web页面和业务接口。

![](images/d44fef9e6ed12bfb6895d0d4a5558719062df5b54f7d463a17531aafa9ef3e6e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个管理界面，其中包含一个表格和一个弹出的提示框。表格列出了客户端终端标识、IP地址、开启状态、创建时间和备注等信息。具体来说：

- 第一行数据：客户端终端标识为“test”，IP地址为************，开启状态为“关闭”，创建时间为2025-05-13 15:54:04。
- 第二行数据：客户端终端标识为“default-当前登录IP-勿删”，IP地址为*************，开启状态为“开启”，创建时间为2025-05-13 14:58:54，并且备注中提到“当前登录ip自动添加为白名单”。

在表格下方，有一个弹出的提示框，内容为“确定要开启白名单吗？”，并提供了“取消”和“确定”两个按钮供用户选择。

此外，在表格的右上角，有“编辑”、“开启”、“关闭”和“更多”等操作选项，用户可以通过这些选项对表格中的数据进行相应的操作。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### *******. 删除白名单

系统操作员登录，点击列表数据后的删除按钮，可删除对应的白名单配置信息

![](images/e56b38a01a73fb6cfdc51e256fb1b636d66ab116606e66cc6eb1dd9a262304da.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个管理或监控系统的网页界面。界面上方有一个表格，列出了客户端标识、IP地址、开启状态、创建时间和备注等信息。表格中有一条记录，显示了当前登录的IP地址为*************，状态为开启，并且有备注说明这是当前登录IP自动添加为白名单。

在界面的中央，有一个弹出的提示框，内容是“删除后将无法恢复，是否确认删除？”，提示用户即将进行的操作是不可逆的。提示框中有两个按钮，分别是“取消”和“确定”，供用户选择是否继续执行删除操作。

此外，在表格的右上角，有一个“操作”列，提供了编辑、开启、关闭和更多选项的功能，允许用户对列表中的项目进行进一步的管理。整个界面的设计简洁明了，功能分区清晰，便于用户理解和操作。
```
  
图 3-7- 26 开启白名单  
图 3-7-28 删除白名单

### 3.8.7. UKEY 根证管理

安全管理员登录平台，配置 UKEY 根证，可管理平台登录 UKEY 根证。

#### *******. 白名单列表

安全管理员登录平台，点击系统管理下的UKEY 根证管理菜单，列表展示添加的UKEY 根证信息，界面展示序号、序列号、颁发者、使用者，起止时间、备注、创建时间和操作。

![](images/4dc91d420267b826ce42846b3de9ebd8cf964368a80d26739aadac04e52a301d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示证书管理界面的截图，界面上有一个表格，列出了证书的相关信息。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.8.7.2. 导入 UKEY 根证

安全管理员登录平台，点击 UKEY 根证列表左上角的导入按钮，弹出导入根证书界面，可根证书文件拖入证书文件框，或者点击上传，选择证书文件。

导入根证书

![](images/0963b1840ae19de5ca33ec26b1fcb19e3dc06c95f4de055b988d8d979cf0d3bd.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个文件上传界面，主要用于上传证书文件。界面上方有一个灰色的云朵图标，表示上传功能。下面的文字提示用户可以将文件拖到此处进行上传，或者点击“点击上传”按钮来选择文件。此外，还有一行小字说明了文件大小限制为不超过20KB，并且只允许上传.cer和.p7b格式的文件。在界面底部有两个按钮，分别是“取消”和“确定”，用户可以选择取消操作或确认上传。整个界面设计简洁明了，方便用户操作。
```
  
图 3-7-29 UKEY 根证列表  
图 3-7- 30 导入根证

#### 3.8.7.3. 删除白名单

安全管理员登录平台，点击 UKEY 根证列表数据后的删除按钮，可删除对应的根证书信息。

![](images/74ee6f39c5733e9dbefaa1b41658c96620fef44a23bed7a854c0491b3b14f301.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个证书管理系统的截图。界面上方有一个“导入”按钮，表明用户可以在此导入新的证书。界面的主要部分是一个表格，列出了证书的详细信息，包括序号、序列号、颁发者、使用者、起止时间、备注、创建时间和操作等字段。

在表格中，我们可以看到一条记录，其颁发者信息为“C=CN,ST=北京,L=北京,O=三未信安,CN=...”，这表明该证书是由位于中国北京市的三未信安公司颁发的。证书的有效期从2019年9月4日14:47:14至某个未完全显示的时间点，备注中提到这是“三未信安PD根证”。

在界面的右下角，有一个红色的“删除”按钮，用户可以点击它来删除选中的证书。当前，系统弹出了一个提示框，询问用户是否确认要删除根证书，提示框中有两个按钮：“取消”和“确定”，用户需要选择其中一个来继续操作。

这个界面的设计目的是为了方便用户管理和维护他们的数字证书，确保网络安全和数据保护。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

## 3.9.日志管理

### 3.9.1. 管理日志

#### *******. 操作日志查询

![](images/1f4739fdf284ad21e0a2576a0a43f31daa42b65513c09c20997230da22846f8a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个操作审计日志的界面，记录了不同功能模块下的操作内容、操作人、请求IP、操作状态、操作时间、hmac检验状态以及是否审计的信息。具体包括用户管理、物理机管理、服务管理、虚拟机管理等模块的操作记录，例如删除注册账户信息、添加物理机、新增服务信息、启动虚拟机等。每条记录都详细列出了操作的具体情况，如操作人都是“oper”，请求IP有**********和**********两个不同的地址，操作状态有成功和失败两种，操作时间精确到秒，hmac检验状态均为“校验成功”，是否审计一栏目前都显示为“未审计”。
```
  
图 3-7-31 删除 UKEY 根证

审计员登录后，点击日志管理->管理日志，进入操作日志管理界面，可以根据查询条件查看操作日志信息。

#### *******. 操作日志审计

选中需要审计的日志信息，点击批量审计按钮，完成日志信息审计。

![](images/4b2da4a16768232dc7471c1ae26aeb89c27d15a53beeee37284d886dae145147.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个批量审计的界面，界面上有一个弹出的提示框，询问是否确认审计日志。背景中可以看到一个表格，列出了多个功能模块的操作记录，包括用户管理、物理机管理、服务管理等。每条记录包含了操作内容、操作人、请求IP、操作状态、操作时间、hmac校验状态和是否审计的信息。
```
  
图3-8-1 操作日志页面  
图3-8-2 操作日志审计

#### *******. 操作日志导出

点击导出按钮，可以按界面中的查询条件导出日志信息，导出数据一次最多允许导出5000条数据。

![](images/0d451f52849b8e9ec6f7073c95d3daf5231b93c5cb50958c20cf97b9b39e3a59.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个系统操作日志界面，列出了不同功能模块的操作记录，包括用户管理、物理机管理、服务管理等。每条记录包含了操作内容、操作人、请求IP、操作状态、操作时间、hmac校验状态和是否审计等信息。当前页面显示了20条记录中的第一页，总共有256条记录。

在图片的中央，有一个弹出的提示框，询问用户是否确认导出当前日志记录，提示框中有“取消”和“确定”两个按钮供用户选择。
```
  
图3-8-3 操作日志导出

## 3.10. 监控告警

### 3.10.1. 告警信息

以系统操作员身份登录系统，在监控告警-告警信息中可以查看告警信息。

![](images/52bf53a77416ebee2b8669f67836272fb3615308985acf311b496242f0f46c38.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个告警信息的管理界面，具体包含以下内容：

1. **告警类型**：列出了两种告警类型，分别是“磁盘使用率”和“内存使用率”。
2. **告警信息**：详细说明了告警的具体情况，对于磁盘使用率，告警信息为“磁盘使用率高于70”，对于内存使用率，告警信息为“内存使用率高于70”。
3. **所属租户**：当前没有指定具体的租户。
4. **区域**：所有告警都发生在“region1”区域。
5. **告警源名称**：告警来源于“业务服务器”。
6. **告警源IP**：告警源的IP地址为“***********”。
7. **告警次数**：磁盘使用率告警发生了6次，内存使用率告警发生了2次。
8. **首次告警时间**：磁盘使用率的首次告警时间为“2024-05-11 17:19:20”，内存使用率的首次告警时间为“2024-05-11 17:44:55”。
9. **最后告警时间**：两种告警的最后告警时间都是“2024-05-11 17:54:45”。
10. **状态**：所有告警的状态都是“待处置”。
11. **操作**：提供了“处置”的选项，可以对告警进行处理。

此外，界面顶部有筛选条件，包括告警类型、区域和所属租户的选择框，以及查询和重置按钮。底部显示了当前页面共有2条记录，每页显示20条记录，当前是第1页，共1页。
```
  
图 3-9-1 告警信息

点击操作列中的处置按钮，在弹出的窗口中输入备注信息，点击确定按钮。处置后的告警信息，可以在监控告警-告警历史功能查询。

![](images/483d641c91512ed075f33a9818437cde20379672fa6c321fbdcaa43056f4fcd5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个告警管理界面，列出了两个告警信息。第一个告警是关于磁盘使用率高于70%，发生在region1区域的业务服务器上，IP地址为***********，已经告警6次，首次告警时间为2024年5月11日17:19:20，最后告警时间为2024年5月11日17:54:45，当前状态为待处理。第二个告警是关于内存使用率高于70%，同样发生在region1区域的业务服务器上，IP地址也是***********，已经告警2次，首次告警时间为2024年5月11日17:44:55，最后告警时间为2024年5月11日17:54:45，当前状态也为待处理。在操作列中，有一个红色框标出的“处置”按钮，表示可以对告警进行处理。
```


![](images/65073fcfb650801cf47152b4caacde76a64b4bdd00b8701751a7694135894c1e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户界面，用于填写处置信息。界面上有一个标题“请填写处置信息”，在右上角有一个关闭按钮（X）。下方有一个标签“备注”，旁边是一个文本输入框，提示用户“请输入备注”。在文本输入框的右下角显示当前已输入字符数为0，最大限制为100个字符。在界面底部有两个按钮，一个是灰色的“取消”按钮，另一个是蓝色的“确定”按钮。
```
  
图 3-9-2 处理消息

### 3.10.2. 告警历史

以系统操作员身份登录系统，在监控告警-告警历史中可查看已处置的告警信息。

![](images/1f2e33ccd1656304b178a8d5e99410d87b9414fe6876c14b7a3c7be8715f8220.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个告警信息的表格，包含了以下列：序号、告警类型、告警信息、所属租户、区域、告警源名称、告警源IP、告警次数、首次告警时间、最后告警时间、状态和备注。具体来说，表格中有一条记录，序号为1，告警类型是内存使用率，告警信息是内存使用率高于70%，所属租户为空，区域是region1，告警源名称是业务服务器，告警源IP是***********，告警次数是2次，首次告警时间是2024-05-11 17:44:55，最后告警时间是2024-05-11 17:54:45，状态是已处理，备注也是已处理。
```
  
图3-9-4 告警历史界面

### 3.10.3. 告警配置

以系统操作员身份登录系统，在监控告警-告警配置中可查看告警配置信息，包括：CPU 使用率告警、产品配额剩余时间告警、内存使用率告警、服务状态告警、磁盘使用率告警、许可证剩余时间告警、设备状态异常告警。

点击列表中是否启用按钮，可以启用、禁用告警配置。

![](images/66e9daf461f3d3cc7e2ea98b25a21d2477aec0029c9716db87cc0dd3d4b4f6f6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示告警配置界面的截图，界面上方有一个搜索栏，用户可以输入规则名称和选择标签进行查询。下方是一个表格，列出了不同的告警规则及其状态。表格包含以下列：

- 规则名称：列出了各种告警规则，如CPU使用率告警、产品配额剩余时间告警等。
- 标签：每个规则对应的标签，例如cpu、product_quota等。
- 是否启用：显示了每个规则的启用状态，所有规则目前都处于启用状态，用绿色开关表示。
- 操作：提供了对每个规则进行编辑的选项。

从截图中可以看出，这是一个用于管理和配置系统告警规则的界面，用户可以通过这个界面查看和调整各种告警规则的设置。
```
  
图 3-9-3 处置消息  
图3-9-5 告警配置界面

点击列表操作列中的编辑按钮，可以编辑告警配置规则和触发条件。

注：所需的配置规则都已提前预置好，不建议修改。

![](images/e413fadfdc8bd5a705fe760a05cfa0450b1a56ca93bf684ad23e62422fc132f1.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个配置告警规则的界面。界面上有一个标题“配置告警规则”，下面有两个主要部分：配置规则信息和触发条件详情。

在“配置规则信息”部分，有一个必填项“规则名称”，当前填写的是“CPU使用率告警”。还有一个标签选择区域，当前选中的标签是“cpu”，并且有一个“新增标签”的按钮。

在“触发条件详情”部分，设置了一个触发阈值，具体为“CPU使用率 大于 70”，并且持续时间设置为“3 分钟”。

界面底部有两个按钮，分别是“取消”和“提交”。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.10.4. 邮件服务器

以系统操作员身份登录平台，点击监控告警下的邮件服务器，邮箱需提前开通 smtp 服务，将开通后的数据配置到邮件服务器界面。

![](images/8db50a5d17224734a374a966091d187780c5d756899331603cf3426b5568adc0.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示电子邮件设置界面的图片。界面上有多个输入框和选项，用于配置电子邮件的相关信息。具体内容如下：

1. **邮件主题**：输入框中填写了“Info”。
2. **邮件服务器**：输入框中填写了“smtp.qq.com”。
3. **邮箱账号**：输入框中填写了一个QQ邮箱地址“<EMAIL>”。
4. **用户名**：输入框中同样填写了“<EMAIL>”。
5. **授权码**：输入框中显示了一串星号“******”，表示已输入但被隐藏的授权码。
6. **端口**：输入框中填写了数字“465”。
7. **是否开启SSL**：选择框中选择了“是”。
8. **是否开启TLS**：选择框中选择了“否”。

在界面底部，有三个按钮：
- **重置**：用于重置所有输入内容。
- **提交**：用于提交当前的设置。
- **发送验证邮件**：用于发送一封验证邮件以测试设置是否正确。

这些设置通常用于配置应用程序或系统中的电子邮件功能，确保能够通过指定的邮件服务器发送邮件。
```
  
图3-9-6 编辑告警配置界面  
图3-9-7 邮件服务器配置界面

### 3.10.5. 告警对象

#### 3.10.5.1. 新增告警对象

以系统操作员身份登录平台，点击监控告警-》告警对象，点击左上角的新建按钮，填写通知对象名称、描述、标签、通知对象类型、通知模板，点击确定。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/498af71830719321103d45d231b5868280ea3093ad8d4c03b24c3e941777cd43.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新增通知对象”界面的图片，界面上有五个输入框和两个按钮。
```
  
新增的告警对象需在告警配置中配置联系人后方可通知到对应的对象。

![](images/93918fd4eb8d231edd8f168d8440fcf694f95ad74dc51c4140d1052d94d6ed42.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示系统监控和告警配置的界面截图。界面上方有多个标签页，包括“首页”、“告警信息”、“告警历史”、“告警配置”等，当前选中的标签页是“告警配置”。在“告警配置”标签页中，有一个表格列出了各种告警规则的详细信息。

表格的列标题包括：
- 规则名称：列出了各种告警规则的名称，如CPU使用率告警、VPN隧道状态告警等。
- 标签：列出了与告警规则相关的标签，如ccsp_cpu、ccspSys2_cpu等。
- 联系人标签：列出了与告警规则相关的联系人标签。
- 是否启用：列出了告警规则是否启用的状态，绿色开关表示已启用。
- 操作：列出了对告警规则的操作选项，如编辑、更多、配置联系人、删除等。

在“操作”列中，有一个红色框标注了“配置联系人”的选项，可能表示这是一个需要特别注意或操作的功能。
```
  
图 3-9-9 配置联系人界面

![](images/7af3eed956394d9cb210581ac19366d60b1f1c7a4402583984b3cbd222803a2d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个配置联系人的界面，可能是某个软件或系统的报警管理模块的一部分。界面上有一个弹出的对话框，标题为“配置联系人”，里面包含三个下拉选择框和两个按钮。

第一个下拉选择框的标签是“告警发送类型”，提示用户选择告警的发送方式类型；第二个下拉选择框的标签是“联系人标签”，提示用户选择联系人的标签；第三个下拉选择框的标签是“是否重复告警通知”，提示用户选择是否需要重复的告警通知。

在对话框的底部有两个按钮，分别是“取消”和“提交”，用户可以通过点击这两个按钮来取消操作或者提交配置信息。

背景中可以看到一些模糊的文字和图标，包括“首页”、“告警信息”、“告警历史”、“告警配置”等标签，以及一个搜索框和一些操作按钮，但这些内容由于焦点在弹出的对话框上，所以显得比较模糊。
```
  
图3-9-8 新增通知对象界面  
图3-9-10 配置联系人界面

#### 3.10.5.2. 编辑告警对象

以系统操作员身份登录平台，点击监控告警-》告警对象，点击数据后的编辑按钮，可修改通知对象的名称、描述、通知对象类型、模板等信息。

![](images/9490e16e05946613881272e5099e62f5ff4d51bbb7e88c437d57fa4ada411d21.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示编辑通知对象界面的截图。界面上有多个输入框和选择框，用于填写通知对象的相关信息。具体包括：

1. 通知对象名称：已填写为“asd”。
2. 描述：提示请输入描述，但目前为空。
3. 标签：已填写为“sda”。
4. 通知对象类型：选择了“邮件”选项。
5. 通知模板：选择了“邮件中文模板”选项。
6. 邮箱地址：已填写为“<EMAIL>”。

在页面底部有两个按钮，分别是“取消”和“提交”。用户可以点击“提交”按钮来保存所填写的信息，或者点击“取消”按钮来放弃当前操作。
```


图3-9-11 编辑通知对象界面

#### 3.10.5.3. 删除告警对象

以系统操作员身份登录平台，点击监控告警-》告警对象，点击数据后的删除按钮，可删除对应的告警对象。

![](images/1aa751cb5a5b9ed5c4758a7ed6d555a6f39db5a5371c1885e80920fb41e79c07.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示告警配置界面的截图，界面上有多个标签页，当前选中的标签页是“告警配置”。在该标签页中，有一个表格列出了通知对象的信息，包括通知对象名称、标签、通知对象类型、通知对象信息和描述。表格中有一条记录，通知对象名称为“asd”，标签为“sda”，通知对象类型为“邮件”，通知对象信息为“<EMAIL>”。在操作列中，有一个红色框标出的“删除”按钮。
```
  
图3-9-12 删除通知对象界面

## 3.11. 密码产品

以系统操作员身份登录系统，可以对密码产品进行管理。

### 3.11.1. 产品信息

在产品信息中可查看各个产品的详细信息，并完成产品的上架、下架、新增、删除、预览、编辑等操作。

#### 3.11.1.1. 产品上架和下架

点击列表中对应产品的上架开关，可以将产品状态设置为上架或下架。只有上架的产品才能对其服务进行配额管理。

![](images/8c0bcde6139990735ea5486ea4d6a68a2dd43e4b3be16de7dd761ba8029cd1d5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示密码服务列表的网页截图，表格中列出了11种不同的密码服务产品，包括数据加解密服务、统一身份认证服务、时间戳密码服务、电子签章密码服务、数据库加密服务、密钥管理服务、签名验签密码服务、SSL网关服务、协同签名服务、文件加密服务和动态令牌认证服务。每种服务都有其产品名称、产品类型（均为密码服务）、单位（均为实例/月）、产品简介、创建时间和上架状态。此外，每行还提供了预览、编辑和删除的操作选项。
```
  
图3-10-1 密码产品上架与下架

#### 3.11.1.2. 新建产品

点击产品信息页面的新建按钮，进入产品发布页。

个返回 产品发布

![](images/2ec4123ce2180cb73acb007237e93775f249fb169d87b2960637eeb2464ac818.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示产品发布页面的截图，页面包含了多个输入框和选项，用于填写产品的详细信息。
```
  
图3-10-2 密码产品发布页

产品发布页中共有5 项信息需要填写，分别为基础信息、产品优势、产品功能、产品规格和应用场景。其中基础信息、产品规格的计价单位和应用场景是必填项，产品优势、产品功能为选填。

![](images/efbf1c04edd7f0e66199d47e32554e656a2c1229d73e3dc0731e067e0bfcbaec.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个产品信息录入的界面，主要分为三个部分：产品优势、产品功能和产品规格。在产品优势部分，有三个相同的输入框组，每个组包含图标、优势和描述的输入区域，并且每个组都有一个“删除”按钮。在产品功能部分，有一个产品功能和描述的输入区域，以及一个“删除”按钮。在产品规格部分，有计件单位、规格、原价、折扣价和备注的输入区域，同样配有一个“删除”按钮。此外，在每个部分的右上角都有一个“添加”按钮，用于增加新的输入框组或输入区域。
```
  
图3-10-3 密码产品发布页添加产品优势等

此外，产品优势、产品功能和产品规格部分可以点击右上角的“添加”按钮选择添加，页面会自动添加显示相应表单，在相应表单处点击“删除”按钮可以删除相应表单。填写完成后，点击页面“预览”按钮可以查看产品的页面效果。

![](images/bb282c3447c4c9e1e99aaa086bd2c967255effec72f2518833837fc06d2f984a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个关于数据库加密服务的网页界面。页面顶部有一个标题“数据库加密服务”，并有一段简短的介绍，说明了该服务的功能和优势。

在页面的中间部分，有四个标签：“产品优势”、“产品功能”、“产品规格”和“应用场景”，当前选中的是“产品优势”。

在“产品优势”部分，列出了三个主要的优势：
1. 应用无改造：数据库加密后SQL语法兼容性好，支持模糊查询，无需对用户应用程序进行二次开发。
2. 广泛的数据库兼容性：支持多种数据库，包括主流的国际数据库和国产数据库。
3. 多种密钥管理方式：采用集中密钥管理，支持对接三方密钥管理系统。

在页面的底部，列出了四个主要的产品功能：
1. 数据库机密性保护：支持对数据库敏感数据使用国密算法进行机密性保护。
2. 数据库完整性保护：支持对数据库敏感数据使用国密算法进行完整性保护。
3. 支持模糊查询：支持选择加密后密文仍然可以支持模糊查询功能的加密模式。
4. 数据库动态脱敏：支持依据指定的脱敏规则，对数据库返回的数据进行专门的遮盖、替换，确保生产环境的敏感数据能够得到保护。

页面右侧有一个蓝色的图标，显示了一个带有锁的数据库符号，象征着数据的安全性和加密功能。
```
  
个返回 产品详情  
图 3-10-4 密码产品预览

填写完成后，点击“提交”按钮，密码产品将被添加，默认状态为下架状态。

#### 3.11.1.3. 预览产品

点击产品信息页面列表项操作列里的“预览”按钮，可以查看产品的页面效果，产品预览页面同图 3-10-4 所示。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/6651a7ca1d3e31b638a066522043f4cd319faaf884a7e88cf4c8165b282aaf4d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示产品信息管理界面的截图，界面上方有“首页”、“租户管理”和“产品信息”三个标签页，当前选中的是“产品信息”。在页面的右上角，显示了许可证的有效期为2026年9月27日，并且有一个“大屏”图标，旁边显示有34个操作员和平台操作员的信息。在产品信息区域，有一个搜索框，用户可以输入产品名称进行查询，右侧有两个按钮，分别是“查询”和“重置”。下方是一个表格，列出了产品的详细信息，包括序号、产品名称、产品类型、销量、单位、上架状态、备注、创建时间和更新时间等。每行数据右侧都有“预览”、“编辑”和“删除”三个操作按钮，方便对产品信息进行管理。
```
  
图3-10-5 密码产品预览入口

#### 3.11.1.4. 编辑产品

点击产品信息页面列表项操作列里的“编辑”按钮，可以编辑产品信息。

![](images/213b7cf5b08f44b5ec1dedd010345ccac0e5d9199c2235f6d72b4220dfb2cdef.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个产品发布页面，具体来说是一个密码服务产品的发布界面。页面分为几个部分：

1. **基础信息**：这部分包括服务类型、服务名称和服务图标等基本信息的填写区域。
   - 服务类型被选择为“密码服务”。
   - 服务名称输入框中填写了“数据加解密服务”。
   - 服务图标展示了一个蓝色和白色的图标，建议上传200px*200px的图片，且大小不超过5MB。

2. **接口文档和附件**：这部分提供了上传接口文档和附件的功能。
   - 接口文档上传区域提示仅支持上传一个文件，大小不超过50MB，仅支持pdf格式，并且已经上传了一个名为“三未信安密码服务平台_数据加解密服务接口V1.2.pdf”的文件。
   - 附件上传区域同样提示仅支持上传一个文件，大小不超过100MB，支持扩展名包括pdf、doc、docx、xls、xlsx、zip和rar，并且已经上传了一个名为“数据加解密.zip”的文件。

3. **产品简介**：这部分是一个文本框，用于填写产品的详细介绍。当前填写的内容是关于数据加解密服务能够提供高速率、高并发的密码运算，满足数据的签名/验证、加密/解密的要求，支持常见的国密、国际算法，提供标准的REST服务接口，适用于各类密码安全应用系统，保证传输信息的机密性、完整性和有效性。

4. **产品优势**：这部分展示了四个产品优势的图标和名称，分别是业务监控、负载均衡、系统安全和密钥安全。

页面底部有取消、预览和提交按钮，用户可以进行相应的操作。
```
  
图3-10-6 密码产品编辑界面

#### ********. 删除产品

点击产品信息页面列表项操作列里的“删除”按钮，弹出框提示确认信息，确认后可删除产品。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

序号 产品名称 产品类型 销量 单位 上架 备注 创建时间 更新时间 操作电子签章密码服务 电子签章密码服务…数据库加密服务 密码服务 1实例/月 数据库加密服务是… 预览编辑删除密钥管理服务 密钥管理服务支持… 2023-12-04 10:07:02 2023-12-04 10:07: 预览编辑删除数字证书认证服务 密码服务 1实例/月 数字证书认证服务… 2023-12-02 14:16:02 2023-12-02 14:16: 预览编辑删除文件加密服务 密码服务 1实例/月 文件加密服务是保… 2023-12-02 13:54:22 2023-12-02 13:54:动态令牌认证服务 密码服务 动态令牌认证服务… 预览编辑删除协同签名服务 密码服务 1实例/月 协同签名服务依托… 2023-12-0212:27:预览 编辑删除SSL网关服务 密码服务 SSLVPN综合安全网… 2023-12-02 11:59: 预览编辑删除

## 3.12. 消息管理

以系统操作员身份登录系统，可以对消息进行管理。

### 3.12.1. 消息铃铛

系统操作员登录之后，在界面右上角可以看到一个消息铃铛，铃铛上的数字代表未读消息数；  
点击铃铛之后，可以对消息标为已读和处理。

#### 3.12.1.1. 消息标为已读

点击铃铛按钮，在弹出的铃铛消息列表中点击对应消息的标为已读按钮，可以将消息标为已读。

![](images/9222ad24b5a0ea2cc25be3762aed77a505c0e1f86682a6567f2913f0e7dc23c5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个消息通知界面，日期为2026年9月27日。界面上方有一个红色的圆形图标，显示有22条未读消息。页面标题为“消息通知”，右侧有一个蓝色链接“全部已读”。

通知列表中有三条消息：

1. 第一条消息的时间是2023年12月5日09:33:35，内容为：“[告警]区域[pt_region]中，虚拟密码机[***********]磁盘使用率不等于70”。这条消息右侧有两个按钮：“标为已读”和“去处理”，其中“标为已读”按钮被红色框标记。

2. 第二条消息的时间是2023年12月5日09:09:09，内容为：“[工单]oper对编号：GD6274764263360563206工单提交了一条评论”。这条消息右侧也有两个按钮：“标为已读”和“去处理”。

3. 第三条消息的时间也是2023年12月5日09:09:09，内容为：“[工单]编号：GD6274764263360563206的工单已撤销，撤销人：oper”。这条消息右侧同样有两个按钮：“标为已读”和“去处理”。

每条消息的右侧都有一个红色圆点，表示这些消息尚未阅读。
```
  
图3-10-7 密码产品删除入口  
图 3-11-1 消息标为已读

#### 3.12.1.2. 消息全 已读

点击铃铛按钮，在弹出的铃铛消息列表右上角点击全部已读按钮，可以将全部未读消息消息标为已读。

![](images/8681a4980fd2975270669011ec88dfc08f3c8d2b97b6a9d0a7e331317edf116d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个消息通知页面，页面顶部有一个红色圆圈，内有数字“20”，表示有20条未读消息。页面右上角显示了当前日期为2026年9月27日，并且有“操作员”和“平台操作员”的标签。

在页面的主体部分，有三条消息通知：

1. 第一条消息的时间戳是2023年12月5日09:33:35，内容是一个告警信息，指出在区域[pt_region]中，虚拟密码机[***********]的磁盘使用率不等于70。
2. 第二条消息的时间戳是2023年12月5日09:09:09，内容是一个工单信息，指出用户oper对编号为GD6274764263360563206的工单提交了一条评论。
3. 第三条消息的时间戳也是2023年12月5日09:09:09，内容是一个工单信息，指出编号为GD6274764263360563206的工单已被撤销，撤销人是oper。

每条消息右侧都有一个蓝色按钮，标有“去处理”。在第一条消息下方还有一个红色框，内有文字“全部已读”。在第三条消息下方有两个按钮，分别是“标为已读”和“去处理”。
```
  
图 3-11-2 消息全部已读  
图 3-11-3 处理消息

#### 3.12.1.3. 处理消息

点击铃铛按钮，在弹出的铃铛消息列表中点击对应消息的去处理按钮，可以跳转至对应消息的处理界面。

消息通知 全部已读  
[告警]区域[pt_region]中，虚拟密码机[10.20.3  
6.41]磁盘使用率不等于70去处理  
[工单]oper对编号：GD6274764263360563206  
工单提交了一条评论去处理  
[工单]编号：GD6274764263360563206的工单  
已撤销，撤销人：oper标为已读 去处理  
[工单]oper对编号：GD6274764263360563206  
工单提交了一条评论去处理  
[工单]oper新建了一条工单，工单编号：GD627

### 3.12.2. 消息管理

系统操作员登录之后，在消息管理列表可以对消息进行已读、删除、批量已读和批量删除的操作。

#### 3.12.2.1. 消息已读

在消息管理列表点击对应消息的已读按钮，即可将对应消息标为已读。

![](images/7cd45eee584d85c9dd3bf3985b763f5b3101f8eeb3ec9a90ba079422ac7ad75a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个消息管理界面，界面上有多个消息条目，每个条目包含消息来源、标题、内容、是否已读、创建时间和操作选项。消息来源包括工单和告警，标题和内容列显示了具体的消息信息，例如工单编号和告警详情。是否已读列显示了消息的阅读状态，创建时间列显示了消息的创建时间。操作列提供了“已读”和“删除”的选项，用户可以对消息进行相应的操作。此外，界面上还有批量已读和批量删除的按钮，以及查询和重置的功能按钮。
```
  
图 3-11-4 消息已读  
图 3-11-5 消息删除

#### 3.12.2.2. 消息删除

在消息管理列表点击对应消息的删除按钮，即可将对应消息删除。

<html><body><table><tr><td colspan="8">批里已读 批重耐除</td></tr><tr><td></td><td>消息来源</td><td>标题</td><td>内容</td><td>是否已读</td><td>创建时间</td><td>操作</td><td></td></tr><tr><td></td><td>工单</td><td>工单消息</td><td>oper对编号：GD62755524732215644</td><td>未读</td><td>2023-12-05 15:40:38</td><td>已读删除</td><td></td></tr><tr><td></td><td>工单</td><td>工单消息</td><td>oper新建了一条工单，工单编号：GD6…</td><td>未读</td><td>2023-12-05 15:40:37</td><td>已读 删除</td><td></td></tr><tr><td>□</td><td>告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的虚.</td><td>未读</td><td>2023-12-05 15:08:20</td><td>已读删除</td><td></td></tr><tr><td></td><td>告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的虚.</td><td>未读</td><td>2023-12-05 15:04:25</td><td>已读删除</td><td></td></tr><tr><td>□</td><td>告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的虚…</td><td>未读</td><td>2023-12-05 15:04:20</td><td>已读删除</td><td></td></tr><tr><td>□</td><td>告警</td><td>告警消息</td><td>租户[tenant]的密码产品[自动化预置-</td><td>未读</td><td>2023-12-05 14:44:55</td><td>已读删除</td><td></td></tr><tr><td>□</td><td>告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的密</td><td>未读</td><td>2023-12-05 14:27:43</td><td>已读删除</td><td></td></tr></table></body></html>

#### 3.12.2.3. 消息批量已读

在消息管理列表中选择多个消息，再点击左上角的批量已读按钮，即可将对应的多个消息批量已读。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

<html><body><table><tr><td colspan="2">批量已读</td><td colspan="6">批量删除</td></tr><tr><td>□</td><td colspan="2">消息来源</td><td>标题</td><td>内容</td><td>是否已读</td><td>创建时间</td><td>操作</td></tr><tr><td>√</td><td colspan="2">工单</td><td>工单消息</td><td>oper对编号：GD62755524732215644</td><td>未读</td><td>2023-12-05 15:40:38</td><td>已读删除</td></tr><tr><td>√</td><td colspan="2">工单</td><td>工单消息</td><td>oper新建了一条工单，工单编号：GD6……</td><td>未读</td><td>2023-12-05 15:40:37</td><td>已读删除</td></tr><tr><td>√</td><td colspan="2">告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的虚…</td><td>未读</td><td>2023-12-05 15:08:20</td><td>已读删除</td></tr><tr><td>√</td><td colspan="2">告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的虚…</td><td>未读</td><td>2023-12-05 15:04:25</td><td>已读删除</td></tr><tr><td>□</td><td colspan="2">告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的虚…</td><td>未读</td><td>2023-12-05 15:04:20</td><td>已读删除</td></tr><tr><td>□</td><td colspan="2">告警</td><td>告警消息</td><td>租户[tenant]的密码产品[自动化预置-</td><td>未读</td><td>2023-12-05 14:44:55</td><td></td></tr><tr><td>□</td><td colspan="2">告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的密…</td><td>未读</td><td>2023-12-05 14:27:43</td><td>已读删除</td></tr><tr><td>□</td><td colspan="2">工单</td><td>工单消息</td><td>oper对编号:GD62753806768449802</td><td>未读</td><td>2023-12-05 14:15:18</td><td>已读删除</td></tr><tr><td>□</td><td colspan="2">工单</td><td>工单消息</td><td>oper新建了一条工单，工单编号：GD6</td><td>未读</td><td></td><td>已读删除</td></tr><tr><td></td><td colspan="2"></td><td></td><td></td><td></td><td>2023-12-05 14:15:18</td><td>已读删除</td></tr></table></body></html>

#### 3.12.2.4. 消息批量删除

在消息管理列表中选择多个消息，再点击左上角的批量删除按钮，即可将对应的多个消息批量删除。

<html><body><table><tr><td>□</td><td>消息来源</td><td>标题</td><td>内容</td><td>是否已读</td><td>创建时间</td><td>操作</td></tr><tr><td></td><td>工单</td><td>工单消息</td><td>oper对编号：GD62755524732215644</td><td>未读</td><td>2023-12-05 15:40:38</td><td>已读删除</td></tr><tr><td>√</td><td>工单</td><td>工单消息</td><td>oper新建了一条工单，工单编号：GD6</td><td>未读</td><td>2023-12-05 15:40:37</td><td>已读删除</td></tr><tr><td>√</td><td>告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的虚…</td><td>未读</td><td>2023-12-05 15:08:20</td><td>已读删除</td></tr><tr><td>□</td><td>告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的虚</td><td>未读</td><td>2023-12-05 15:04:25</td><td>已读删除</td></tr><tr><td>□</td><td>告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的虚…</td><td>未读</td><td>2023-12-05 15:04:20</td><td>已读删除</td></tr><tr><td>□</td><td>告警</td><td>告警消息</td><td>租户[tenant]的密码产品[自动化预置</td><td>未读</td><td>2023-12-05 14:44:55</td><td>已读删除</td></tr><tr><td>□</td><td>告警</td><td>告警消息</td><td>区域[pt_region]中，租户[tenant]的密</td><td>未读</td><td>2023-12-05 14:27:43</td><td>已读删除</td></tr><tr><td>□</td><td>工单</td><td>工单消息</td><td>oper对编号：GD62753806768449802.</td><td>未读</td><td>2023-12-05 14:15:18</td><td>已读删除</td></tr></table></body></html>

## 3.13. 工单管理

以系统操作员身份登录系统，可以对工单进行管理。

### 3.13.1. 待处理工单

系统操作员登录之后，点击界面左边菜单栏工单管理->待处理工单按钮，进入待处理工单管理界面。

问题描述 工单类型 所属租户 Q查询号 工单编号 工单类型 所属租户 问题描述 提交时间 状态 操作

#### 3.13.1.1. 处理工单

点击对应工单（状态为“待处理“）的查看按钮进入工单详情界面，再点击右上角的开始处理按钮，即可将工单状态标记为“处理中”。

![](images/7630dd9cd765118b055f87a6670f6e1e12b7b5dc0f9b4fc66ca99d505c534ec3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示工单信息的截图，具体包括以下内容：

1. **工单信息**：
   - 工单编号：GD6275319605760493576
   - 产品名称：产品已删除或已下架
   - 手机号：13675314456
   - 问题描述：测试测试添加工单描述
   - 工单类型：申请密码服务工单
   - 申请数量：1
   - 邮箱：<EMAIL>
   - 租户名称：tenant
   - 组织机构：三未信安
   - 提交时间：2023-12-05 13:44:57
   - 联系人：测试名称
   - 工单状态：待处理
   - 处理人：（未指定）

2. **沟通记录**：
   - 用户“oper”在2023-12-05 13:44:58发送了一条消息：“测试测试添加工单描述”，并附带了一个名为“workorderAttachment.png”的附件。

3. **操作按钮**：
   - 右上角有一个红色边框的“开始处理”按钮。
   - 底部有一个输入框和一个“发送”按钮，用于用户输入和发送新的消息。
   - 输入框下方有一个“上传附件”的链接，用于上传文件。

这张图片展示了一个工单管理系统中的工单详情页面，包含了工单的基本信息、沟通记录以及相关的操作按钮。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图3-12-1 待处理工单管理界面  
图 3-12-2 开始处理工单

个返回 工单详情

系统操作员可以在工单详情界面下方与工单提交者进行沟通，在聊天框中编辑信息或点击上传附件按钮上传附件之后，再点击发送按钮即可发送信息。

![](images/6ac12d405c19cec547051d6a908871cb46f93fea86f4bacee86734a2c32acec1.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示工单详情的截图，具体信息如下：

- **工单编号**: GD6275380676844980229
- **工单类型**: 申请密码服务工单
- **产品名称**: 密钥管理服务
- **手机号**: 15611111111
- **问题描述**: 我需要开通密管服务
- **申请数量**: 100
- **租户名称**: tenant
- **组织机构**: sanssec
- **提交时间**: 2023-12-05 14:15:18
- **联系人**: 张三
- **处理人**: 平台操作员
- **工单状态**: 处理中

在沟通记录部分，可以看到用户（oper）和平台操作员之间的对话：

- 用户在2023-12-05 14:15:18发送消息：“我需要开通密管服务”
- 平台操作员在2023-12-05 14:18:57回复：“已为你开通密钥管理服务”，并附带了一个名为“密钥管理系统使用说明.docx”的附件。

页面底部有一个输入框和一个“发送”按钮，用户可以在此处继续输入信息或上传附件。
```
  
图 3-12-3 处理工单

#### 3.13.1.2. 工单处理完成

工单处理完成之后，可以在工单详情界面点击右上角的处理完成按钮，即可将工单处理完成。

![](images/7a29e8132ec2ff6bf0f593c7b80e250f8b3559ef4edf5a7999b6ea2f4936b177.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示工单信息和沟通记录的截图。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
个返回 工单详情  
图 3-12-4 工单处理完成

## 3.13.2. 已处理工单

系统操作员登录之后，点击界面左边菜单栏工单管理->已处理工单按钮，进入已处理工单管理界面。

工单编号 问题描述 工单类型 所属租户 Q查询号 工单编号 工单类型 所属租户 问题描述 提交时间 完成时间 操作

### 3.13.2.1. 查看工单

点击对应工单的查看按钮即可进入工单详情界面查看工单。

![](images/3c41b1c4bdee34c6e864515ae8c4973abbc1dff73866267369d856ccd2802e32.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个工单信息页面，具体内容如下：

### 工单信息
- **工单编号**: GD6275380676844980229
- **产品名称**: 密钥管理服务
- **手机号**: 15611111111
- **问题描述**: 我需要开通密管服务

- **工单类型**: 申请密码服务工单
- **申请数量**: 100
- **邮箱**: （未填写）
- **租户名称**: tenant
- **组织机构**: sansec
- **提交时间**: 2023-12-05 14:15:18
- **联系人**: 张三
- **工单状态**: 已处理
- **处理人**: 平台操作员

### 沟通记录
- **oper**: 我需要开通密管服务 (2023-12-05 14:15:18)
- **平台操作员**: 已为你开通密钥管理服务，并附上了《密钥管理系统使用说明.docx》文件 (2023-12-05 14:18:57)

这张图片展示了一个用户请求开通密钥管理服务的工单，以及平台操作员对此请求的处理和回复。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图3-12-5 已处理工单管理界面  
个返回 工单详情  
图 3-12-6 查看工单详情

## 3.14. 密评管理

### 3.14.1. 密码知识库

#### 3.14.1.1. 密码知识库列表

点击密码知识库，展示密码知识库列表，界面显示密码知识库名称，所属分类、文件类型、文件大小、备注、创建时间、是否支持租户访问（租户不显示），可根据知识库名称模糊查询，也可以选择知识库分类进行分类查询。

![](images/79e4d005504aa0d3bfd1bdf170cc05da8dfea7db76ecd1acd897d781eafe54d7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个文档管理系统的界面，列出了多个知识库条目及其详细信息。
```
  
图3-13-1 密码知识库列表界面

#### 3.14.1.2. 新增密码知识库

在密码知识库列表界面点击左上角的新增按钮，进入新增密码知识库界面，填写知识库名称、选择知识库分类、选择是否支持租户访问、上传文件(pdf、xls、xlsx、docx、mp4)，填写备注，点击提交，提示新增成功，密码知识库列表可看到新增一条记录。支持租户访问的登录租户侧可查看密码知识库相关数据。若不支持租户访问，租户侧无法看到该条记录。

![](images/dcaf07382424a8528593320fd50c71be4c664ca898a469a2ae7525aa5cd33838.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示新建知识库界面的截图。界面上有以下内容：

1. **知识库名称**：输入框中填写了“密评文档”。
2. **知识库分类**：下拉菜单中选择了“密评文档”。
3. **租户访问**：有一个开关按钮，当前处于开启状态。
4. **文件上传**：有一个蓝色的“上传文件”按钮，下方有提示信息说明支持上传的文件类型和大小限制（仅支持.pdf、.xls、.xlsx、.docx、.mp4格式，大小不超过50MB）。已经上传了一个名为“restful_api.docx”的文件。
5. **备注**：有一个多行文本输入框，提示用户可以在此输入备注信息，当前为空。
6. **底部按钮**：有两个按钮，分别是“取消”和“提交”。

整体界面简洁明了，用于创建新的知识库并上传相关文件。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.14.1.3. 编辑密码知识库

非租户在密码知识库列表界面点击一条数据信息后的编辑按钮，进入编辑密码知识库界面，可修改密码知识库的名称以及所属分类、备注。填写好点击提交，界面提示编辑成功

![](images/3badf20d0bd2dfac1649a3ef8e2be9ac2df126f1ea6c1040a0bb03923704fcfe.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个文档管理系统的界面，列出了多个文档的详细信息。表格中有以下列：

- **知识库名称**：列出了各个文档的名称，如“密评文档”、“test1111”等。
- **分类**：显示了文档的分类，大部分为“密评文档”，还有“标准文档”和“操作文档”。
- **文件类型**：列出了文档的格式，包括docx、mp4、pdf、doc、xls、xlsx等。
- **文件大小(M)**：显示了每个文档的大小，单位为兆字节（M），例如0.39M、35.23M等。
- **租户访问**：这一列有开关按钮，表示是否允许租户访问该文档，部分文档的开关是打开状态，部分是关闭状态。
- **备注**：这一列中有些文档有额外的备注信息，如“test1111”、“11”、“12345”、“xxxx”、“ww”等。
- **创建时间**：显示了文档的创建时间，格式为年-月-日 时:分:秒，例如2024-07-25 10:35:56。
- **操作**：这一列提供了对每个文档的操作选项，包括“预览”、“编辑”和“删除”。

在表格上方有一个“新增”按钮，可能用于添加新的文档。右上角有“查询”和“重置”按钮，用于搜索和重置筛选条件。表格底部显示了当前页码和总页数，以及每页显示的条目数。
```
  
图3-13-2 密码知识库新增界面  
图3-13-3 密码知识库编辑按钮

![](images/4c85101c93dccafaf9b1619b2882c93222de6f63ff0437bdf3cbb7b07f9e46ed.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“编辑知识库”界面的截图。界面上有三个主要部分：

1. **知识库名称**：这是一个必填项，当前输入的是“密评文档”。
2. **知识库分类**：这也是一个必填项，当前选择的是“密评文档”。
3. **备注**：这是一个可选填写的区域，当前为空，提示“请输入”，并且显示已输入字符数为0/100。

在界面底部有两个按钮：“取消”和“提交”。用户可以点击“取消”来放弃当前的编辑操作，或者点击“提交”来保存所做的更改。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

#### 3.14.1.4. 删除密码知识库

非租户在密码知识库列表界面点击一条数据信息后的删除按钮，界面提示界面提示“删除后无法恢复是否确认删除“，点击确定，界面提示删除成功，可看到知识库列表不存在已删除的知识库信息。

![](images/78ec2c0bf878185ef68533eb13cd7f3f27209f0e81eaa6fed84d0f7d727966df.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个文档管理系统的界面，列出了多个知识库文档的详细信息。表格中包含了以下列：知识库名称、分类、文件类型、文件大小（M）、租户访问、备注、创建时间以及操作选项。每个文档条目都有预览、编辑和删除的功能按钮。例如，第一个文档名为“密评文档”，分类为“密评文档”，文件类型为“docx”，文件大小为0.39M，租户访问状态为开启，没有备注，创建时间为2024-07-25 10:35:56。
```
  
图3-13-4 密码知识库编辑界面  
图3-13-5 密码知识库删除按钮

![](images/7cb98257c1bb2ccfed9a5675c3cd2b6f0b5c5a6af83651b8a11d04de323a8c7f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个提示框，内容为“删除后将无法恢复，是否确认删除？”。提示框的左上角有一个黄色的感叹号图标，表示这是一个警告或重要信息。右上角有一个关闭按钮（X）。提示框的底部有两个按钮，分别是“取消”和“确定”，用户可以选择是否继续进行删除操作。
```
  
图3-13-6 密码知识库删除界面

#### 3.14.1.1. 预览密码知识库

在密码知识库列表界面点击一条数据信息后的预览按钮，可查看上传的文件内容。

![](images/106a59f3b59ed033803a572309b63a956c63e62fb24775548e2be78caaeafc04.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个文档管理系统的界面，列出了多个知识库条目及其详细信息。
```
  
图3-13-7 密码知识库预览按钮

![](images/06a520d25ddd800f1012bb634a6a105de400286924d65b9df07129f8dc35178c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个视频预览界面。在界面的中央，有一个播放按钮，背景是一幅雨景图像，雨水滴落在地面上，周围是模糊的绿色植物和树木。界面的左侧是一个列表，列出了多个文件名，如“test1111”、“testxy.pdf”等，这些可能是用户上传或创建的知识库内容。右侧则显示了每个文件的操作选项，包括预览、编辑和删除。整个界面看起来像是一个知识库管理系统，用户可以在这里管理和查看各种类型的文件。
```
  
图3-13-8 密码知识库预览界面

### 3.14.2. 密码工具箱

#### 3.14.2.1. 流密码算法

对祖冲之加密/解密数据进行解密/加密。

![](images/a8d2c5a75dde065d38fc3bb75db357bb9497cefd3c981f75a38da89f34128a7f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个加密/解密工具的用户界面，具体是用于祖冲之加密算法的操作页面。界面上方有多个标签页，包括“首页”、“产品信息”、“消息管理”、“待处理工单”、“密码知识库”、“密评统计”、“流密码算法”和“杂凑算法”，当前选中的标签页是“流密码算法”。

在页面的主要部分，有一个标题为“祖冲之加密/解密数据”的区域，提示用户输入待加解密文本内容，并采用hex格式。下方有两个输入框，分别用于输入密钥和IV（初始化向量），同样要求以hex格式输入，密钥长度可以是128bit或256bit，IV的长度可以是128bit或200bit。

在输入框下方，有两个按钮，分别是“加密”和“解密”，用户可以根据需要选择进行相应的操作。在这些按钮下方，还有一个标题为“加密/解密结果”的区域，这里将显示操作后的结果。

此外，在页面的左下角还有一个“重置”按钮，用户可以点击它来清空所有输入和结果，以便进行新的操作。
```
  
图3-13-9 流密码算法界面

#### 3.14.2.2. 分组算法

提供使用 SM4、SM4GCM、SM7、DES/3DES、AES、AESGCM 算法对数据进行加解密。

![](images/62eb8b136dfcb7964311749f5be4cc86466ca8f5bc5bfcb1d6b5ead16896501c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个分组算法的列表，背景为黑色，文字为白色。列表中的选项包括：

1. SM4（当前被选中，背景为蓝色）
2. SM4GCM
3. SM7
4. DES/3DES
5. AES
6. AESGCM

这些选项都是不同的加密算法，用于数据的安全传输和存储。SM4、SM4GCM和SM7是中国国家密码管理局发布的商用密码算法，而DES/3DES、AES和AESGCM则是国际上广泛使用的加密标准。
```
  
图 3-13-10 分组算法界面

#### 3.14.2.3. 杂凑算法

支持使用杂凑算法对数据进行计算。

![](images/eeac70f76ce299c07f8a948144466fd67089ebb3d8c4e2a79c899a35930c9cd5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示杂凑算法界面的图片，
```
  
图 3-13-11 杂凑算法界面

#### 3.14.2.4. 非对称算法

支持使用 SM2 算法、SM9 算法、RSA、DSA、ECDSA、x448 算法、ed448 算法、x25519算法、ed25519 算法进行各类计算。

![](images/0319a4764b62ececf847fafce485ff9bb9d409d2177b988b781fa9791969db47.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个关于非对称算法的列表。列表的标题是“非对称算法”，下面列出了几种具体的算法，包括：

1. SM2算法
2. SM9算法
3. RSA
4. DSA
5. ECDSA
6. x448算法
7. ed448算法
8. x25519算法
9. ed25519算法

这些算法都是非对称加密算法，广泛应用于数据加密、数字签名和密钥交换等领域。其中，SM2和SM9是中国国家密码管理局制定的商用密码算法标准；RSA、DSA和ECDSA是国际上常用的非对称加密算法；x448、ed448、x25519和ed25519则是基于椭圆曲线的现代加密算法，具有更高的安全性和效率。
```
  
图 3-13-12 非对称算法界面

#### 3.14.2.5. 证书工具

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

提供查看证书信息、验证证书链、证书验签、tls 报文解析功能。

![](images/88b965bdcc6712127f25c7044e6ff73c920b283540349524949e74363ca3fe50.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“证书工具”的界面，界面上有四个选项，分别是：

1. 查看证书信息
2. 证书链验证
3. 证书验签
4. tls报文解析

这些选项可能用于对数字证书进行各种操作和检查。
```
  
图 3-13-13 证书工具界面

![](images/40951a0983ef2e23e7800ebc3489fcfc4d9079e11d19264674c2490554ea762b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
识别失败
```
  
图3-13-14 查看证书信息界面

![](images/128030155f2aa577c8264b5aeed9c7281fa1a4a5004ae1227b74f59aca75c445.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个网页界面，主要用于上传和验证证书文件。界面上方有一个红色星号标记的“证书文件”标签，旁边有一个蓝色的“选择证书”按钮，提示用户需要选择一个证书文件进行上传。在页面中央，有一个蓝色的矩形按钮，上面写着“验证证书链”，这表明用户可以点击此按钮来验证所选证书的证书链。在按钮下方，有一个标有“结果”的文本框，用于显示验证证书链后的结果信息。整个界面设计简洁明了，主要功能是帮助用户完成证书文件的上传和验证操作。
```
  
图3-13-15 查看证书信息界面

![](images/7addd9300299f9ab6f129fdf708aa7e147c99f0d5ff03b1f596511acb2c7620a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个数字签名验证的界面。
```
  
首页

![](images/076311f195b860b5483b89c451e54360560932dc5a2a21c590c72092fdd74828.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个简单的用户界面，可能是一个网页或应用程序的一部分。界面上有一个蓝色的按钮，上面写着“报文解析”，这表明用户可以点击这个按钮来解析某种类型的报文或数据。在按钮的上方，有一行小字，写着“* 报文文件 + 选择文件”，这可能是提示用户需要先选择一个报文文件才能进行解析操作。整体界面设计简洁，主要功能集中在报文解析上。
```
  
图 3-13-16 证书验签界面  
图 3-13-17 tls 报文解析界面

#### 3.14.2.6. 编码转换

可进行多种编码格式的转换。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/f32a69b9480edf4a9ddb14eac0094f4bc6fe46337d986a07d0088a0d36559a3a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示编码转换工具界面的图片。
```
  
图 3-13-18 编码转换界面

### 3.14.3. 密评统计

密评统计主要是对进行密评的应用、单位各个进度进行展现。

#### 3.14.3.1. 密评数量统计

![](images/3e311b5676e3912f26adbe2e6d058133e9546517e6268b8fa87bc8dc5dffa3dc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个统计信息的界面，包含了四个不同的数据指标：

1. 应用总数：图标是一个蓝色的立方体，旁边的文字说明是“应用总数”，数值为5。
2. 测评完成数：图标是一个绿色的立方体，旁边的文字说明是“测评完成数”，数值为2。
3. 密改单位数：图标是一个紫色的立方体，旁边的文字说明是“密改单位数”，数值为1。
4. 单位数：图标是一个黄色的立方体，旁边的文字说明是“单位数”，数值为1。

这些数据可能用于某个系统或平台的应用管理、测评进度和单位统计等方面。
```
  
图 3-13-19 密评数量统计

![](images/b50287b9858afb2d6e341d49118b324f8624e3d10aaad4f778cf64c2349c329b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个环形图，标题为“等保等级应用分布”。环形图中只有一个蓝色的部分，表示“一级”等级的应用分布。根据图中的标注，“一级”的比例为100%，这意味着所有的应用都属于“一级”等级。右下角有一个蓝色的方块和“一级”的文字，作为图例说明。
```
  
图3-13-20 等保等级应用分布

密评数量统计展示密评应用个数、测评完成的应用数、密评包含的单位数与进行密改的单位数。等保等级展示应用的等保等级分布。

#### 3.14.3.2. 单位密评进度统计

对密评单位的进度进行统计展示。

![](images/b6fa701f225ffa756f887cf38ab57117bfc4ae72fe52a8dc074a4c360783f8c1.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示各单位密评完成进度统计的图表。图表的标题是“各单位密评完成进度统计”，左侧的纵轴表示完成率百分比，从0%到100%，右侧的横轴上有一个标签“三末”。在图表中，有一个蓝色的柱状图，代表“三末”的完成情况，其高度对应于40%的完成率。图表上方有一个悬浮提示框，显示“三末 完成率: 40%”。这表明在所统计的单位中，“三末”单位的密评完成进度为40%。
```
  
图3-13-21 各单位密评完成进度统计

#### 3.14.3.3. 密评单位统计

对注册的单位数和开始密改的单位数进行统计展示。

![](images/06949960b98ee67f4a5aaf9f2e5ef22f089813964e93d95e84f925ba8548950e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张折线图，标题为“密评单位统计”。图表中有两条折线，分别代表“已注册单位数”和“开始密改单位数”。横轴表示日期，从9月13日到9月19日；纵轴表示单位数量，数值范围从0到1.2。

从图表中可以看出：
- “已注册单位数”的折线在9月13日开始迅速上升，在9月14日达到约0.9的峰值后趋于平稳。
- “开始密改单位数”的折线在9月13日开始缓慢上升，在9月14日达到约0.9的峰值后也趋于平稳。

两条折线在9月14日后基本重合，表明从这一天开始，已注册单位数和开始密改单位数的变化趋势一致。
```
  
图 3-13-22 密评单位统计

#### 3.14.3.4. 密评完成趋势

对应用总数和测评完成的应用数进行统计展示。

![](images/0cab5cf0da408d3471478b0a6a548489bccaaceffafa0ffc3da403505f37316b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片是一张折线图，标题为“密评完成趋势”。图表中有两条折线，分别代表“应用总数”和“测评完成总数”。

- 蓝色实线表示“应用总数”，数值稳定在5左右，没有明显变化。
- 深蓝色虚线表示“测评完成总数”，从9月13日的0开始逐渐上升，在9月14日达到1后保持稳定，直到9月18日再次上升，在9月19日达到接近2的数值。

横轴表示日期，从9月13日到9月19日；纵轴表示数量，范围从0到5。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

## 3.15. 密评模板

### 3.15.1. 密评进度模板

#### 3.15.1.1. 密评进度模板列表

点击密评模板下的密评进度模板菜单，界面展示所有的密评进度模板，展示字段有模板标识、模板名称、创建时间、备注。界面上方支持模板名称的模糊查询。

![](images/32176d913f066f9f09f20d88220e7d270b6a67b0c57f5a9d0ef98cd0d72497e9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个模板管理界面，列出了多个模板的详细信息。表格中有四列：模板标识、模板名称、创建时间和备注。每一行代表一个模板，包含了该模板的唯一标识符、名称、创建的具体时间以及一些额外的备注信息。在右侧的操作列中，提供了对每个模板进行编辑、复制和更多操作的选项。此外，在页面顶部有一个搜索框，可以输入模板名称进行查询，以及一个“新建”按钮，用于创建新的模板。页面底部显示了当前页码和总页数，表明这是一个分页显示的列表。
```
  
图 3-13-22 密评单位统计  
图3-14-1 密评进度模板列表界面

#### 3.15.1.2. 新增密评进度模板

点击密评模板下的密评进度模板菜单，点击左上方的新增按钮，展示新增密评进度模板界面，输入模板的模板标识、模板名称、备注，点击确定，界面显示新增成功。

![](images/b18881719b0da313891ade3d87dd87d2c0fafc2ec71abc5cb324d8e2d4fb4970.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个模板管理界面，界面上有一个表格列出了多个模板的信息。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/2b0732d3ca39b8b196a2547236df373918c685dcb46549fb64aac2c4106efabc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新建密评进度模板”界面的图片。界面上有三个输入框，分别用于输入模板标识、模板名称和备注。模板标识和模板名称是必填项，用红色星号标记。备注栏下方显示当前已输入字符数为0，最大可输入100个字符。界面底部有两个按钮，一个是蓝色的“确定”按钮，另一个是灰色的“取消”按钮。右上角有一个关闭按钮（X）。
```
  
图3-14-2 密评进度模板新增按钮  
图3-14-3 密评进度模板新增界面

#### 3.15.1.3. 编辑密评进度模板

点击密评模板下的密评进度模板菜单，点击列表数据后的编辑按钮，展示密评进度模板编辑界面，可修改模板的模板标识、模板名称、备注信息。点击确定，界面显示编辑成功。

![](images/267e1dc9f6b57966c52a0f4a38e8240ab656a1cb3584c8836ca4ad6b1c544449.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个模板管理界面，列出了多个模板的详细信息。表格中有四列：模板标识、模板名称、创建时间和备注。每一行代表一个模板，包含了该模板的唯一标识符、名称、创建的具体时间以及一些额外的备注信息。在右侧的操作列中，提供了对每个模板进行编辑、复制和更多操作的选项。例如，对于模板标识为“cccc1”的模板，其名称是“俺是模板3”，创建时间为2024年7月24日9点54分26秒，当前选中了对该模板的编辑操作。整个界面设计简洁明了，方便用户管理和操作这些模板。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/18a8b5c9bc2e2bf843242f67d8dea5fd77967346aabf339985dc8cb6ffb04cfb.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示模板创建或编辑界面的截图。界面上有三个主要部分：

1. **模板标识**：这是一个必填字段，当前输入的值为“cccc1”。
2. **模板名称**：这也是一个必填字段，当前输入的值为“俺是模板3”。
3. **备注**：这是一个可选字段，提示用户可以在此输入备注信息，当前没有输入任何内容，并且显示了字符限制为0/100。

在界面的底部有两个按钮：“确定”和“取消”，分别用于确认操作和取消操作。
```
  
图3-14-4 密评进度模板编辑按钮  
图3-14-5 密评进度模板编辑界面

#### 3.15.1.4. 复 密评进度模板

点击密评模板下的密评进度模板菜单，点击模板数据后的复制按钮，界面弹出复制模板界面，填写新模板的名称，点击确定，界面显示复制成功，可复制模板标识和配置项。

![](images/94ffebd6e6ce8dcc20621231ecfba6fac269a3039a5808e32ffcef8051b0d9cb.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个模板管理界面，列出了多个模板的详细信息。表格中有四列：模板标识、模板名称、创建时间和备注。每一行代表一个模板，包含了该模板的唯一标识符、名称、创建的具体时间以及一些额外的备注信息。在操作列中，每个模板都有编辑、复制和更多选项，用户可以对模板进行相应的操作。此外，页面顶部有一个搜索框，用户可以通过输入模板名称来查找特定的模板。右上角有查询和重置按钮，方便用户进行数据筛选和页面刷新。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/8c08a46bd41f7f2dfe483cc0732daf92f0a1a12073035737288fd5d9d93897d6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“复制模板”的对话框。对话框中包含以下内容：

1. 标题：“复制模板”
2. 一个标签“模板名称”，后面跟着文本“俺是模板31”
3. 一个带有星号的标签“新模板名称”，后面是一个输入框，输入框中也显示了文本“俺是模板31”
4. 底部有两个按钮：“取消”和“提交”

这个对话框似乎用于创建一个新的模板，用户可以输入新的模板名称并提交。
```
  
图3-14-6 密评进度模板复制按钮  
图3-14-7 密评进度模板复制界面

#### 3.15.1.5. 配置密评进度模板

点击密评模板下的密评进度模板按钮，点击模板数据后的更多-》配置，进入配置界面，可配置模板中项目准备、方案编制、改造实施、密码评测步骤的具体过程。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/d52c76f5ffc2d7ab37a1a0c6ecca72c791d9f84d5841bc725039b489b32f15c3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个模板管理界面，列出了多个模板的详细信息。表格中有四列：模板标识、模板名称、创建时间和备注。每一行代表一个模板，例如“cccc1”、“cccc”、“test001”等。在操作列中，有编辑、复制和更多选项，其中“更多”选项下拉菜单中包含“配置”和“删除”两个功能。
```
  
图3-14-8 密评进度模板配置按钮  
图3-14-9 密评进度模板配置界面

② ③   
串 0 B B   
0 m 0   
8 0 O 0 P 0

#### 3.15.1.6. 删除密评进度模板

点击密评模板下的密评进度模板按钮，点击模板数据后的更多-》删除按钮，界面提示删除后将无法恢复，是否确认删除，点击确定可删除该条数据。

![](images/5dee8d8ea9a4d8186fe70a2eff43676e064ec79afd3a62128ae7521d624b286e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个模板管理界面，列出了多个模板的详细信息。表格中有四列：模板标识、模板名称、创建时间和备注。每行代表一个模板，包含了模板的唯一标识符、名称、创建的具体时间以及一些额外的备注信息。此外，在表格的右侧有一个操作列，提供了对每个模板进行编辑、复制和更多操作的选项。在页面的顶部有一个搜索框，可以输入模板名称进行查询，以及一个“新建”按钮，用于创建新的模板。页面底部显示了当前页码和总页数，以及每页显示的条目数量，方便用户进行分页浏览。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/2798684eba26297f9ec6d5a3389745d31d285941ac18c02f92fa52138a4fd2dc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个提示框，内容是关于删除操作的确认。提示框的标题是“提示”，在标题下方有一个黄色的感叹号图标，旁边的文字写着“删除后将无法恢复，是否确认删除？”。在提示框的底部有两个按钮，左边的是灰色的“取消”按钮，右边的是蓝色的“确定”按钮。右上角有一个关闭按钮（X）。这个提示框通常出现在用户尝试删除某些数据或文件时，提醒用户一旦删除将无法恢复，需要确认是否继续进行删除操作。
```
  
图3-14-10 密评进度模板删除按钮

## 3.15.2. 密评要求模板

### 3.15.2.1. 密评要求模板列表

![](images/b3aba9ada4d5a187cba2568d2d54b679dbb2a2b269f328ad859849c8dea1e2d5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个网页界面，界面上有一个表格，表格的标题是“模板名称”，“密评等级”，“默认”，“创建时间”，“备注”和“操作”。表格中有五行数据，每行分别对应一个模板。第一行是“等保一级默认模板（不要动）”，密评等级为一级，默认为是，创建时间为2024-07-15 11:30:35；第二行是“等保二级默认模板（不要动）”，密评等级为二级，默认为是，创建时间为2024-07-22 16:09:08；第三行是“等保三级默认模板（不要动）”，密评等级为三级，默认为是，创建时间为2024-07-22 16:15:43；第四行是“等保四级默认模板（不要动）”，密评等级为四级，默认为是，创建时间为2024-07-22 16:25:41；第五行是“测试等保二级模板-用这个测试”，密评等级为二级，默认为否，创建时间为2024-07-22 16:54:35。在“操作”列中，每行都有“编辑”，“复制”和“更多》”的选项。在表格的右下角，显示了当前页面共有5条记录，每页显示20条记录，当前页码为1。
```
  
图3-14-11 密评进度模板删除界面

点击密评模板下的密评要求模板菜单，界面展示所有的密评要求模板，展示字段有模板名称、密评等级、默认、创建时间、备注。界面上方有模板名称模糊搜索栏。

### 3.15.2.2. 新增密评要求模板

点击密评模板下的密评要求模板菜单，点击左上方的新增按钮，展示新增密评要求模板界面，输入模板的模板名称、选择密评等级，填写备注，点击确定，界面显示新增成功。

![](images/2d98331ca57fddb4f4f542896925f89268ce8ad27e6907f1529018e5eb9a3728.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个网页界面，主要用于管理和查看模板信息。界面上方有一个搜索框和两个按钮：“查询”和“重置”。在搜索框下方，有一个红色边框的“新建”按钮，点击该按钮可以创建新的模板。

表格列出了多个模板的信息，包括模板名称、密评等级、默认设置、创建时间和备注等字段。具体来说：

- 模板名称：列出了五个模板，分别是“等保一级默认模板（不要动）”、“等保二级默认模板（不要动）”、“等保三级默认模板（不要动）”、“等保四级默认模板（不要动）”和“测试等保二级模板-用这个测试”。
- 密评等级：分别对应一至四级和二级。
- 默认：前四个模板的默认设置为“是”，最后一个模板的默认设置为“否”。
- 创建时间：所有模板的创建时间都在2024年7月15日到2024年7月22日之间。
- 备注：目前没有填写任何备注信息。
- 操作：每个模板后面都有“编辑”、“复制”和“更多”三个操作选项，用户可以通过这些选项对模板进行进一步的管理。

此外，页面底部显示了当前页码和总页数，以及每页显示的条目数量。
```
  
图3-14-12 密评要求模板列表界面

![](images/52de9afcdd154922185b85dcea96f4a7338f4d6431a4bfd09a0f2a2ab91c7ec8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示新建密评要求模板的界面截图。界面上有三个主要部分：

1. **模板名称**：这是一个必填项，用户需要在旁边的输入框中输入模板的名称。
2. **密评等级**：这也是一个必填项，用户需要从四个选项中选择一个密评等级，分别是“一级”、“二级”、“三级”和“四级”。每个选项前面都有一个单选按钮供用户选择。
3. **备注**：这是一个可选项，用户可以在旁边的文本框中输入一些备注信息。文本框下方显示当前已输入的字符数为0，最大允许输入100个字符。

在界面的底部有两个按钮：“确定”和“取消”。用户点击“确定”按钮可以提交填写的信息，点击“取消”按钮则会放弃当前操作并关闭此界面。
```
  
图3-14-13 密评要求模板新增按钮  
图3-14-14 密评要求模板新增列表

### 3.15.2.3. 编辑密评要求模板

点击密评模板下的密评要求模板菜单，点解列表数据后的编辑按钮，展示密评要求模板编辑界面，可修改模板的模板名称、密评等级、备注，点击确定，界面显示编辑成功。

![](images/1bf8777f12992cc7d96f8fb22df9d8c34fb47cc7521881de5ffcd4ef3b57db76.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个网页界面，界面上有一个表格，表格的标题行包括“模板名称”、“密评等级”、“默认”、“创建时间”、“备注”和“操作”。表格中有五条记录，每条记录对应一个模板，模板名称分别为“等保一级默认模板（不要动）”、“等保二级默认模板（不要动）”、“等保三级默认模板（不要动）”、“等保四级默认模板（不要动）”和“测试等保二级模板-用这个测试”。每个模板的密评等级从一级到四级不等，前四个模板的“默认”列都标记为“是”，而最后一个模板标记为“否”。所有模板的创建时间都在2024年7月15日或22日。在“操作”列中，每个模板都有“编辑”、“复制”和“更多”的选项，其中第一个模板的“编辑”选项被红色边框突出显示。页面右上角有“查询”和“重置”按钮，左上角有一个“新建”按钮。页面底部显示了当前页码和总页数信息。
```
  
图3-14-15 密评要求模板修改按钮

![](images/49aa032bcc107b25d081fb9f7e5beb74012f29fb0bac60b8f5ff6525730b6457.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个表单界面，包含以下内容：

1. **模板名称**：有一个输入框，里面已经填写了“等保一级默认模板（不要动）”。

2. **密评等级**：有四个选项，分别是“一级”、“二级”、“三级”和“四级”。其中，“一级”被选中。

3. **备注**：有一个文本框，提示用户“请输入备注”，当前为空。文本框右下角显示“0/100”，表示可以输入最多100个字符。

4. **按钮**：底部有两个按钮，一个是蓝色的“确定”按钮，另一个是灰色的“取消”按钮。

这个表单可能用于设置或编辑某个模板的相关信息。
```
  
修改密评要求模板  
图3-14-16 密评要求模板修改界面

### 3.15.2.4. 复 密评要求模板

点击密评模板下的密评要求模板菜单，点击模板数据后的复制按钮，界面弹出复制模板界面，填写新模板的名称，点击确定，界面显示复制成功，可复制模板标识和配置项。

![](images/05e2363a3d9b21f978c71765602d0138900ac632fc1664d86119d482e74f01be.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个管理界面，主要用于管理和编辑不同级别的默认模板。界面上方有一个搜索框，用户可以输入模板名称进行查询，并有“查询”和“重置”按钮。在搜索框下方，有一个表格列出了多个模板的信息，包括模板名称、临评等级、是否为默认模板、创建时间和备注等。每个模板后面都有“编辑”、“复制”和“更多”操作选项。表格中列出了五个模板，分别是等保一至四级的默认模板和一个测试模板，它们的临评等级从一级到四级不等，创建时间也各不相同。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/b65912b9500be8fb5a3fdad32853aabff9b65f39036951354a71b8f1ef06b8a7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“复制模板”的对话框。对话框中包含以下内容：

1. **标题**：在对话框的左上角，有一个标题“复制模板”。
2. **模板名称**：在对话框的中间部分，有一行文字“模板名称 等保一级默认模板（不要动）”，表示当前选择的模板名称。
3. **新模板名称**：在“模板名称”下方，有一个带星号的标签“* 新模板名称”，旁边有一个输入框，输入框中已经填写了“等保一级默认模板（不要动）”。
4. **按钮**：在对话框的底部，有两个按钮：
   - 左边是一个灰色的“取消”按钮。
   - 右边是一个蓝色的“提交”按钮。

这个对话框看起来是一个用于复制现有模板并为新模板命名的界面。用户可以在这里输入新的模板名称，并通过点击“提交”按钮来完成操作。如果不想进行任何更改，可以点击“取消”按钮退出。
```
  
图3-14-17 密评要求模板复制按钮  
图3-14-18 密评要求模板复制界面

### 3.15.2.5. 配置密评进度模板

点击密评模板下的密评进度模板菜单，点击模板数据后的更多-》配置，进入配置页面，展示已经配置好的密评要求。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/a409fe63b6531bc0884f2c288a5a0603969e3263b6b49177c02629e8f182d902.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个网页界面，主要内容是一个表格，列出了不同级别的默认模板信息。表格的列标题包括“模板名称”、“密评等级”、“默认”、“创建时间”、“备注”和“操作”。每一行代表一个模板，例如“等保一级默认模板（不要动）”，其密评等级为一级，默认设置为是，创建时间为2024-07-15 11:30:35。在“操作”列中，有编辑、复制、更多等选项，其中“更多”选项下拉菜单中包含了配置和删除两个选项。页面右上角有查询和重置按钮，左上角有一个新建按钮。
```
  
图3-14-19 密评要求模板配置按钮

![](images/23c5455f800efe7c1e2a3ecb1dbbb0090f2c32ec08b772834afc69c6b46c94a7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个模板管理界面，列出了多个模板的详细信息。表格中有四列：模板标识、模板名称、创建时间和备注。每一行代表一个模板，包含了该模板的唯一标识符、名称、创建的具体时间以及一些额外的备注信息。在右侧的操作列中，提供了对每个模板进行编辑、复制和更多操作的选项。此外，在页面顶部有一个搜索框，可以输入模板名称进行查询，以及一个“新建”按钮，用于创建新的模板。页面底部显示了当前页码和总页数，表明这是一个分页显示的列表。
```
  
图3-14-20 密评要求模板配置界面

### 3.15.2.6. 删除密评进度模板

点击密评模板下的密评进度模板菜单，点击模板数据后的更多-》删除，界面弹出提示框“删除后将无法恢复，是否确认删除“,点击确定可删除该模板。

![](images/a09203b667dbedd2a10be52f9fab4245cf4ad0feb861c4401078e3859f4e6053.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个网页界面，界面上有一个表格，列出了多个模板的信息。表格的列标题包括“模板名称”、“密评等级”、“默认”、“创建时间”、“备注”和“操作”。每一行代表一个模板，列出了模板的名称、密评等级、是否为默认模板、创建时间以及一些操作选项，如编辑、复制和更多。在“操作”列中，有一个下拉菜单，显示了“配置”选项，并且“配置”选项被红色框标记出来。页面顶部有一个搜索框和两个按钮，分别是“查询”和“重置”。页面底部显示了当前页码和总页数信息。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/25db89b19f4d2319a48ebca09d199d5eaa37f5fbe9e85512a822bfc548533eb0.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个提示框，内容是关于删除操作的确认。提示框的标题是“提示”，左侧有一个黄色的感叹号图标，表示这是一个需要注意的信息。提示框的主要内容是：“删除后将无法恢复，是否确认删除？”这表明用户正在进行一个不可逆的删除操作，需要确认是否继续。

在提示框的底部有两个按钮，分别是“取消”和“确定”。用户可以选择点击“取消”来放弃删除操作，或者点击“确定”来确认并执行删除操作。右上角有一个关闭按钮（X），用户也可以点击它来关闭提示框。
```
  
图3-14-21 密评要求模板删除按钮  
图3-14-22 密评要求模板删除界面

### 3.15.2.7. 添加密评要求

点击密评模板下的密评进度模板菜单，界面展示所有的密评进度模板，点击模板数据后的更多-》配置，进入配置页面，点击左上角的添加密评要求，弹出新建密评要求条目对话框，输入密评要求、密评条目、风险等级、密评要求级别、调研选项、改进建议以及排序，点击确定。界面显示添加成功。

![](images/3c0a5b61c79ff978c2c52040cd44004319e977523560c42ab2c3ed2cee074a14.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个关于技术要求的表格，主要分为物理和环境安全、网络和通信安全、设备和计算安全以及应用和数据安全四个部分。每个部分都列出了具体的要求条目、要求级别、风险等级、调研选项和建议，并提供了编辑和删除的操作选项。例如，在物理和环境安全部分，提到了身份鉴别和电子门禁记录数据存储完整性；在网络和通信安全部分，涉及了身份鉴别、通信数据完整性、通信过程中重要数据的完整性和网络边界访问控制信息的完整性；在设备和计算安全部分，则包括了身份鉴别、系统资源访问控制信息完整和日志记录完整性。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/99bd240c3f425da2e8d520a6894278bd3174c1288bbddef5088ff8a3be851fd1.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示密评要求条目设置界面的图片。界面上有多个选项和输入框，用于配置密评要求的具体内容。具体内容包括：

1. **密评要求**：这是一个下拉菜单，提示用户“请选择密评要求”。
2. **密评条目**：这也是一个下拉菜单，提示用户“请选择密评条目”。
3. **风险等级**：这里有三个单选按钮，分别是“低”、“中”和“高”，用户可以选择其中一个来表示风险等级。
4. **密评要求级别**：这里有三个单选按钮，分别是“可”、“宜”和“应”，用户可以选择其中一个来表示密评要求的级别。
5. **调研选项**：这是一个标签，但没有具体的选项或输入框。
6. **改进建议**：这是一个标签，但没有具体的选项或输入框。
7. **排序**：这是一个输入框，提示用户“请输入”，可能用于输入排序的数值或文本。
8. **确定和取消按钮**：在界面底部有两个按钮，一个是蓝色的“确定”按钮，另一个是灰色的“取消”按钮，用户可以通过点击这两个按钮来确认或取消操作。

这个界面看起来是一个用于配置或编辑密评要求条目的工具，用户可以根据需要选择和输入相关信息。
```
  
图3-14-23 添加密评要求按钮  
图3-14-24 添加密评要求界面

### 3.15.2.8. 编辑密评要求

点击密评模板下的密评进度模板菜单，界面展示所有的密评进度模板，点击模板数据后的更多-》配置，进入配置页面，点击条目后的编辑按钮，进入密评要求编辑界面，可编辑修改风险等级、密评要求级别、调研选项、改进建议以及排序，点击确定，界面提示编辑成功。

![](images/40166f3ccb9f9f6252ec773e994e205d8fa584a0e391660f48a7885c9832e0e5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个名为“等保一级默认模板（不要动）-模板详情”的界面，主要用于管理和编辑信息安全技术要求。界面分为几个部分，每个部分列出了不同的技术要求类别及其详细信息。

1. **物理和环境安全（2）**：这一部分包括两个要求条目，分别是身份鉴别和电子门禁记录数据存储完整性，都标记为“可”级别风险，风险等级为低，并且已经支持相应的功能。建议是采购特定产品以支持这些功能。

2. **网络和通信安全（4）**：这部分列出了四个要求条目，包括身份鉴别、通信数据完整性、通信过程中重要数据的完整性和网络边界访问控制信息的完整性，同样标记为“可”级别风险，风险等级为低，且已支持相关功能。建议同样是采购特定产品来支持这些功能。

3. **设备和计算安全（3）**：这一部分包含三个要求条目，分别是身份鉴别、系统资源访问控制信息完整和日志记录完整性，标记为“可”级别风险，风险等级为低，且已支持相关功能。建议是采购特定产品以支持这些功能。

每个条目后面都有“编辑”和“删除”按钮，允许用户对这些要求进行修改或移除。此外，界面顶部有一个“添加密评要求”的按钮，可能用于添加新的安全要求。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/cb6e7265226d229edfd328bb7d5f40e58bea92633fb2946edc15c4103f4de18c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“编辑密评要求信息”界面的截图，具体内容如下：

1. **标题**：编辑密评要求信息

2. **内容**：
   - **密评条目**：身份鉴别
   - **风险等级**：选择了“低”，其他选项为“中”和“高”
   - **密评要求级别**：选择了“可”，其他选项为“宜”和“应”
   - **调研选项**：选择了“已支持身份鉴别”，另一个未选中的选项为“dqwqdqw”
   - **改进建议**：选择了“采购xxx产品支持身份鉴别”
   - **排序**：输入框内填写了数字“1”

3. **按钮**：
   - 取消
   - 提交（蓝色按钮）

这张图片展示了一个用于编辑密评要求信息的表单界面，用户可以在此选择不同的选项并提交。
```
  
图3-14-25 编辑密评要求按钮  
图3-14-26 编辑密评要求界面

### 3.15.2.9. 删除密评要求

点击密评模板下的密评进度模板菜单，界面展示所有的密评进度模板，点击模板数据后的更多-》配置，进入配置页面，点击要求条目后的删除按钮，弹出提示框“删除后将无法恢复，是否确认删除“，点击确定，可删除该条密评要求条目。

←等保—级默认模板（不要动）-模板详情

![](images/a192596992fa2b6df8f2388bd186dc3459c638048c8d907c4e8584daf697d62f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个关于技术要求的表格，主要分为四个部分：物理和环境安全、网络和通信安全、设备和计算安全以及应用和数据安全。每个部分都列出了具体的要求条目、要求级别、风险等级、调研选项、建议和操作。

1. **物理和环境安全**：
   - 身份鉴别：要求级别为“可”，风险等级为“低”，调研选项为“已支持身份鉴别”，建议为“采购xxx产品支持身份鉴别”，操作有“编辑”和“删除”。
   - 电子门禁记录数据存储完整性：要求级别为“可”，风险等级为“低”，调研选项为“已支持电子门禁记录数据存储完整性”，建议为“采购xxx产品支持电子门禁记录数据存储完整性”，操作有“编辑”和“删除”。

2. **网络和通信安全**：
   - 身份鉴别：要求级别为“可”，风险等级为“低”，调研选项为“已支持身份鉴别”，建议为“采购xxx产品支持身份鉴别”，操作有“编辑”和“删除”。
   - 通信数据完整性：要求级别为“可”，风险等级为“低”，调研选项为“已支持通信数据完整性”，建议为“采购xxx产品支持通信数据完整性”，操作有“编辑”和“删除”。
   - 通信过程中重要数据的完整性：要求级别为“可”，风险等级为“低”，调研选项为“已支持通信过程中重要数据的完整性”，建议为“采购xxx产品支持通信过程中重要数据的完整性”，操作有“编辑”和“删除”。
   - 网络边界访问控制信息的完整性：要求级别为“可”，风险等级为“低”，调研选项为“已支持网络边界访问控制信息的完整性”，建议为“采购xxx产品支持网络边界访问控制信息的完整性”，操作有“编辑”和“删除”。

3. **设备和计算安全**：
   - 身份鉴别：要求级别为“可”，风险等级为“低”，调研选项为“已支持身份鉴别”，建议为“采购xxx产品支持身份鉴别”，操作有“编辑”和“删除”。
   - 系统资源访问控制信息完整：要求级别为“可”，风险等级为“低”，调研选项为“已支持系统资源访问控制信息完整”，建议为“采购xxx产品支持系统资源访问控制信息完整”，操作有“编辑”和“删除”。
   - 日志记录完整性：要求级别为“可”，风险等级为“低”，调研选项为“已支持日志记录完整性”，建议为“采购xxx产品支持日志记录完整性”，操作有“编辑”和“删除”。

4. **应用和数据安全**：
   - 这部分在图片中没有完全显示出来，但可以看到标题为“【技术要求】应用和数据安全（6）”。

每个部分的要求条目都强调了身份鉴别、数据完整性和访问控制的重要性，并且都建议采购支持这些功能的产品。操作列提供了“编辑”和“删除”的选项，方便对要求进行修改或移除。
```
  
图3-14-27 删除密评要求按钮

![](images/ee7d6f74258d982e47c34a51d78b45edc68e906b625a48f7036cc672cc628de9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示删除确认对话框的图片。对话框的标题是“提示”，内容是一个警告信息，写着“删除后将无法恢复，是否确认删除？”旁边有一个黄色的感叹号图标，表示这是一个重要的警告。对话框底部有两个按钮，一个是灰色的“取消”按钮，另一个是蓝色的“确定”按钮。用户需要选择其中一个按钮来决定是否继续删除操作。右上角有一个关闭按钮（X），可以用来关闭这个对话框。
```
  
图3-14-28 删除密评要求界面

## 3.15.3. 交付文档模板

### ********. 交付文档模板列表

点击密评模板下的交付文档模板菜单，页面展示所有的交付文档模板，展示字段有模板名称、业务类型、文件类型、备注、创建时间。页面上方有根据模板名称模糊查询的搜索框。

![](images/b5f3a82e2cea4d12aad4b9ab2d7191653f6d187dd19ceaf1794ed6c5f60c3f95.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个文档管理系统的界面，列出了多个模板文件及其相关信息。表格中有五个主要列：“模板名称”、“业务类型”、“文件类型”、“备注”、“创建时间”和“操作”。每个模板都有一个对应的业务类型、文件类型和创建时间，并且在“操作”列中提供了下载和删除的选项。

具体来说，模板名称包括“基线产品_三方组件维护表.xlsx”、“密码安全管理制度.zip”、“服务接口文档.zip”等，涵盖了系统密评改造FAQ、系统密码安全管理制度、密钥接口文档等多种业务类型。文件类型主要是.xlsx和.docx格式，也有.zip压缩包。创建时间从2024年7月22日到2024年7月25日不等，显示了这些模板的创建日期。

此外，在表格底部有一个分页导航栏，显示当前页面是第1页，共20条记录，每页显示20条。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### ********. 新增文档交付模板

点击密评模板下的交付文档模板菜单，点击页面左上角的新增按钮，弹出新增交付模板界面，页面选择业务类型，上传文档模板文件，填写备注，点击确定。界面显示新建成功。

![](images/d8a06ef30f398c3d4a3b3540e2ed94615ff3b301af7b02ec7b15f3e1d7693b1c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个文件管理界面，列出了多个与系统安全和密码管理相关的文档。每个文档都有其模板名称、业务类型、文件类型、备注、创建时间和操作选项。例如，有“基线产品_三方组件维护表.xlsx”、“密码安全管理制度.zip”、“服务接口文档.zip”等文件，它们的业务类型包括系统密评改造FAQ、系统密码安全管理制度、密服接口文档等。创建时间从2024年7月22日到2024年7月25日不等，所有文件都提供了下载和删除的操作选项。
```
  
图3-14-29 交付文档列表界面  
图3-14-30 新建交付模板按钮

![](images/fc855cabb6f5a5baed03ae7fd6ab992d7377db958efef4b0216d712c18d45de6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“新建交付模板”界面的截图。界面上有以下内容：

1. **业务类型**：这是一个下拉菜单，提示用户“请选择业务类型”，表示用户需要从下拉菜单中选择一个业务类型。

2. **文件**：旁边有一个蓝色的“+ 上传附件”按钮，表示用户可以点击此按钮上传相关文件。

3. **备注**：这是一个文本输入框，提示用户“请输入备注”，并且在右下角显示当前输入字数为0/100，表示用户可以在该框内输入最多100个字符的备注信息。

4. **取消和提交按钮**：在界面底部有两个按钮，一个是灰色的“取消”按钮，另一个是蓝色的“提交”按钮。用户可以点击“取消”按钮放弃当前操作，或者点击“提交”按钮完成新建交付模板的操作。

这个界面主要用于用户创建新的交付模板，需要填写业务类型、上传相关文件并添加备注信息。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.15.3.3. 下载文档交付模板

点击密评模板下的交付文档模板菜单，点击模板记录后的下载按钮，可下载上传的文档交付模板。

![](images/1f75cd62c66b92cb4a8fb70f249f32317f5e0b5ed1c547bcce519643302f797e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个文档管理系统的界面，列出了多个模板文件的详细信息。表格中有五个主要列：模板名称、业务类型、文件类型、备注和创建时间。每个模板旁边都有“下载”和“删除”的操作按钮。例如，第一个模板是“基线产品_三方组件维护表.xlsx”，业务类型为“系统密评改造FAQ”，创建时间为2024-07-25 11:31:39。其他模板包括各种与密码安全管理和系统改造相关的文档，如“密码安全管理制度.zip”、“服务接口文档.zip”等，它们的创建时间也依次列出。
```
  
图3-14-31 新建交付模板界面  
图 3-14-32 下载文档交付模板界面

### ********. 删除文档交付模板

点击密评模板下的交付文档模板菜单，点击模板记录后的删除按钮，弹出提示框“删除后将无法恢复，是否确认删除“，点击确定可删除该条文档模板记录。

![](images/84976a449b58c3040c77c5e76defaeb00f9d2a297ba20bea6d77a88b1b197195.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个文件管理界面，列出了多个与系统安全和密码应用相关的文档。每个文档都有其模板名称、业务类型、文件类型、备注、创建时间和操作选项。例如，有“基线产品_三方组件维护表.xlsx”、“密码安全管理制度.zip”、“服务接口文档.zip”等文件，它们的业务类型包括系统密评改造FAQ、系统密码安全管理制度、密服接口文档等。创建时间从2024年7月23日到2024年7月24日不等，所有文件都提供了下载和删除的操作选项。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/79e356b9e9b2c548635af1f0e78812ceb5af98edbdcda49faf3d4d1c4dfb34dc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示删除确认对话框的图片。对话框的标题是“提示”，内容是一个警告信息，写着“删除后将无法恢复，是否确认删除？”旁边有一个黄色的感叹号图标，表示这是一个重要的警告。对话框底部有两个按钮，一个是灰色的“取消”按钮，另一个是蓝色的“确定”按钮，用户可以通过点击这两个按钮来选择是否继续进行删除操作。右上角有一个关闭按钮（X），可以用来关闭这个对话框。
```
  
图 3-14-33 删除文档交付模板按钮  
图 3-14-34 删除文档交付模板界面

# 3.16. 密评基础数据

以系统操作员登录系统，能够进行密评基础数据的管理。

## 3.16.1. 密评机构管理

系统操作员登录后，点击界面左边菜单栏密评基础数据->密评机构管理按钮，进入密评机构管理界面。

### 3.16.1.1. 密评机构列表

打开密评机构管理菜单，页面展示密评机构信息列表。

![](images/7bf1478543d74290ee2c661bc6758508738ea83d3056891f1f344189860fa501.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个机构信息管理界面，包含一个表格和一些操作按钮。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.16.1.2. 添加密评机构

点击密评机构列表左上方的新建按钮，弹窗打开新增密评机构表单，填写密评机构信息后，点击确定按钮保存密评机构信息。

![](images/ec6eb25171da238f6410c3bb461f449ca1ec69089043a301ed6c4c72e8aa4b07.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个网页界面，主要用于管理和创建机构信息。界面上有一个弹出窗口，标题为“新建密评机构”，用户可以通过这个窗口输入新的机构信息。弹出窗口中有多个输入框，包括机构名称、地址、联系人、联系方式、固定电话、邮箱和备注等字段，其中机构名称和联系方式是必填项（用星号标记）。在弹出窗口的底部有两个按钮，分别是“确定”和“取消”。

背景中可以看到一个表格，列出了已有的机构信息，包括机构名称、地址、联系人、备注、创建时间和操作选项。表格中有两条记录，一条是名为“igtest00121”的机构，另一条是名为“Sansec Cryptographic A...”的机构，地址为“123 Main Street, Anyto...”，联系人为“John Doe”。每条记录后面都有编辑和删除的选项。

此外，在页面的右上角有“查询”和“重置”按钮，用户可以使用这些按钮来搜索或重置表单内容。页面底部显示了当前页码和总页数，以及每页显示的记录数量。
```
  
图 3-15-1 密评机构列表  
图3-15-2 密评机构添加界面

### 3.16.1.3. 编辑密评机构

点击密评机构列表右侧操作列的编辑按钮，弹窗打开编辑密评机构表单，可以修改密评机构的各种信息，修改密评机构信息后，点击确定按钮保存编辑的信息。

![](images/f4ca7c6bd4d44d0f997634079d4ee6db22d2e38dafdaded6c81cc4eb253cefdc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个网页界面，主要用于管理和编辑机构信息。界面上有一个弹出窗口，标题为“修改密评机构”，用户正在编辑一个名为“Sansec Cryptographic Agency”的机构信息。该机构的地址是“123 Main Street, Anytown, USA”，联系人是“John Doe”，联系方式包括电话号码“+1-234-567-8901”和电子邮件“<EMAIL>”。在备注栏中，有一条示例备注：“This is a sample remark。”。

在弹出窗口的底部有两个按钮，一个是蓝色的“确定”按钮，另一个是灰色的“取消”按钮，用户可以通过点击这两个按钮来确认或取消对机构信息的修改。

背景中可以看到一个表格，列出了两个机构的信息，分别是“lgtest00121”和“Sansec Cryptographic Agency”，每个机构都有对应的地址、联系人、备注和创建时间等信息。表格右侧有“编辑”和“删除”两个操作选项，用户可以对这些机构进行进一步的管理。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.16.1.4. 删除密评机构

点击密评机构列表右侧操作列删除按钮，提示是否确认删除，点击确认删除，删除密评机构信息。

![](images/fcaee718ac23dd8e8caa7ac16fb7b41471d6470424f7ec23c5b0da8c6a7f8951.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个管理机构信息的网页或应用程序。界面上方有一个搜索栏和两个按钮，分别是“查询”和“重置”。在搜索栏下方，有一个表格列出了机构的相关信息，包括机构名称、地址、联系人、联系方式、固定电话、邮箱、备注以及创建时间等字段。

表格中有两条记录，第一条记录的机构名称为“igtest00121”，地址为“igtest0012”，联系人为“igtest”，联系方式为“igtest0012”，邮箱为“<EMAIL>”，备注为“igtest0012”，创建时间为“2024-07-16 13:47:10”。第二条记录的机构名称为“Sanssec Cryptographic A...”，地址为“123 Main Street, Anyto...”，联系人为“John Doe”，联系方式为“+1-234-567-8901”，邮箱为“<EMAIL>”，备注为“This is a sample remark.”，创建时间为“2024-07-16 11:01:51”。

在每条记录的最右侧，有两个操作按钮，分别是“编辑”和“删除”。当前界面中，用户似乎正在尝试删除第二条记录，因此弹出了一个提示框，询问用户是否确认删除，提示框中有“取消”和“确定”两个选项按钮。
```
  
图3-15-3 密评机构编辑界面  
图3-15-4 密评机构删除界面

## 3.16.2. 密码厂商管理

系统操作员登录后，点击界面左边菜单栏密评基础数据->密码厂商管理按钮，进入密码厂商管理界面。

### 3.16.2.1. 密码厂商列表

打开密码厂商管理菜单，页面展示密码厂商信息列表。

![](images/640bcc0c73dd9e60d2bd2f5caa9e64f3c44ce7eb920dbfee4cc4377a68fcc798.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示厂商信息的表格截图，表格中列出了厂商名称、地址、联系人、联系方式、固定电话、邮箱、备注、创建时间和操作等字段。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.16.2.2. 添加密码厂商

点击密码厂商列表左上方的新建按钮，弹窗打开新增密码厂商表单，添加密码厂商信息后，点击确定按钮，保存密码厂商信息。

![](images/e3145514c5e70dca116df7cb8055a1b9301c5beeb42722440dded35c43337ead.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机软件界面，具体来说是一个用于管理厂商信息的系统。界面上有一个弹出窗口，标题为“新建厂商”，表明用户正在创建一个新的厂商记录。弹出窗口中有多个输入框，包括厂商名称、地址、联系人、联系方式、固定电话、邮箱和备注等字段，用户需要填写这些信息来完成新厂商的添加。

在弹出窗口的下方有两个按钮，分别是“确定”和“取消”，用户可以点击“确定”来保存新厂商的信息，或者点击“取消”来放弃当前操作。

背景中可以看到一个表格，列出了已有的厂商信息，包括厂商名称、地址、联系人、备注、创建时间和操作等列。表格中的数据表明已经有几个厂商被添加到系统中，并且每个厂商记录都有相应的创建时间。此外，表格右侧的操作列提供了编辑和删除功能，允许用户对现有的厂商信息进行修改或删除。

整体来看，这个界面设计简洁明了，功能齐全，方便用户管理和维护厂商信息。
```
  
图3-15-5 密码厂商添加界面  
图3-15-6 密码厂商添加界面

### 3.16.2.3. 编辑密码厂商

点击密码厂商列表右侧操作列编辑按钮，弹窗打开密码厂商编辑菜单，可以修改密码厂商信息，修改密码厂商信息后，点击确定按钮，保存编辑的密码厂商信息。

![](images/cdbec2a2deda02d85ed19e51c0544ccadc2e1002d712d37222fccc52323f02af.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个厂商信息管理系统的界面，当前正在编辑一个名为“lgtest”的厂商信息。界面上方有一个搜索框，可以输入厂商名称进行查询，右侧有两个按钮，分别是“查询”和“重置”。在界面的左侧，有一个表格列出了厂商名称、地址和联系人等信息，当前显示了三条记录，分别是“11”、“俺是名字”和“lgtest”。在界面的右侧，有一个弹出的对话框，标题为“修改厂商”，里面包含了厂商名称、地址、联系人、联系方式、固定电话、邮箱和备注等字段的信息，这些信息都是可以编辑的。在对话框的底部，有两个按钮，分别是“确定”和“取消”，用户可以通过点击这两个按钮来保存或取消对厂商信息的修改。在界面的右下角，有一个分页导航栏，显示当前页面是第一页，总共有3条记录，每页显示20条记录。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.16.2.4. 删除密码厂商

点击密码厂商列表右侧操作列删除按钮，提示是否确认删除，点击确认删除，删除密码厂商信息。

![](images/9398c85ff618cb65cff824fbde2058d02846ca2d2c1c80248c9f004eec4c6972.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个厂商信息管理界面，界面上有一个表格列出了厂商的详细信息，包括厂商名称、地址、联系人、联系方式、固定电话、邮箱、备注和创建时间等。表格中有三条记录，分别是“11”、“俺是名字”和“lgtest”，每条记录都有相应的详细信息。

在界面的右上角，有一个蓝色的“查询”按钮和一个灰色的“重置”按钮。在表格的上方，有一个“新建”按钮，用于添加新的厂商信息。

当前界面上弹出一个提示框，内容为“删除后将无法恢复，是否确认删除？”，提示框中有两个按钮，一个是“取消”，另一个是“确定”。这表明用户正在尝试删除某条厂商信息，并且系统在进行最后的确认提示。

此外，在表格的右侧，每条记录后面都有“编辑”和“删除”两个操作选项，用户可以通过这些选项对厂商信息进行修改或删除操作。在表格的底部，有分页导航，显示当前页面是第一页，共有3条记录，每页显示20条记录。
```
  
图3-15-7 密码厂商编辑界面  
图3-15-8 密码厂商编辑界面

## 3.16.3. 枚举管理

系统操作员登录后，点击界面左边菜单栏密评基础数据->枚举管理按钮，进入枚举管理界面。

### 3.16.3.1. 枚举列表

打开枚举管理菜单，页面展示枚举信息列表。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/b47d2aa7bdb7a6c10167f067c303286c827a3310a498097fe627af85444d1554.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示数据表格的截图，表格标题为“枚举值”，列出了多个与系统业务相关的枚举值及其详细信息。
```
  
图 3-15-9 枚举列表

### 3.16.3.2. 添加枚举

点击枚举列表左上方的新建按钮，弹窗打开新增枚举表单，添加枚举信息后，点击确定按钮，保存乜剧信息。

![](images/5de1e24f57146036dbaaafeac4338026317830a144ff485668c63c42b7663a04.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个名为“新建枚举”的弹出窗口，位于一个表格的上方。弹出窗口中有多个输入框和标签，包括“枚举值”、“枚举标识”、“分组标识”、“排序”和“备注”，每个标签旁边都有相应的输入框供用户填写信息。在弹出窗口的底部有两个按钮，分别是“确定”和“取消”。

背景中的表格列出了多个枚举值、枚举标识和分组标识，以及创建时间和操作选项。表格中的数据包括一些测试条目和系统相关的条目，如“密评交付模板”、“租户密评项目进展日报”、“系统密评调研表”等。每行数据右侧都有“编辑”和“删除”两个操作按钮。

此外，在界面的右上角还有“查询”和“重置”两个按钮，可能用于对表格中的数据进行搜索和重置操作。整体来看，这个界面似乎是一个用于管理和编辑枚举值的系统管理工具。
```
  
图 3-15-10 枚举管理列表

### 3.16.3.3. 编辑枚举

点击密码厂商列表右侧操作列编辑按钮，弹窗打开密码厂商编辑菜单，可以修改枚举的枚举值和枚举分组标识等信息，修枚举信息后，点击确定按钮，保存编辑的枚举信息。

![](images/8639bab74e3cdb1f59025858dd5ce81b2abb79a308c6ae3c8780c2a636fdb764.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个数据库或系统管理的后台操作页面。页面的主要部分是一个表格，列出了多个条目，每个条目包含“枚举值”、“枚举标识”和“分组标识”等信息。例如，可以看到一些条目如“密评交付模板”、“租户密评项目进展日报”、“系统密评调研表”等，它们分别对应不同的枚举值和标识。

在页面的中央，有一个弹出的对话框，标题为“修改枚举”，这表明用户正在编辑或添加一个新的枚举值。对话框内有多个输入字段，包括“枚举值”、“枚举标识”、“分组标识”、“排序”和“备注”。当前填写的信息显示“枚举值”为“系统密评改造指南”，“枚举标识”为“5”，“分组标识”为“DELIVER_BUSINESS_TYPE”，“排序”为“0”，而“备注”字段为空。

此外，在表格的右侧，有一列显示了每个条目的创建时间，以及“编辑”和“删除”按钮，允许用户对这些条目进行进一步的操作。整个界面设计简洁明了，方便用户管理和维护系统中的各种枚举值和相关数据。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.16.3.4. 启用或禁用枚举

点击枚举列表中，启用列的开关按钮，控制枚举项是否启用。

![](images/4d53cc76bac6b04989000fda7f2d797ff896c6cfdd88f92cc6912a7f7c05a1a9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个数据管理界面，其中包含一个表格，列出了多个枚举值及其相关信息。表格的列包括枚举值、枚举标识、分组标识、启用状态、排序、备注、创建时间和操作选项。在“启用”列中，有一些行的状态被标记为启用（蓝色开关），而其他行则未启用（灰色开关）。红色框标出了部分未启用的行，提示用户注意这些状态。此外，页面顶部有一个绿色的消息框，显示“操作成功”，表明最近的操作已经完成。右上角有“查询”和“重置”按钮，用于数据筛选和重置操作。
```
  
图 3-15-11 枚举编辑界面  
图3-15-12 启用与禁用枚举项

### 3.16.3.5. 删除枚举

点击枚举列表右侧操作列删除按钮，提示是否确认删除，点击确认删除，删除枚举信息。

![](images/085fb8ddf72da33a32122c353189f5fdc0e1551175c0725cae97ec38e26a615c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个管理系统的操作页面。页面上有一个表格，列出了多个枚举值及其相关信息，包括枚举标识、分组标识、启用状态、排序、备注和创建时间等。每行数据右侧都有“编辑”和“删除”两个操作按钮。

在页面的中央，有一个弹出的提示框，内容为“删除后将无法恢复，是否确认删除？”，并提供了“取消”和“确定”两个选项按钮。这表明用户正在尝试删除某条记录，并且系统要求用户确认这一操作。

此外，页面顶部有一个搜索栏，用户可以输入枚举值进行查询，旁边还有“查询”和“重置”按钮。左上角还有一个“新建”按钮，可能用于添加新的枚举值记录。

整体来看，这个界面主要用于管理和维护一个枚举值列表，允许用户查看、编辑、添加和删除记录。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

## 3.16.4. 密评要求体系

系统操作员登录后，点击界面左边菜单栏密评基础数据->密评要求体系管理按钮，进入密评要求体系管理界面。

### 3.16.4.1. 密评要求体系列表

打开密评要求体系管理菜单，页面展示密评要求体系信息列表。

![](images/5c51ecf1a5afbd40747b4780957a52adb75c59d359be20f6cf9abfcd91294ac9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个管理界面，主要用于管理和查看各种密评要求。界面上方有一个搜索栏，用户可以输入密评要求名称和选择密评要求类型进行查询。右侧有两个按钮，分别是“查询”和“重置”。

在搜索栏下方，有一个表格列出了所有的密评要求。表格的列标题包括“密评要求名称”、“密评要求类型”、“创建时间”、“备注”和“操作”。每一行代表一个具体的密评要求，例如“物理和环境安全”、“网络和通信安全”等。在“密评要求类型”列中，大部分的要求被标记为“技术要求”，而“管理制度”、“人员管理”、“建设运行”和“应急处理”则被标记为“管理要求”。

在“操作”列中，每一条密评要求都有两个选项：“编辑”和“删除”，用户可以通过点击这些链接来对密评要求进行相应的操作。

此外，在表格的右下角，有一个分页导航，显示当前页面是第1页，总共有8条记录，每页显示20条。用户可以通过这个导航来切换不同的页面查看更多的密评要求。
```
  
图 3-15-13 删除枚举界面  
图3-15-14 密评要求体系列表

### 3.16.4.2. 添加密评要求体系

点击密评要求体系列表左上方的新建按钮，弹窗打开新增密评要求体系表单，添加密评要求体系信息后，点击确定按钮，保存密评要求体系信息。

![](images/d38fa6afa6f20159224c9052a220eea16b7ff695e5321dfb7e0264d0063e1336.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机软件界面，具体来说是一个用于创建或编辑“密评要求”的窗口。这个窗口包含几个输入字段和按钮：

1. **密评要求类型**：这是一个下拉菜单，用户可以从中选择不同的密评要求类型。
2. **密评要求名称**：这是一个文本框，用户需要在这里输入密评要求的名称。
3. **备注**：这是一个多行文本框，用户可以在这里输入一些额外的备注信息，最大输入长度为100个字符。

在窗口的底部有两个按钮：
- **确定**：点击此按钮将保存输入的信息并关闭窗口。
- **取消**：点击此按钮将放弃任何更改并关闭窗口。

背景中可以看到一个表格，列出了不同类型的密评要求及其对应的管理要求或技术要求，以及操作选项（如编辑和删除）。表格中的内容包括物理和环境安全、网络和通信安全、设备和计算安全、应用和数据安全、管理制度、人员管理、建设运行和应急处理等类别。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.16.4.3. 编辑密评要求体系

点击密评要求体系列表右侧操作列编辑按钮，弹窗打开密码厂商编辑菜单，可以修改密评要求体系的各种信息，修改密评要求体系信息后，点击确定按钮，保存编辑的密评要求体系信息。

![](images/49481114f13bc3f3d83abeaad89281eb5645e6ed98de73107dd295854805f890.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机软件界面，具体来说是一个用于管理和编辑密评要求的系统。界面上方有一个搜索栏和两个按钮，分别是“查询”和“重置”。在搜索栏下方，有一个表格列出了各种密评要求名称及其对应的类型，包括物理和环境安全、网络和通信安全、设备和计算安全、应用和数据安全、管理制度、人员管理、建设运行和应急处理等，这些都被归类为技术要求或管理要求。

在表格的右侧，有一列操作选项，每个条目都有“编辑”和“删除”两个按钮。当前，一个名为“网络和通信安全”的密评要求正在被编辑，这从弹出的对话框中可以看出。这个对话框标题为“修改密评要求”，包含三个输入字段：“密评要求类型”、“密评要求名称”和“备注”。用户可以选择密评要求类型（当前选择的是“技术要求”），输入或修改密评要求名称（当前显示的是“网络和通信安全”），并在备注字段中添加额外的信息（当前为空）。对话框底部有两个按钮，“确定”和“取消”，分别用于保存更改或放弃编辑。

此外，在界面的右下角，有一个分页导航，显示当前页面是第一页，总共有8条记录，每页显示20条。
```
  
图3-15-15 添加密评要求体系界面  
图3-15-16 编辑密评要求体系界面

### 3.16.4.4. 删除密评要求体系

点击密评要求体系列表右侧操作列删除按钮，提示是否确认删除，点击确认删除，删除密评要求体系信息。

![](images/8a2504a5a035a3182498e91ba3e3c39b007a9c990087b7206b55818adf3034c4.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机软件界面，具体来说是一个管理密评要求的系统界面。界面上方有一个搜索栏和两个按钮，分别是“查询”和“重置”。在搜索栏下方，有一个表格列出了多个密评要求名称及其类型，包括物理和环境安全、网络和通信安全、设备和计算安全、应用和数据安全、管理制度、人员管理、建设运行和应急处理等，这些都被归类为技术要求或管理要求。

在表格的右侧，有一个“操作”列，每个条目下都有“编辑”和“删除”两个选项。当前，用户似乎正在尝试删除某个条目，因此弹出了一个提示框，询问是否确认删除，因为删除后将无法恢复。提示框中有两个按钮，一个是“取消”，另一个是“确定”。

此外，在界面的底部，有一个分页导航栏，显示当前页面是第一页，总共有8条记录，每页显示20条。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

## 3.16.5. 密评要求条目

系统操作员登录后，点击界面左边菜单栏密评基础数据->密评要求条目管理按钮，进入密评要求条目管理界面。

### 3.16.5.1. 密评要求条目列表

打开密评要求条目管理菜单，页面展示密评要求条目信息列表。

![](images/02e3a4213dae4a6a982f93792c3f49becd57f3cc8e74bd66b6c41cc82e6ae6cf.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个表格，表格的标题是“要求条目”，表格中列出了多个与安全相关的项目及其所属的评估要求体系和等级。表格的列标题包括：要求条目、所属测评要求体系、第一级、第二级、第三级、第四级、备注和操作。每一行代表一个具体的安全要求条目，例如身份鉴别、电子门禁记录数据存储完整性、视频监控记录存储数据完整性等。每个条目下都有其对应的所属测评要求体系，如物理和环境安全、网络和通信安全、设备和计算安全等。在第一级到第四级的列中，用“可”、“应”、“直”等字样表示该条目在不同等级下的要求状态。在备注列中，有一些条目有额外的说明，如“test”。在操作列中，每个条目都有“编辑”、“调研选项”和“更多”的链接，用于对该条目的进一步操作。
```
  
图3-15-17 删除密评要求体系界面  
图3-15-18 密评要求条目列表

### 3.16.5.2. 添加密评要求条目

点击密评要求条目列表左上方的新建按钮，弹窗打开新增密评要求条目表单，添加密评要求条目信息后，点击确定按钮，保存密评要求条目信息。

![](images/91ec4173b227abf069a57640c68a66102659a878a0bd7e445834ffa64b94f5ff.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个关于新建密评条目的弹窗对话框。在这个对话框中，用户可以输入要求条目、选择所属密评要求体系，并对第一级到第四级的要求进行选择（可、宜、应）。此外，还有一个备注栏供用户输入额外信息，当前备注栏中有一个名为“test”的条目。

在背景中，我们可以看到一个表格，列出了多个要求条目及其所属的密评要求体系和第一级的要求。这些条目包括身份鉴别、电子门禁记录数据存储完整性、视频监控记录存储数据完整性等，涉及物理和环境安全、网络和通信安全、设备和计算安全等多个领域。每个条目后面都有一个“可”字，表示它们的第一级要求是“可”。表格右侧有编辑、调研选项和更多操作的链接。

界面右上角有两个按钮，分别是“查询”和“重置”，可能用于搜索特定的条目或清除当前的筛选条件。整个界面看起来像是一个用于管理和编辑信息安全评估要求的系统。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.16.5.3. 编辑密评要求条目

点击密评要求条目列表右侧操作列编辑按钮，弹窗打开密码要求条目编辑菜单，可以修改密评要求条目的各种信息，修改密评要求条目信息后，点击确定按钮，保存编辑的密评要求条目信息。

![](images/eb2dd8406dadcbe247039ef415e986b469f160e295f61e8b0c7f9200aaa6c722.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机软件界面，具体来说是一个用于管理和编辑安全评估要求条目的系统。界面上方有一个搜索框和两个按钮，分别是“查询”和“重置”。在主界面的左侧，有一个表格列出了多个要求条目及其所属的密评要求体系和第一级分类，例如“身份鉴别”、“电子门禁记录数据存储完整...”等，这些条目主要涉及物理和环境安全、网络和通信安全以及设备和计算安全等领域。

在主界面的右侧，有一个弹出窗口，标题为“修改密评条目”，用户可以在这里编辑特定的要求条目。在这个窗口中，用户需要填写或选择以下信息：

- 要求条目：当前显示为“身份鉴别”。
- 所属密评要求体系：当前选择的是“网络和通信安全”。
- 第一级至第四级：每个级别都有三个选项：“可”、“直”和“应”，用户可以通过单选按钮进行选择。当前的选择情况是：第一级选择了“可”，第二级选择了“直”，第三级和第四级都选择了“应”。
- 备注：这是一个文本输入框，用户可以在此输入备注信息，当前显示为空白。

在弹出窗口的底部有两个按钮：“确定”和“取消”，用户可以点击“确定”来保存所做的更改，或者点击“取消”来关闭窗口而不保存。

此外，在主界面的右下角，还有一个分页导航栏，显示当前页面是第1页，总共有41条记录，每页显示20条。用户可以通过点击数字或使用箭头按钮来切换不同的页面。
```
  
图3-15-19 添加密评要求条目界面  
图3-15-20 编辑密评要求条目界面

### 3.16.5.4. 删除密评要求条目

点击密评要求条目列表右侧操作列删除按钮，提示是否确认删除，点击确认删除，删除密评要求条目信息。

![](images/6923bad62d04c4f3a9a678f7714f9bacffbc523cbc50b49c23e68a3976123d20.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机软件界面，具体来说是一个关于安全评估要求的管理界面。界面上方有一个搜索框和两个按钮，分别是“查询”和“重置”。在搜索框旁边有一个“新建”的按钮。

界面的主要部分是一个表格，列出了各种安全评估要求条目及其所属的评估要求体系、第一级到第四级的评估结果、备注以及操作选项。表格中的条目包括身份鉴别、电子门禁记录数据存储完整性、视频监控记录数据存储完整性等，这些条目被分为物理和环境安全、网络和通信安全、设备和计算安全等类别。

在表格的右下角，有一个弹出的提示框，内容是“删除后将无法恢复，是否确认删除？”，并提供了“取消”和“确定”两个选项供用户选择。

此外，在界面的底部，有一个分页导航栏，显示当前页面为第1页，总共有41条记录，每页显示20条。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

## 3.16.6. 调研选项管理

系统操作员登录后，点击界面左边菜单栏密评基础数据->密评要求条目管理按钮，进入密评要求条目管理界面，点击密评要求条目右侧操作列调研选项按钮，进入调研选项管理界面。

![](images/f20111901846d5ebf1dacbab4c7efbaeaa031cf9e8de8170b1d792b39a2d94df.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示网络安全评估要求的表格截图，表格列出了不同级别的安全要求项目及其所属的评估体系。
```
  
图3-15-21 删除密评要求条目界面  
图3-15-22 调研选项界面入口

### 3.16.6.1. 调研选项列表

打开密评要求条目管理菜单，点击密评要求条目操作列的调研选项按钮，进入调研选项页面，展示该密评要求条目对应的调研选项列表。

![](images/852bd690d6961a875fe4a78ea72dffe65cb467bd9140ff28e7fb2ffc154221c9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个电子门禁记录数据存储完整性的调研选项界面。在界面的顶部，有一个标题栏，上面写着“电子门禁记录数据存储完整性-调研选项”。在标题栏下方，有一个按钮，上面写着“新建”，旁边有一个绿色的加号图标。

在界面的主要部分，有一个表格，表格的列标题分别是“调研选项”、“备注”和“操作”。在“调研选项”列中，有一行内容，写着“已支持电子门禁记录数据存储完整性”。在“操作”列中，有两个链接，分别是“编辑”和“删除”。

整个界面看起来像是一个管理系统的一部分，用于管理和查看电子门禁记录数据存储完整性的调研选项。用户可以通过点击“新建”按钮来添加新的调研选项，通过点击“编辑”链接来修改现有的调研选项，或者通过点击“删除”链接来删除不需要的调研选项。
```
  
  
图 3-15-23 调研选项列表

### 3.16.6.2. 添加调研选项

在调研选项列表页面，点击左上角的新建按钮，弹出新增调研选项表单，添加调研选项信息后，点击确定按钮，保存调研选项信息。

![](images/b6330e597e2b4f7f7ab7f855cf9acde073b56ed284d817464a9f8288018d9c5b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机软件界面，具体来说是一个弹出的对话框，标题为“新建密评条目调研选项”。这个对话框包含两个输入区域和两个按钮。

1. **调研选项**：这是一个必填项（由星号*标识），提示用户“请输入调研选项”，意味着用户需要在这里输入与调研相关的选项内容。

2. **备注**：这是一个可选的文本输入区域，提示用户“请输入备注”，用户可以在这里添加额外的说明或注释。在备注区域的右下角，有一个计数器显示“0/100”，这表示用户最多可以输入100个字符。

3. **按钮**：对话框底部有两个按钮，“确定”和“取消”。点击“确定”按钮将确认并提交用户在对话框中输入的信息；点击“取消”按钮则会关闭对话框，不保存任何更改。

背景部分显示了软件的其他功能区域，包括“新建”、“调研选项”等标签，以及一些关于电子门禁记录数据存储完整性的信息，但这些内容被当前的对话框遮挡，不是图片的主要焦点。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图3-15-24 添加调研选项界面

### 3.16.6.3. 编辑调研选项

点击调研选项列表右侧操作列的编辑按钮，弹窗打开调研选项编辑页面，编辑调研选项信息后，点击确定按钮，保存编辑的调研选项信息。

![](images/5b8e4928db516043baca379e54fb8e60e80b5439ab81ad009d2b573552a50b1e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个弹出的对话框，标题为“修改密评条目调研选项”。对话框内有两个主要部分：

1. **调研选项**：这是一个下拉菜单，当前选择的选项是“已支持电子门禁记录数据存储完整性”。

2. **备注**：这是一个文本输入框，提示用户“请输入备注”，目前没有输入任何内容，并且显示了字数限制为0/100。

在对话框的底部有两个按钮：“确定”和“取消”，分别用于确认或取消操作。

背景中可以看到一个灰色的页面，标题为“电子门禁记录数据存储完整性-调研选项”，并且有一个“新建”按钮和一个“调研选项”的标签，标签下有一行文字“已支持电子门禁记录数据存储完整性”，右侧有“编辑”和“删除”两个操作按钮。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.16.6.4. 删除调研选项

点击调研选项列表右侧操作列的删除按钮，提示是否确认删除，点击确认删除，删除调研选项信息。

![](images/f512c17b2b3df8d7c7abbbd62aed07dc4ba741073b8409dfeaa671d38fe9bd3d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个电子门禁记录数据存储完整性的调研选项界面。界面上有一个弹出的提示框，内容是“删除后将无法恢复，是否确认删除？”，提示用户即将进行的操作是不可逆的。提示框中有两个按钮，分别是“取消”和“确定”，供用户选择是否继续执行删除操作。背景中可以看到调研选项列表，其中有一项已支持电子门禁记录数据存储完整性，但具体内容被遮挡，无法完全看清。
```
  
图3-15-25 编辑调研选项界面  
图3-15-26 删除调研选项界面

## 3.16.7. 改进建议管理

系统操作员登录后，点击界面左边菜单栏密评基础数据->密评要求条目管理按钮，进入密评要求条目管理界面，点击密评要求条目右侧操作列改进建议按钮，进入改进建议管理界面。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/9410a7c96193b8e64d0efcea3ba1de9bfdd82080eee7051b33c7da6fc74143a4.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个表格，表格的标题是“要求条目”，表格中有多个列，分别是“要求条目”、“所属测评要求体系”、“第一级”、“第二级”、“第三级”、“第四级”、“备注”和“操作”。在“操作”列中，有一个红色的框，框内有“改进建议”和“删除”两个选项。表格中的内容涉及到身份鉴别、电子门禁记录数据存储完整性、视频监控记录数据存储完整性、通信数据完整性、网络边界访问控制信息的完整性和准确性等多个方面的要求。
```
  
图3-15-27 改进建议界面入口

### 3.16.7.1. 改进建议列表

打开密评要求条目管理菜单，点击密评要求条目操作列的改进建议按钮，进入改进建议页面，展示该密评要求条目对应的改进建议列表。

![](images/716ed32adf88e43dabe109d00afe665fbf39d4f07411601faff1e85422d741f6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个简单的用户界面，可能是一个网页或应用程序的一部分。界面上有一个标题为“改进建议”的表格，表格中有两列：“改进建议”和“备注”，以及一列“操作”。在“改进建议”列中，有一条建议内容为“采购xxxx产品支持身份鉴别”。在“操作”列中，有两个按钮，分别是“编辑”和“删除”，用于对这条建议进行修改或移除。此外，在表格的左上角还有一个“新建”按钮，可能用于添加新的改进建议。整个界面设计简洁明了，方便用户进行操作。
```
  
图 3-15-28 改进建议列表

### 3.16.7.2. 添加改进建议

在改进建议列表页面，点击左上角的新建按钮，弹出新增改进建议表单，添加改进建议信息后，点击确定按钮，保存改进建议信息。

![](images/39727593f16eed5da74fffd5cce90268e06d90df31faff9e4096bde55d24783c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机界面，具体来说是一个弹出的对话框，标题为“新建密评条目改进建议”。这个对话框包含两个主要部分：

1. **改进建议**：这是一个文本输入框，提示用户“请输入改进建议”，并且在右下角显示当前输入的字符数为0/100，意味着用户可以输入最多100个字符的建议。

2. **备注**：这是另一个文本输入框，提示用户“请输入备注”，同样在右下角显示当前输入的字符数为0/100，表示用户可以输入最多100个字符的备注信息。

在对话框的底部有两个按钮：“确定”和“取消”。用户可以点击“确定”来提交他们的改进建议和备注，或者点击“取消”来关闭对话框而不保存任何输入。

背景中可以看到一些模糊的文字，包括“身份鉴别-改进建议”、“采购xxxx产品支持身份鉴别”等，这表明这个对话框可能是在一个与身份鉴别相关的系统或应用中出现的，用户正在对某个产品的身份鉴别功能提出改进建议。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.16.7.3. 编辑改进建议

点击改进建议列表右侧操作列的编辑按钮，弹窗打开改进建议编辑页面，编辑改进建议信息后，点击确定按钮，保存编辑的改进建议信息。

![](images/03be93e4fb49e885acbe023374e007b78dbb54dd7e7f9d0eefcf718669125ed6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机界面，具体来说是一个弹出的对话框，标题为“修改密评条目改进建议”。在这个对话框中，有两个主要部分：

1. **改进建议**：这是一个必填项（由星号*标记），当前输入的内容是“采购xxxx产品支持身份鉴别”，并且已经使用了13个字符，最大限制为100个字符。

2. **备注**：这是一个可选项，目前没有输入任何内容，最大限制也是100个字符。

在对话框的底部，有两个按钮：“确定”和“取消”，分别用于确认或取消操作。

背景页面的部分信息显示，这可能是一个与身份鉴别相关的系统设置或管理界面，其中包含“新建”、“改进建议”等选项。
```
  
图3-15-29 添加改进建议界面  
图3-15-30 编辑改进建议界面

### 3.16.7.4. 删除改进建议

点击改进建议列表右侧操作列的删除按钮，提示是否确认删除，点击确认删除，删除改进建议信息。

![](images/a5bec2361787bc6cc1d8c5b22b293692ec47e2fbb68b7387aefa90a66bb2dc12.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机界面，具体来说是一个关于电子门禁记录数据存储完整性的改进建议页面。页面顶部有一个标题栏，写着“电子门禁记录数据存储完整性-改进建议”。在标题栏下方，有一个表格，表格的列标题分别是“改进建议”、“备注”和“操作”。在“改进建议”列中，有一条建议内容为“采购xxxx产品支持电子门禁记录数据存储完整性”，而在“操作”列中，有“编辑”和“删除”两个选项。

当前页面上出现了一个弹出窗口，这是一个提示框，内容是“删除后将无法恢复，是否确认删除？”，并且有两个按钮，一个是“取消”，另一个是“确定”。这表明用户可能正在尝试删除这条改进建议，系统正在进行最后的确认，以防止误操作导致的数据丢失。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

## 3.16.8. 密码产品介绍

系统操作员登录后，点击界面左边菜单栏密评基础数据->密码产品介绍按钮，进入密码产品介绍管理界面。

### 3.16.8.1. 密码产品介绍列表

点击密码产品介绍列表左上方的新建按钮，弹窗打开新增密码产品介绍表单，添加密码产品介绍信息后，点击确定按钮，保存密码产品介绍信息。

![](images/a984168c887b98d18b1ca1b355deed520a91225f14fc09beba40a122ceca465d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个管理界面，主要用于管理和查看密评产品信息。界面上方有一个搜索栏，用户可以输入密评产品名称和选择密评产品分类进行查询。右侧有两个按钮，分别是“查询”和“重置”。

在搜索栏下方是一个表格，列出了密评产品的详细信息。表格的列标题包括：

1. 密评产品名称：显示产品的名称。
2. 密评产品分类：显示产品的分类。
3. 子组件：显示产品的子组件信息。
4. 配置建议：显示配置建议内容。
5. 软硬件类型：显示软硬件的类型。
6. 配置规则：显示配置规则内容。
7. 功能描述：显示功能描述内容。
8. 备注：显示备注信息。
9. 操作：提供编辑和删除操作。

表格中有两条记录，每条记录包含了上述所有列的信息。例如，第一条记录的密评产品名称为“test”，密评产品分类为“用户端”，子组件为“1111”，配置建议为“test7维修费根本test...”，软硬件类型为“软件”，配置规则为“1111111111111111...”，功能描述为“test7维修费根本test...”，备注为“1111111111111111111111111111...”，并且有编辑和删除的操作选项。

此外，在表格的右下角，显示了当前页面的记录总数、每页显示的记录数以及分页导航。
```
  
图3-15-31 删除改进建议界面  
图3-15-32 密码产品介绍列表

### 3.16.8.2. 添加密码产品介绍

点击密码产品介绍列表左上方的新建按钮，弹窗打开新增 Miami 产品介绍表单，添加密码产品介绍信息后，点击确定按钮，保存密码产品介绍信息。

![](images/223df72b8b19c111d0c1e6eaa95447208c5ca60a7b60e1727dab015aa4799184.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机软件界面，具体来说是一个用于创建或编辑“密评产品”的表单。这个表单包含多个输入字段和选择框，用户需要填写或选择相关信息来完成产品的创建或编辑。

从上到下，表单中的字段包括：

1. **密评产品分类**：这是一个下拉菜单，用户需要从中选择一个分类。
2. **密评产品名称**：这是一个文本输入框，用户需要在这里输入产品的名称。
3. **软硬件类型**：这也是一个下拉菜单，用户需要选择产品是软件还是硬件。
4. **子组件**：这是一个文本输入框，用户可以在这里输入产品的子组件信息。
5. **配置建议**：这是一个多行文本输入框，用户可以在这里输入关于产品配置的建议，最大输入长度为100个字符。
6. **配置规则**：这是一个多行文本输入框，用户可以在这里输入产品的配置规则，最大输入长度同样为100个字符。
7. **功能描述**：这是一个多行文本输入框，用户可以在这里详细描述产品的功能，最大输入长度为1000个字符。
8. **备注**：这是一个多行文本输入框，用户可以在这里添加任何额外的备注信息，最大输入长度为100个字符。

在表单的底部有两个按钮：“确定”和“取消”。点击“确定”按钮将保存输入的信息并关闭表单，而点击“取消”按钮则会放弃所有更改并关闭表单。

在背景中，我们可以看到一个表格，列出了已经存在的“密评产品”的信息，包括产品名称、分类、子组件、功能描述、备注以及操作选项（如编辑和删除）。这表明用户不仅可以创建新的产品，还可以查看和管理已有的产品信息。
```
  
图3-15-33 添加密码产品介绍界面

### 3.16.8.3. 编辑密码产品介绍

点击密码产品介绍列表右侧操作列编辑按钮，弹窗打开密码产品介绍编辑菜单，可以修改密码产品介绍的各种信息，修改密码产品介绍信息后，点击确定按钮，保存编辑的密码产品介绍信息。

![](images/70f46953839b579b6304113089945521abb314810febb9a741b725ac10eb9271.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个计算机界面，具体是一个用于修改密评产品信息的弹出窗口。窗口中包含多个输入字段和下拉菜单，用户可以在此填写或选择相关信息。以下是窗口中的各个部分：

1. **密评产品分类**：这是一个下拉菜单，当前选择的是“用户端”。
2. **密评产品名称**：这是一个文本输入框，当前填写的是“两族林组小”。
3. **软硬件类型**：这是一个下拉菜单，当前选择的是“软件”。
4. **子组件**：这是一个文本输入框，当前填写的是“reprehenderit”。
5. **配置建议**：这是一个多行文本输入框，当前填写的是“sed”，并且显示已使用3/100个字符。
6. **配置规则**：这是一个多行文本输入框，当前填写的是“ut dolor consequat sint nulla”，并且显示已使用29/100个字符。
7. **功能描述**：这是一个多行文本输入框，当前填写的是“amet”，并且显示已使用4/1000个字符。
8. **备注**：这是一个多行文本输入框，当前填写的是“eliusmod”，并且显示已使用7/100个字符。

在窗口的底部有两个按钮：“确定”和“取消”，用户可以通过点击这两个按钮来确认或取消对密评产品的修改。

背景中可以看到一个表格，列出了密评产品名称、密评产品分类、子组件、功能描述、备注和操作等信息。表格中有两条记录，分别是“test”和“两族林组小”，每条记录都有对应的编辑和删除按钮。
```
  
图3-15-34 编辑密码产品介绍界面

### 3.16.8.4. 删除密码产品介绍

点击密码产品介绍列表右侧操作列删除按钮，提示是否确认删除，点击确认删除，删除密码产品介绍信息。

![](images/f4640ea20c9339cdc682ff3d7062f8e7e5d1f5df3e5bc85d32ce8365ac72c2ef.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个计算机软件界面，具体来说是一个管理密评产品信息的系统。界面上方有一个搜索栏，用户可以输入密评产品名称和选择密评产品分类进行查询。右侧有两个按钮，分别是“查询”和“重置”。

在界面的主体部分，有一个表格列出了密评产品的详细信息，包括密评产品名称、密评产品分类、子组件、配置建议、软硬件类型、配置规则、功能描述、备注以及操作选项。表格中有两条记录，第一条记录的密评产品名称为“test”，第二条记录的密评产品名称为“两族林组小”。

在界面的右下角，有一个弹出的提示框，内容是“删除后将无法恢复，是否确认删除？”，提示框中有两个按钮，分别是“取消”和“确定”。这表明用户正在尝试删除某条记录，并且系统在执行删除操作前要求用户进行确认。

此外，界面底部还有一个分页导航，显示当前页面是第1页，总共有2条记录，每页显示20条记录。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

# 3.17. 计量分析

## 3.17.1. 计量信息

### 3.17.1.1. 计量信息列表

系统操作员登录，点击计量分析下的计量信息，列表展示统计到的业务信息以及流量等信息。该信息是一段时间内业务调用的展示，包括调用次数、总流量、流速峰值等信息。统计维度可按照分钟、小时、天、月查询。

![](images/141d9372fb398e2dbc72729dd96b2d0df285d3c58c898f0d1058b35b1d135da6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个网页界面，具体来说是一个计量信息查询页面。页面顶部有一个导航栏，当前选中的标签是“计量信息”。在页面的主要部分，有一个查询表单和一个数据表格。

查询表单包含以下字段：
- 区域：提供了一个下拉菜单供用户选择区域。
- 所属租户：提供了一个下拉菜单供用户选择所属租户。
- 业务类型：提供了一个下拉菜单供用户选择业务类型。
- 统计维度：提供了一个下拉菜单供用户选择统计维度，当前默认为“分钟”。
- 时间：提供了一个下拉菜单供用户选择时间范围，当前默认为“当前”。

在查询表单的右侧有两个按钮：“查询”和“重置”，分别用于提交查询和重置表单。

数据表格位于查询表单下方，列出了以下信息：
- 序号
- 所属租户
- 所属应用
- 业务类型
- 调用次数
- tps峰值
- 总流量
- 流速峰值（Mb/s）
- 时间

然而，表格中目前没有显示任何数据，只有一行提示“暂无数据”。这可能是因为还没有进行查询操作，或者查询结果为空。
```
  
图3-15-35 删除密码产品介绍界面  
图 3-16-1 计量信息列表

# 3.18. 数据备份

系统操作员登录平台可对平台数据库进行自动或手动的备份，备份可整库备份、以服务组为单位备份。提供了数据的自动备份、自动清理、手动备份、备份还原等功能，当前版本仅支持达梦以及 openguass。

基本概念：

1、物理备份

① 物理备份是指对数据库的操作系统物理文件（数据文件、控制文件、日志文件等）的备份；

② 物理备份的备份范围仅支持整库备份；  
③ 物理备份支持全量备份及增量备份。全量备份是对整个数据库进行一次完整的备份，包括数据库中的所有数据和结构。增量备份（这里具体指累积增量备份）是在全量备份的基础上，只备份自上次全量备份或增量备份以来发生更改的数据。  
④ 平台不支持页面操作物理备份的还原。物理备份不支持联机还原，需要先停掉对应达梦数据库实例，再使用达梦RMAN 工具进行一系列还原操作。

2、逻辑备份

① 逻辑备份是指对数据库逻辑组件（表、模式）的备份（导出 sql）。  
② 逻辑备份仅支持全量备份，不支持增量备份。  
③ 逻辑备份的备份单位是服务组数据，支持备份全部服务组数据和指定服务组数据。  
④ 逻辑备份支持在线还原。  
⑤ 逻辑备份还原的单位是数据库实例/模式。

## 3.18.1. 手动备份

### 3.18.1.1. 手动备份记录列表

系统操作员登录，点击数据备份下的手动备份菜单栏，界面显示手动备份记录列表。列表展示手动备份的数据库名称、备份文件名称、备份方式、备份范围以及备份状态等信息。

数据库 Q查询序号 数据库 数据库类型 备份文件名称 备份方式 备份范围 备份类型 是否加密 存储地址 备份服务组 创建时间 操作

### 3.18.1.2. 新增手动备份

点击菜单栏“数据备份”->“手动备份”，点击新增按钮。

物理备份：弹出框选择数据库，选择物理备份。选择全量备份时可以选择是否加密及加密口令；选择增量备份时的加密规则自动与基备份（上一次手动备份）一致，无需填写是否加密和加密口令。

![](images/eb0421be9b5f7d55244661a6c6bc913ea7b5fd2ae923dc44b6a02067dfec7045.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于数据库备份设置的界面截图，具体描述如下：

1. **标题**：新增手动备份

2. **数据库选择**：
   - 有一个下拉菜单，提示“请选择数据库”。

3. **备份方式**：
   - 提供了两种选择：“物理备份”和“逻辑备份”，当前选中的是“物理备份”。

4. **备份范围**：
   - 提供了两种选择：“整库备份”和“增量备份”，当前选中的是“整库备份”。

5. **备份类型**：
   - 提供了两种选择：“全量备份”和“增量备份”，当前选中的是“全量备份”。

6. **是否加密**：
   - 提供了两种选择：“是”和“否”，当前选中的是“是”。

7. **加密口令**：
   - 有一个输入框，提示“请输入加密口令”。

8. **确认加密口令**：
   - 有一个输入框，提示“请确认加密口令”。

9. **备注**：
   - 有一个文本框，提示“请输入备注”，当前为空。

10. **按钮**：
    - 底部有两个按钮：“取消”和“确定”。

这个界面主要用于设置数据库的手动备份参数，包括选择数据库、备份方式、备份范围、备份类型、是否加密以及输入加密口令等。
```


点击确定开始创建。

![](images/5c332831ff3e4fe233619625fd6b22b29a81f7ba3961ec18e6e6ef537f89690b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个数据库备份管理界面。界面上方有一个绿色的提示框，显示“新增成功”，表示刚刚进行的操作已经成功完成。在页面的左上角，有“首页”和“手动备份”的标签，当前选中的是“手动备份”。

在中间部分，有一个表单区域，包含以下选项：
- 数据库：可以选择要备份的数据库。
- 备份方式：可以选择备份的方式。
- 备份类型：可以选择备份的类型。
- 备份文件名称：可以输入备份文件的名称。

在表单区域的右侧，有两个按钮：“查询”和“重置”。

在页面的下半部分，有一个表格，列出了已有的备份记录。表格的列包括：
- 序号：备份记录的编号。
- 数据库：备份的数据库名称。
- 数据库类型：数据库的类型。
- 备份文件名称：备份文件的名称。
- 备份方式：备份的方式。
- 备份范围：备份的范围。
- 备份类型：备份的类型。
- 是否加密：备份文件是否加密。
- 存储地址：备份文件的存储地址。
- 状态：备份的状态。
- 操作：对备份记录的操作选项。

在表格的第一行，有一个备份记录的状态显示为“备份中”，并且被红色框标记出来。其他两行的状态显示为“成功”。
```
  
图 3-17-2 新增手动备份  
图 3-17-3 手动备份状态

逻辑备份：弹出框选择数据库，选择逻辑备份。备份范围可以选择全部服务组或指定服务组，选择指定服务组时需要勾选至少一个服务组（可多选）。

![](images/9df499bf153c4df1be81c0f184f2138403644f4cbcdfc8e36bd72a56bcbd2680.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个数据库备份的设置界面，具体来说是“新增手动备份”的操作窗口。界面上方有“新增手动备份”的标题，表明这是一个用于创建新的数据库备份的操作。

在界面的左侧，我们可以看到一些备份设置选项：

- 数据库：选择了“广电dm数据库”。
- 备份方式：选择了“逻辑备份”。
- 备份范围：选择了“全部服务组”。
- 备份类型：选择了“全量备份”。
- 是否加密：选择了“是”，并且需要输入加密口令和确认加密口令。
- 备注：有一个文本框供用户输入备注信息。

在界面的右侧，有一个表格列出了服务组的信息，包括服务组标识、名称和租户等字段。目前只有一行数据，显示的是“tenant_ccn”服务组的相关信息。

整个界面底部有两个按钮：“取消”和“确定”，用户可以通过点击这两个按钮来取消或确认当前的备份设置操作。
```
  
图 3-17-4 手动逻辑备份

### 3.18.1.3. 手动备份还原

系统操作员登录，点击数据备份-》手动备份，点击列表后的还原按钮，可对逻辑备份的数据进行还原，物理备份的数据不支持还原。

![](images/c9dd68f02fe11154e0ef6ca8d0ed6909164b1c81b57e5a05129eefecad5a7ef6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个数据库备份管理界面。界面上有多个选项和按钮，包括选择数据库、备份方式、备份类型和备份文件名称等。表格中列出了四个备份记录，每个记录包含序号、数据库、数据库类型、备份文件名称、备份方式、备份范围、备份类型、是否加密、存储地址、备份服务组、创建时间和状态等信息。所有记录的状态都显示为“成功”，并且在操作列中有一个红色框标出的“还原”按钮。
```
  
图 3-17-5 手动备份还原

进入二级页面，该页面展示了该备份记录包含了哪些模式的数据，勾选需要还原的数据库模式，点击还原按钮进行还原。

![](images/0570d1a7f2f3da68e404aa833dfe3599b3312ccf2ffcae1eeee138f4c570b77c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个备份记录还原的界面，具体是名为“Logical_Manual_ServiceGroup_20240905_102503”的备份记录。界面上有一个红色框标注的“还原”按钮，表明用户可以点击此按钮进行还原操作。表格列出了多个服务组及其对应的服务类型，包括加解密服务、签名验签服务、密钥管理服务、时间戳服务、协同签名服务、动态令牌服务、数据库加密服务、文件加密服务、电子签章服务、SSLVPN加密通道服务、数字证书认证服务和统一身份认证服务等。每个服务组前都有一个复选框，用户可以选择需要还原的服务组。
```


系统自动跳转到还原记录页面，可在此查看还原任务的执行状态。

![](images/dec237468c26d91c4af2a4ece97403760ef56367b285c855458ad0e822441d10.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个数据库管理界面，具体是关于数据备份和还原的操作。界面上方有一个绿色的提示框，显示“执行成功”，表明最近的操作已经顺利完成。页面标题栏中列出了多个标签，包括“首页”、“手动备份”、“自动备份策略”、“自动备份记录”、“备份记录还原”和“还原记录”，当前选中的标签是“还原记录”。

在页面的主要部分，有一个表格列出了与数据库还原相关的详细信息，包括序号、还原数据库、数据库类型、还原文件名、还原服务组、还原内容、状态、执行人和执行时间等列。表格中有一条记录，显示的是对名为“广电dm数据库”的数据库进行还原操作的信息，该数据库类型为“dm”，还原文件名为“Logical_Manual_Ser...”，还原服务组为“tenant_ccn-专享服务...”，还原内容为“ccsp_pki_706916129...”，当前状态为“还原中”，执行人为“oper”，执行时间为“2024-09-05 10:49”。此外，页面右上角显示了当前用户的权限状态为“永久授权”，用户名为“oper”。
```
  
图3-17-6 手动备份还原列表

## 3.18.2. 自动备份策略

### 3.18.2.1. 自动备份策略列表

系统管理员登录，可查看自动备份策略列表。该列表展示平台配置的自动备份策略。

![](images/d541b15bd8448661a97fddc364aec855d4505999a0c78735ded982f19f83ccc6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个数据库管理系统的界面，具体是关于自动备份策略的设置和查看。界面上方有多个标签页，包括首页、计量信息、租户管理、服务管理、服务类型管理、自动备份策略（当前选中）、升级包管理、自动备份记录、手动备份、还原记录和自动清理策略。

在自动备份策略页面中，有一个表格列出了备份的相关信息，包括序号、数据库名称、数据库类型、备份方式、备份范围、备份类型、备份频率、是否加密、备份服务组、创建时间、备注、状态和操作。表格中有一条记录，显示了一个名为“广电dm数据库”的数据库，其类型为dm，备份方式为物理备份，备份范围为整库备份，备份类型为全量备份，备份频率为3天，未加密，没有指定备份服务组，创建时间为2024年9月5日10:36:58，状态为成功，并且提供了编辑和删除的操作选项。

此外，页面右上角有查询和重置按钮，可以对备份策略进行搜索和重置操作。页面底部显示了分页信息，当前显示的是第1页，共1条记录，每页显示20条。
```
  
图3-17-7 手动备份还原列表  
图 3-17-8 自动备份策略列表

### 3.18.2.2. 新增自动备份策略

系统管理员登录，点击自动备份策略左上角的新增按钮。展开新增备份策略，选择备份数据库，选择备份方式、备份范围、备份类型，填写备份频率和密码，点击确定，新增备份策略，按照备份策略进行一定方式的数据库备份。

在制定了自动备份策略后，为避免磁盘被占满，强烈建议制定合理的自动清理策略。制定自动清理策略的流程在下文详述。

![](images/bb84c0edc0938e53b0e44e3cbffec085ef37b1a930cb29204bd0f6d806d22097.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于新增备份策略的设置界面截图。界面上有多个选项和输入框，用于配置数据库备份的相关参数。具体内容包括：

1. **数据库**：有一个下拉菜单，提示用户选择需要备份的数据库。
2. **备份方式**：提供了两个选项，分别是“物理备份”和“逻辑备份”，当前选中的是“物理备份”。
3. **备份范围**：同样有两个选项，“整库备份”和另一个未被选中的选项，当前选中的是“整库备份”。
4. **备份类型**：提供“全量备份”和“增量备份”两个选项，当前选中的是“全量备份”。
5. **备份频率**：有一个输入框，提示用户输入备份的频率，并且在输入框右侧有一个单位“天”。
6. **是否加密**：提供“是”和“否”两个选项，当前选中的是“是”。
7. **加密口令**：有一个输入框，提示用户输入加密口令。
8. **确认加密口令**：有一个输入框，提示用户再次输入加密口令以确认。
9. **备注**：有一个文本框，供用户输入额外的备注信息，当前显示可以输入最多100个字符。
10. 在界面底部，有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来取消操作或确认设置。

整个界面设计简洁明了，各项设置都有明确的提示和选项，方便用户进行数据库备份策略的配置。
```
  
图 3-17-9 新增备份策略

### 3.18.2.3. 编辑备份策略

系统操作员登录，点击自动备份策略列表数据后的编辑按钮，可修改自动备份策略的备份频率和备注。

![](images/1f32091f7be787fb9494aef4eb6c43ec1086c8f057e75c7f3ba15ead2364beda.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个编辑备份策略的界面，具体内容如下：

1. **数据库**：选择了一个名为“广电dm数据库”的数据库。
2. **备份方式**：选择了“物理备份”选项。
3. **备份范围**：选择了“整库备份”选项。
4. **备份类型**：选择了“全量备份”选项。
5. **备份频率**：设置为每3天进行一次备份。
6. **备注**：有一个输入框，提示用户可以输入备注信息，当前为空。

在界面底部有两个按钮：“取消”和“确定”，用户可以通过点击这两个按钮来取消或确认当前的备份策略设置。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图 3-17-10 编辑备份策略

### 3.18.2.4. 删除备份策略

系统操作员登录，点击自动备份策略列表数据后的删除按钮，可删除该条自动备份策略。

![](images/b798cef9b05a219c67f1adeb0414cf25d454d29468bd9c91f631bd164079e7a8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个数据库备份策略的管理界面。界面上方有多个标签页，包括“自动备份策略”、“巡检策略”、“巡检报告”、“升级包管理”和“平台升级”。当前选中的标签页是“自动备份策略”。

在“自动备份策略”标签页中，有一个表格列出了备份策略的相关信息。表格的列标题包括序号、数据库、数据库类型、备份方式、备份范围、备份类型、备份频率、是否加密、备份服务组、创建时间、备注、状态和操作。

表格中有一条记录，显示了以下信息：
- 序号：1
- 数据库：广电dm数据库
- 数据库类型：dm
- 备份方式：物理备份
- 备份范围：整库备份
- 备份类型：全量备份
- 备份频率：3天
- 是否加密：否
- 备份服务组：（未填写）
- 创建时间：2024-09-05 10:36:58
- 备注：（未填写）
- 状态：成功
- 操作：编辑、删除

此外，在表格上方还有一个“新增”按钮，用于添加新的备份策略。在表格右侧有两个按钮，分别是“查询”和“重置”，用于查询和重置备份策略的筛选条件。
```
  
图 3-17-11 删除备份策略

## 3.18.3. 自动备份记录

### 3.18.3.1. 自动备份记录列表

系统操作员登录，可查看自动备份记录。配置完成自动备份策略后，会按照备份策略的频率自动更新自动备份记录。

密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

![](images/58441e5cad29c5e5a8d6b28d218c6ee17d065a665b09f04ae1e577ef67e1ce9e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个数据库自动备份记录的界面。界面上有多个标签页，包括“首页”、“自动备份策略”、“巡检策略”、“巡检报告”、“升级包管理”、“平台升级”和当前选中的“自动备份记录”。在“自动备份记录”标签页下，有一个表格列出了备份的相关信息。

表格的列标题包括：序号、数据库、数据库类型、备份文件名称、备份方式、备份范围、备份类型、是否加密、存储地址、备份服务组、创建时间和状态、操作。表格中有两条记录，都是关于“广电dm数据库”的备份信息，备份类型为物理备份，备份范围为整库备份，备份类型为全量备份，没有加密，存储地址为10.20.37.178，备份服务组为空，创建时间分别为2024-09-10 00:00:00和2024-09-07 00:00:00，状态都显示为成功，操作列提供了还原选项。

此外，界面上还有数据库、备份方式、备份类型和备份文件名称的选择框，以及查询和重置按钮。
```
  
图3-17-12 自动备份记录列表

### 3.18.3.2. 自动备份记录还原

系统操作员登录，当自动备份策略选择的备份方式是逻辑备份时，可进行备份还原。

![](images/c87d53bbb11d08bf303dfb6079b4c65a5d2310c9fc3ef61e4ed8bd8493d63a99.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个数据库备份管理界面。界面上有多个选项和按钮，用于选择数据库、备份方式、备份类型以及备份文件名称。在表格中列出了两个备份记录，分别是序号1和序号2的广电dm数据库备份信息，包括数据库类型、备份文件名称、备份方式、备份范围、备份类型、是否加密、存储地址、备份服务组、创建时间和状态等详细信息。每个备份记录的状态都显示为成功，并且在操作列中有一个红色边框的“还原”按钮。
```
  
图3-17-13 自动备份记录还原

## 3.18.4. 还原记录

### 3.18.4.1. 还原记录列表

系统操作员登录，点击数据备份-》还原记录。界面显示还原记录列表，展示自动备份和手动备份执行还原操作的数据库以及状态等信息。其中，“还原服务组”指该次还原涉及到哪些服务组；“还原内容”指该次还原具体还原了哪些数据库模式。

![](images/0c07c1bdadf54306e4ca1dc1774076f825e2d10cda93af4bc3345d078b590ad8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个数据库管理界面，具体是关于数据库还原记录的页面。界面上方有多个标签页，包括“首页”、“自动备份策略”、“巡检策略”、“巡检报告”、“升级包管理”、“平台升级”、“自动备份记录”、“手动备份”和当前选中的“还原记录”。在“还原记录”标签页下，有一个表格列出了数据库还原的相关信息。

表格包含以下列：
- 序号：记录的编号。
- 还原数据库：需要还原的数据库名称，这里是“广电dm数据库”。
- 数据库类型：数据库的类型，这里是“dm”。
- 还原文件名：用于还原的文件名称，这里是“Logical_Manual_ServiceGro…”。
- 还原服务组：还原操作的服务组，这里是“tenant_ccn-专享服务组;”。
- 还原内容：具体的还原内容，这里是“ccsp_pkl_706916129079256…”。
- 状态：还原操作的状态，这里是“成功”，并且用绿色背景突出显示。
- 执行人：执行还原操作的人员，这里是“oper”。
- 执行时间：还原操作的执行时间，这里是“2024-09-05 10:49:46”。

此外，在表格上方有一个选择框，提示用户“请选择数据库”，以及两个按钮：“查询”和“重置”，分别用于查询和重置操作。
```
  
图 3-17-14 还原记录列表

## 3.18.5. 自动清理策略

### 3.18.5.1. 自动清理策略列表

系统操作员登录，点击数据备份-》自动清理策略，展示对数据库备份文件的清理策略，包括保留时间、数据库类型、数据库名称等信息。

![](images/5660bbf95c7c1651b79f0cea8349cdb77b29764c6e382cd316d5c445385bed41.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个数据库管理界面，具体是关于自动清理策略的设置。界面上方有多个标签页，包括“首页”、“自动备份策略”、“巡检策略”、“巡检报告”、“升级包管理”、“平台升级”、“自动备份记录”、“手动备份”、“还原记录”和当前选中的“自动清理策略”。在“自动清理策略”标签页下，有一个表格列出了数据库的相关信息，包括序号、数据库名称、数据库类型、保留周期、备注、创建时间和操作选项。

表格中有一条记录，序号为1，数据库名为“广电dm数据库”，数据库类型为“dm”，保留周期为30天，创建时间为2024年9月5日11:03:05。在操作列中，有两个按钮，分别是“编辑”和“删除”，用户可以对这条记录进行相应的操作。

此外，在表格上方有一个“新建”按钮，用户可以点击该按钮来添加新的自动清理策略。在页面的右上角，有两个按钮，一个是“查询”，另一个是“重置”，用户可以通过这两个按钮来进行数据的查询和条件的重置。在页面的右下角，有一个分页导航栏，显示当前页面为第1页，总共有1条记录，每页显示20条记录。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.18.5.2. 新建清理策略

系统操作员登录，点击数据备份-》自动清理策略，点击左上角的新建，界面选择数据库，填写保留周期、备注，点击确定，新增对该数据库的清理策略。

![](images/cc9a594929377c2893e827fdd6383f439c33d9fb720a6437f6147d2541dde867.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户界面，用于配置数据库的保留周期和添加备注。界面上有三个主要部分：

1. **数据库选择**：有一个下拉菜单，提示用户“请选择数据库”，表示用户需要从列表中选择一个数据库。

2. **保留周期设置**：有一个输入框，提示用户“请输入保留周期”，旁边有一个单位选择下拉菜单，默认显示为“天”。这表明用户可以输入一个数值来指定数据的保留时间长度，并可以选择不同的时间单位。

3. **备注信息**：有一个文本区域，提示用户“请输入备注”，并且在右下角显示当前输入字符数为0，最大限制为100个字符。这允许用户添加额外的说明或注释。

在界面底部有两个按钮：“取消”和“确定”。用户可以点击“取消”来放弃当前操作，或者点击“确定”来保存所做设置。
```
  
图3-17-15 自动清理策略列表  
新建清理策略  
图 3-17-16 新增清理策略

### 3.18.5.3. 编辑清理策略

系统操作员登录，点击数据备份-》自动清理策略，点击数据后的编辑按钮，可对清理策略的保留周期、备注进行修改。

![](images/0bc8899066e74fe3f18f58603858a58b5ca1f5d23b415e8bdcda4e7df0608148.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个编辑清理策略的界面。界面上有三个主要部分：

1. **数据库**：这是一个下拉菜单，当前选择的是“广电dm数据库”。

2. **保留周期**：这是一个输入框，用户可以输入一个数字来指定数据的保留天数。当前输入的是“30”天。

3. **备注**：这是一个文本框，用户可以在这里输入一些备注信息。当前文本框中显示的是提示文字“请输入备注”，并且右下角显示当前输入字数为“0/100”，表示最多可以输入100个字符。

在界面的底部有两个按钮：
- **取消**：点击此按钮可以取消当前操作。
- **确定**：点击此按钮可以确认并保存当前设置。

这个界面主要用于设置数据库数据的清理策略，包括选择要清理的数据库、设置数据的保留周期以及添加备注信息。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图 3-17-17 编辑清理策略

### 3.18.5.4. 删除清理策略

系统操作员登录平台，点击数据备份-》自动清理策略，点击列表数据后的删除按钮，可删除该条清理策略。

![](images/8fcb0c02040ffad43e74be585cd925b169bf41c3f7a084bbfad1cc688bef64c9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个数据库管理界面，具体是关于自动清理策略的设置。界面上方有多个标签页，包括首页、自动备份策略、巡检策略、巡检报告、升级包管理、平台升级、自动备份记录、手动备份、还原记录和自动清理策略等。当前选中的标签页是“自动清理策略”。

在自动清理策略页面中，有一个搜索框，提示用户选择数据库，并有两个按钮，分别是查询和重置。下方是一个数据表格，列出了序号、数据库、数据库类型、保留周期、备注、创建时间和操作等信息。

表格中有一条记录，序号为1，数据库名称为广电dm数据库，数据库类型为dm，保留周期为30天，创建时间为2024年9月5日11点03分05秒。在操作列中，有两个选项，分别是编辑和删除，其中删除选项被红色边框突出显示。
```
  
图 3-17-18 删除清理策略

# 3.19. 在线巡检

在线巡检可配置一定的巡检策略，生成巡检报告。报告中可展示对平台使用的数据库、服务组、设备组信息检测结果。

## 3.18.1. 巡检策略

### 3.18.1.1. 巡检策略列表

系统操作员登录，点击在线巡检-》巡检策略，界面展示配置的巡检策略，包括策略名称、报告保留天数、巡检类型、下次执行时间等信息。

![](images/08c5f870677d6b48475bf8e3d93818774cb9b0b7a404e8e8b41fd98abe411bcf.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个巡检任务的详细信息。具体内容如下：

- **序号**：1
- **策略名称**：全面巡检
- **巡检周期**：5分钟/次
- **巡检类型**：数据库，平台，服务，设备
- **报告保留天数**：6天
- **下次执行时间**：2025-05-14 14:00:00
- **备注**：无
- **创建时间**：2024-11-09 15:37:46
- **操作**：巡检报告 导出最新报告 编辑 更多

此外，页面底部显示了分页信息，当前显示的是第1页，共1条记录，每页显示20条记录。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  
图 3-18- 1 巡检策略列表

### 3.18.1.2. 新增巡检策略

系统操作员登录，点击在线巡检-》巡检策略，点击列表左上角的新建按钮，填写策略名称、巡检周期、巡检的类型、生成的巡检报告保留的天数，以及各个巡检类型对应的报告生成策略、记录保留时长，点击确定。

![](images/59d7f639be0a9c9d4c26a5a0194d0977b4b30cc8fe5d8f599f145af1e9a77ac5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个配置界面，主要用于设置某种策略的基本信息和数据库相关的参数。在“基本信息”部分，有四个必填项：策略名称、巡检周期、巡检类型和报告保留天数，以及一个非必填的备注项。在“数据库”标签页下，可以看到数据库类型的选择框，当前选中的是达梦数据库、opengauss数据库和金仓数据库。此外，还有记录生成策略的选择，当前选中的是“全部”，以及记录保留时长的输入框。页面底部有两个按钮，分别是“确定”和“取消”。
```
  
个返回 新建巡检策略  
图 3-18- 2 新建巡检策略

### 3.18.1.3. 编辑巡检策略

系统操作员登录，点击在线巡检-》巡检策略，点击列表数据后的编辑按钮，可对巡检策略的信息进行修改。

![](images/1afc7241c8d7dbf5343fe8a8538f82a537cbecbe804210cf4fa784356b84de7f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个编辑巡检策略的界面，可能来自某个IT运维或数据库管理软件。界面上方是基础信息部分，包括策略名称、巡检周期、巡检类型和报告保留天数等选项。下方是针对不同数据库类型的详细设置，如达梦数据库、openGauss数据库和全合数据库等，每种数据库类型下都有记录生成策略和记录保留时长的设置选项。用户可以通过这个界面配置不同的巡检策略来监控和管理数据库的运行状态。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.18.1.4. 删除巡检策略

系统操作员登录，点击在线巡检-》巡检策略，点击列表数据后的删除按钮，可删除该条巡检策略。

![](images/c44e7ba2d1022b0255765a23cbef0aefdfcc1e54b7337e13fc6a63bff557e396.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个巡检策略的详细信息。具体内容如下：

- 序号：1
- 策略名称：全面巡检
- 巡检周期：5分钟/次
- 巡检类型：数据库，平台，服务，设备
- 报告保留天数：6
- 下次执行时间：2025-05-14 14:10:00
- 备注：无
- 创建时间：2024-11-09 15:37:46

在操作列中，有“巡检报告”、“导出最新报告”和“编辑”等选项。右下角有一个红色框标注的“删除”按钮。
```
  
图 3-18- 4 删除巡检策略

### 3.18.1.5. 手动巡检

系统操作员登录，点击在线巡检-》巡检策略，点击列表数据后的手动巡检，可立即按照巡检策略进行巡检。

![](images/0b3181c31e79c73c3a416ab1a732346a9d5274e8a1002ae14cc7c771dbdb6f1e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个巡检策略的详细信息表格。表格包含了以下列：序号、策略名称、巡检周期、巡检类型、报告保留天数、下次执行时间、备注、创建时间以及操作。具体来说，这个策略的序号是1，策略名称为“全面巡检”，巡检周期为5分钟/次，巡检类型包括数据库、平台、服务和设备，报告保留天数为6天，下次执行时间为2025年5月14日14:10:00，创建时间为2024年11月9日15:37:46。在操作列中，有“巡检报告”、“导出最新报告”、“编辑”和“更多”等选项，其中“更多”选项下有一个红色边框的“手动巡检”按钮和一个“删除”按钮。
```
  
图 3-18- 3 编辑巡检策略  
图 3-18- 5 手动巡检

### 3.18.1.6. 巡检报告

系统操作员登录，点击在线巡检-》巡检策略，点击列表数据后的巡检报告，打开巡检报告列表页面，列表展示巡检记录，包括巡检执行类型、巡检执行的状态、巡检的结果、开始时间、结束时间。

个返回 全面巡检-巡检报告

![](images/e7c2c124fa150e305f31c65c5acf8f2deddf2cf9e7901696e18f69f912740085.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个巡检结果的表格，包含了序号、巡检执行类型、巡检执行状态、巡检结果、开始时间、结束时间和操作等列。
```
  
图 3-18- 6 巡检报告列表  
图 3-18- 7 巡检报告详情

### 3.18.1.7. 巡检报告详情

系统操作员登录，点击在线巡检-》巡检策略，点击列表数据后的巡检报告，点击报告后的详情按钮，可查看具体的巡检报告信息。

基础信息巡检结果 数据库类型 Q查询序号 巡检类型 数据库类型 标识 端口 使用方 指标项名称 指标项值 备注 巡检结果 开始时间 结束时间

### 3.18.1.8. 导出巡检报告

系统操作员登录，点击在线巡检-》巡检策略，点击列表数据后的巡检报告，点击报告后的导出按钮，打开导出报告页面，可导出PDF 或Word 格式的文件。

个返回 全面巡检-巡检报告

![](images/70cbf3dc5a6fda584ad81023e9e896df00a9f3810f17b1db569975762b3484c6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示巡检结果的表格截图，表格中列出了不同巡检任务的执行类型、状态、结果以及开始和结束时间。
```
  
图 3-18- 8 巡检报告列表页面

导出报告

![](images/6be66662d730d175bc84ac9fffe0ad38e7f25b672a58cfbd4f14b5d6d0b98bc3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个文件格式选择界面。界面上有两个选项：“PDF”和“Word”，其中“PDF”选项被选中，表示用户选择了PDF格式。界面底部有两个按钮：“取消”和“确定”，用户可以通过点击“确定”按钮来确认选择的文件格式，或者点击“取消”按钮来放弃当前操作。这个界面通常出现在需要选择文件保存格式的场景中，比如在文档编辑软件中保存文件时。
```
  
图 3-18- 9 导出报告页面

# 3.20. 升级管理

升级管理可对多节点平台的升级包进行在线升级，主要步骤包括升级包上传、升级、回滚操作。

## 3.20.1. 升级包管理

### 3.20.1.1. 升级包列表

系统操作员登录，点击升级管理-》升级包管理，列表展示上传的升级包信息，包括升级包的名称、升级前后的版本变化等信息。

![](images/d4e94288656fb3521175fffaf3373c32284548fee6b7b379b3cbe7b6c1bc526a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个升级包管理界面，界面上有多个输入框和按钮，用于管理和查询升级包信息。表格中列出了两个升级包的详细信息，包括序号、升级包名称、升级前版本、目标版本、CPU架构、升级包类型、升级包状态、描述、创建时间和更新时间。每个升级包的状态都显示为“上传成功”，并且在操作列中有一个红色的“删除”按钮。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

### 3.20.1.2. 升级包上传

系统操作员登录，点击升级管理-》升级包管理，点击列表左上角的上传按钮，上传升级文件，选择升级类型、待升级程序所在服务器的cpu 架构、文件的sh256 值，点击确定。升级包限制大小 2GB。

![](images/0286880ce5f42851c2a8e332c25417db2fa5c41f70e7e52dbe90f29dae724798.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个上传升级包的界面。界面上有以下内容：

1. **标题**：在页面的顶部，有一个标题“上传升级包”。

2. **升级文件**：这是一个必填项，旁边有一个蓝色按钮，上面写着“点击上传”。下方有一行提示文字，说明仅允许上传“tar.gz”格式文件，并且不能超过2GB。

3. **CPU架构**：这是一个必填项，用户需要从下拉菜单中选择一个CPU架构。

4. **升级包类型**：这也是一个必填项，用户需要从下拉菜单中选择一个升级包类型。

5. **文件摘要**：这是一个必填项，用户需要在文本框中输入文件摘要。

6. **升级描述**：这是一个可选项，用户可以在文本框中输入升级描述，最大长度为100个字符。

7. **按钮**：在页面底部有两个按钮，一个是灰色的“取消”按钮，另一个是蓝色的“确定”按钮。

这个界面主要用于用户上传升级包，并填写相关的必要信息。
```
  
图 3-19-1 升级包列表  
图 3-19-2 上传升级包

### 3.20.1.3. 删除升级包

系统操作员登录，点击升级管理-》升级包管理，点击列表数据后的删除按钮，可删除该条记录对应的升级包。

![](images/fe788cc9a7cbf676290d0602f198096da0c52436bc9d4cefdb2199820cb6acfc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个升级包管理界面，界面上有多个输入框和按钮，用于查询和重置操作。表格列出了升级包的详细信息，包括序号、升级包名称、升级前版本、目标版本、CPU架构、升级包类型、升级包状态、描述、创建时间和更新时间。当前显示了两个升级包的信息，它们的升级包状态都是“上传成功”，并且在操作列中有一个红色的“删除”按钮。
```
  
图 3-19-3 删除升级包

## 3.20.2. 平台升级

### 3.20.2.1. 升级包升级

系统操作员登录，点击升级管理-》平台升级，点击左上角的升级，选择cpu 架构、升级包，点击确定，列表新增一条记录，状态为升级中。

平台升级选择的升级包只能是升级包管理列表中升级前版本和当前版本一致的才能进行升级。

![](images/623f8c18222af8e0eaad04ac80f55730f224c33b879999a24e19bc1619707fa8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个平台升级的界面。界面上有三个主要部分：

1. **当前版本**：显示当前的软件版本为“v3.3.2.1”。
2. **CPU架构**：有一个下拉菜单，提示用户选择CPU架构，但目前还没有选择任何选项。
3. **升级包**：另一个下拉菜单，提示用户选择升级包，同样还没有选择任何选项。

在界面的底部有两个按钮：
- **取消**：灰色按钮，用于取消操作。
- **确定**：蓝色按钮，用于确认并执行升级操作。

这些元素共同构成了一个用户界面，用于进行软件的升级操作。
```
  
图 3-19-4 升级包升级  
图 3-19-5 升级包回滚

### 3.20.2.2. 升级包回滚

系统操作员登录，若升级包升级失败，点击升级管理-》平台升级，点击左上角的回滚可进行升级包回滚，若上次升级成功无法进行回滚。

升级前版本 查询序号 升级包名称 cpu架构 升级前版本 目标版本 升级状态 调度节点 创建时间 更新时间

# 3.21. 平台灾备租户信息迁移

灾备租户信息迁移功能目的在于尽可能减少人为迁移工作量，该迁移操作涉及到的迁移范围如下图：

![](images/517f339777100dea943cb54316d78e7a583c44ecb081364162513cad459984f6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个流程图，描述了从申请导入许可到服务站点部署及取消注册的整个过程。流程图分为多个步骤，每个步骤用矩形框表示，并通过箭头连接，表示步骤之间的顺序关系。

1. **申请导入许可**：这是流程的起点。
2. **添加区域**：在申请导入许可后，进行区域的添加。
3. **添加网关**：接着添加网关。
4. **添加区域下组件**：然后添加区域下的组件。
5. **添加数据库**：接下来添加数据库。
6. **注册账户**：之后进行账户的注册。
7. **管理账户**：管理已注册的账户。
8. **添加账户下服务组**：在管理账户后，可以添加账户下的服务组。
   - **添加宿主机**：在服务组中添加宿主机。
   - **上传镜像**：上传镜像文件。
9. **添加账户下设备**：也可以选择添加账户下的设备。
   - **添加设备**：具体添加设备。
10. **添加账号**：在服务组或设备添加完成后，进行账号的添加。
11. **共享开通服务**：账号添加后，可以共享开通的服务。
12. **添加应用**：最后，添加相关的应用。
13. **服务站点部署及取消注册**：这是流程的终点，表示服务站点的部署及可能的取消注册操作。

此外，图中还有一个标注为“迁移范围”的蓝色矩形框，但没有具体的指向，可能表示某些步骤或部分流程涉及迁移操作。
```
  
密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)

系统管理员登录，点击系统管理-》系统配置，可修改平台主备模式。主备模式有非主备模式、主备模式-主平台、主备模式-备平台。其中非主备模式无租户信息导入和备份按钮，主备模式-主平台只有备份按钮，主备模式-备平台只有导入按钮。

## 3.21.1. 租户信息备份

系统管理员登录，点击系统管理-》系统配置，修改平台主备模式，选择平台主备模式为主备模式-主平台。

系统操作员登录，点击租户管理，点击列表左上角的备份，导出租户信息备份文件。

Q查询备份序号 租户标识 租户名称 机构名称 区域 应用数量 创建时间 完整性校验 状态 操作

![](images/b922749f10d4bfc5a34d75f94f521aa5793f758a46b94f2b4d64a3fc3804621c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个管理界面，主要用于管理和查看租户信息。界面上方有三个输入框，分别用于输入“租户标识”、“租户名称”和选择“区域”，右侧有两个按钮，一个是蓝色的“查询”按钮，另一个是白色的“重置”按钮。

在界面的中间部分，有一个表格，标题为“导入”。表格中有多个列，分别是“序号”、“租户标识”、“租户名称”、“机构名称”、“区域”、“业务”、“应用数量”、“创建时间”、“完整性校验”、“状态”和“操作”。

表格中列出了两条租户信息：
1. 第一条租户信息的租户标识、租户名称和机构名称都是“tenant_cm”，区域为“灾备区域”，业务包括“数据加解密、签名验签、密...”，应用数量为0，创建时间为2024-09-04 09:30:38，完整性校验结果为“校验通过”，状态为“运行中”，操作列提供了“详情”、“密码产品配额”、“业务地址”和“更多”的选项。
2. 第二条租户信息的租户标识、租户名称和机构名称都是“tenant_ccn”，区域同样为“灾备区域”，业务也包括“数据加解密、签名验签、密...”，但应用数量为1，创建时间为2024-09-04 09:30:23，完整性校验结果同样为“校验通过”，状态为“运行中”，操作列同样提供了“详情”、“密码产品配额”、“业务地址”和“更多”的选项。

这个界面可能用于系统管理员或相关工作人员查看和管理租户的信息，确保系统的正常运行和数据的安全。
```
  
图 3-20-1 租户信息迁移范围图  
图 3-20-1 租户信息备份

### 3.21.2. 租户信息导入

系统管理员登录，点击系统管理-》系统配置，修改平台主备模式，选择平台主备模式为主备模式-备平台。

系统操作员登录，点击租户管理，点击列表左上角的导入，上传备份文件，点击确定。自动导入备份信息。其余注意事项见《密服平台灾备租户信息迁移功能使用说明》。

图 3-20-2 租户信息导入

# 公司介绍

三未信安科技股份有限公司（股票代码：688489）成立于 2008 年，是国内主要的密码基础设施提供商，专注于密码技术的研究创新和核心产品的开发、销售及服务，为用户提供全面的商用密码产品和整体解决方案。

三未信安具备从密码芯片、密码板卡、密码整机到密码系统的完整密码产品体系和信创密码建设能力，当前已有五十多款产品取得了国家商用密码产品认证证书，是商用密码产品种类最齐全的公司之一。典型产品包括密码芯片、PCI-E 接口密码卡、服务器密码机、金融数据密码机、签名验签服务器、云密码机、数据库加密机、SSL VPN、IPSec VPN、密钥管理系统、密码服务平台、身份认证系统等，全面支持SM1、SM2、SM3、SM4、SM7、SM9、ZUC 等国产密码算法和 RSA、ECC、AES、SHA 等国际密码算法，为关键信息基础设施和重要信息系统提供安全的密码运算和完善的密钥管理机制。三未信安在云计算、大数据、物联网、车联网、人工智能、区块链、隐私保护计算、数字货币等新兴技术领域进行了积极的技术创新，并推出了一系列新型密码解决方案。

经过十几年的市场开拓，三未信安的产品和服务赢得了客户和市场的认可，产品已广泛应用于金融、证券、能源、电信、交通、电子商务等行业，以及海关、公安、税务、水利、医疗保障等政府部门。

三未信安是国家级高新技术企业、国家级专精特新重点“小巨人”企业，公司研发了国内首款安全三级密码板卡和首款安全三级密码机，公司的密码机通过了FIPS 140-2 Level3（美国联邦信息处理标准 3 级）认证，荣获五次国家密码科技进步奖。公司是全国信息安全标准化技术委员会和密码行业标准化技术委员会成员单位，牵头和参与制定了二十余项密码领域国家标准或行业标准。

三未信安坚持“做客户信赖的公司，做有核心技术的公司，做员工热爱的公司”的发展理念，恪守“让生活更美好，做对社会真正有价值的事情”的价值追求，以“用密码技术守护数字世界”为使命，凝聚人才、锐意进取，立志为我国的网络信息安全事业贡献自己的力量！