# 一、集团

注：在集团总部密码服务平台

### 1步骤一：添加新的省级平台

1、平台操作员账号登录，点击右上角的【集省一体】按钮

![](images/dd92f6ea03f2e73c35ba018657a24ad91764b476757195552fb74ffa5bf1e05e.jpg)
```text
这是一张密码服务管理平台的界面截图，显示了平台的运行状态和各种统计信息。
```
  
2、点击【省级平台管理-省级平台列表】，新建一个新的省级平台

![](images/fdb961307f2e83d36d4da0f8efc3faadf5ffaed12672c2384408a177d1650a06.jpg)
```text
这张图片展示了一个名为“集省一体平台”的网页界面，具体是在进行省级平台管理的操作。在左侧的导航栏中，可以看到“省级平台管理”选项被选中，并且有一个红色框标注了“新建”按钮，提示用户可以点击此处创建新的省级平台。

右侧是一个弹出窗口，标题为“新建省级平台”，在这个窗口中，用户需要填写一些信息来完成新平台的创建。这些信息包括：

- 平台编号：已经预设为0001。
- 平台名称：输入了“山东移动”。
- 省份：选择了“山东移动”。
- 备注：这是一个可选字段，目前为空白状态。

在弹出窗口的底部有两个按钮，“取消”和“确定”，用户可以选择取消操作或者确认提交所填写的信息。

此外，在背景页面中还可以看到一个表格，列出了已有的省级平台列表，包括序号、平台标识等信息，以及创建时间和操作选项。
```


3、在新建的升级平台的操作区域点击【凭证管理】，创建新的 AKSK，会下载一个AKSK 文件，需要将此文件提供给省级平台相关负责人

![](images/8dc4eebe784c13893144df9f595c4968b5ecfc9a4b00798aa5bc9f1570934063.jpg)![](images/45582ef4ac8deddec8f04b900a00900f335209a7c4241c67a98a5e1104854075.jpg)
```text
这张图片展示了两个不同的操作界面，都与“集省一体平台”的省级平台管理相关。

在第一张图片中，我们看到的是一个名为“新增凭证”的弹窗界面。这个界面是在用户点击了“新增”按钮后出现的，目的是为了创建新的访问密钥ID。弹窗中包含了平台名称（山东移动）、当前剩余可创建的访问密钥数量（10个）以及一个用于输入描述的文本框。用户需要在这里填写相关信息，并通过点击“确定”按钮来完成凭证的新增操作。

第二张图片则展示了一个文件保存对话框，这是在创建新的AKSK（Access Key和Secret Key）凭证后自动触发的。AKSK凭证文件将被下载到本地计算机上，具体位置为D盘下的downloads文件夹。用户需要选择合适的保存路径，并确认文件名（例如：2310001_WHGU7Y69ZXERRYB_credential.txt）。保存完成后，用户应将此文件提供给省级相关负责人以供后续使用。

这两张图片共同说明了在“集省一体平台”中如何管理和创建省级平台的访问凭证，包括新增凭证的过程以及AKSK凭证文件的保存和分发步骤。
```


AKSK 凭证文件，如下所示：

![](images/16bd9abfe90c9de78223c53c3123cf38a3b29b7aed845fcec8ff2aaa0c390c57.jpg)
```text
这张图片显示了一个文本编辑器窗口，其中包含了一些与身份验证相关的配置信息。具体来说，有三个关键字段：

1. **PlatformCode**: 这个字段的值是5310001，被标记为“省级平台标识”，意味着它可能用于标识某个特定的省级平台或系统。

2. **AccessKeyId**: 这个字段的值被部分遮挡了，但可以看出它以“WH”开头，被标记为“AK”，这通常代表访问密钥ID（Access Key ID），用于身份验证和授权过程中的身份识别。

3. **SecretAccessKey**: 这个字段的值也被部分遮挡了，但可以看出它以“Q+Geu”开头，被标记为“SK”，这通常代表秘密访问密钥（Secret Access Key），与AK一起使用，用于在身份验证过程中提供安全的认证信息。

此外，图片中还有一行提示文字，提醒用户“SecretAccessKey仅允许下载一次，请妥善保管。”这强调了SK的重要性以及需要对其进行保护的必要性，因为它一旦泄露可能会导致安全风险。
```


### 2步骤二：配置集省一体服务

执行步骤一，选择【集团移动】，并得到一个AKSK 文件。

![](images/acedee0cbafa2f33b57b166e7713c35d5df8a3ea219a5d49f5b1a212839be1f4.jpg)
```text
这张图片显示的是一个名为“集省一体平台”的软件界面，具体是在“省级平台管理”模块下的“省级平台列表”页面。当前操作是新建一个省级平台，弹出了一个对话框，要求输入平台编号、平台名称和省份等信息。

在对话框中，平台编号已经填写为“0001”，平台名称和省份都选择了“集团移动”。这两个字段被红色边框标记，可能表示它们是必填项或者当前有错误需要修正。

背景中可以看到省级平台列表的部分内容，列出了序号、平台标识、平台名称、创建时间和操作选项等信息。当前列表中有三条记录，分别对应不同的平台标识和创建时间。

此外，界面左侧有一个导航栏，列出了首页、省级平台管理、资产数据、业务数据、事件数据和基础数据等选项，方便用户在不同功能模块之间切换。右上角显示了当前用户的权限状态为“永久授权”，用户名为“oper”。
```
  
0000002 7H5RXIZ7XN5QWSV credential.txt -记事本文件(F）编银(E)格式(O）查看(V)帮助(H)PlatformCode:0000002AccessKeyId:7H…JWSVSecretAccessKey:ma5tlpUS0sf7s..... /CvSecretAccessKev仅允许下载一次，请妥善保管。

此时需要切换到平台管理员登录：

### 3上报配置

点击【集省一体服务-上报配置】，依次配置

1、集团平台的配置路径：IP 为密码服务平台的第一个网关的 IP，端口为8866；  
2、集团平台的访问凭证：根据刚刚得到的 AKSK 凭证文件，填写相关信息；  
3、当前平台的操作员配置：当前密码服务平台的平台操作员账号信息；  
配置好上面三个信息后，根据需要可以依次点击下面的启用按钮，开启需要同步到【集省一体服务】的相关信息；

![](images/ce122eafebe8c6b10d56ce77702bdaa2ef8b214ddc11dc9ace46e4d43d2b459f.jpg)
```text
这张图片展示了一个名为“密码服务管理平台”的网页界面。页面左侧有一个导航栏，列出了多个功能模块，包括首页、集省一体服务、产品列表、密钥列表、证书列表、文档列表、密码应用列表、应用测评列表、安全事件列表、用户管理和系统管理等。

在页面的主体部分，有三个红色框标注了不同的信息区域：

1. **集团平台的调用路径**：显示了IP地址为***********和端口为8866的信息。
2. **集团平台访问凭证**：提供了平台标识号（0000001）和AK（KGCBLS5D2JIWV3E）。
3. **当前平台操作员配置**：显示了操作员账号名为oper及其对应的口令。

下方是一个表格，标题为“上报内容列表”，列出了序号、上报内容、周期、是否启用、最后上报时间和操作等信息。表格中列出了六项上报内容，分别是密码应用数据上报、密钥信息上报、密码产品信息上报、密码应用漏洞/安全事件上报、证书信息上报和密码文档信息上报，每项内容的周期都是每小时上报一次，并且都处于启用状态，最后上报时间均为2024年12月2日的不同时间点。

页面右上角显示了当前用户的权限信息，包括永久授权、大屏、管理员和admin等标签。
```


#### 查询和编辑

通过操作【上报配置】后，集省一体服务会从密码服务平台拉取相关数据，可以展示列表并可以编辑一些特殊字段。

#### 产品列表

![](images/f13e74ba6b199cd043c68485bc446927fedb9c012ad1486a67657c1d9f002722.jpg)
```text
这张图片显示的是一个名为“密码服务管理平台”的网页界面。在左侧有一个垂直的导航菜单，列出了多个选项，包括首页、集省一体服务、上报配置、产品列表、密钥列表、证书列表、文档列表、密码应用列表、应用测评列表、安全事件列表、用户管理和系统管理等。

在右侧的主要内容区域，顶部有一个标签栏，当前选中的标签是“产品列表”。在这个标签下，有一个表格，列出了产品的详细信息，包括序号、产品名称、产品类型、省份、位置、证书编号、产品简介和创建时间等字段。表格中列出了四个产品，分别是kms_01、sms_01、vsm_10_20_37_175_19443和379，它们的产品类型分别是密码软件、密码软件、虚拟密码机和云服务器密码机，都位于集团移动，创建时间都是2024-11-29 22:27:01。

在表格的每一行的最右侧，有一个操作列，列出了针对每个产品的操作选项，例如“密码业务 编辑”。

在页面的右上角，显示了当前用户的权限信息，包括永久授权、大屏、管理员和admin等。
```


![](images/bef6850543994715443a8e8535becff7d50a8fe7f47f3dc8348db56e719648f6.jpg)
```text
识别失败
```
  
密钥列表

#### 证书列表

![](images/5a45880127f3421dfe29ab3404be3423f7b5e329e5268d7e19a41d7587b9ff49.jpg)
```text
这张图片显示的是一个名为“密码服务管理平台”的用户界面。界面左侧是一个垂直的导航菜单，列出了多个管理选项，包括“集省一体服务”、“产品列表”、“密钥列表”、“证书列表”、“文档列表”、“密码应用列表”、“应用测评列表”、“安全事件列表”、“用户管理”和“系统管理”。当前选中的选项是“证书列表”。

在主内容区域，有一个表格标题为“证书列表”，表格中有以下列：序号、证书标签、证书类型、省份、证书算法、状态和创建时间。表格中显示了一条记录，其详细信息如下：
- 序号：1
- 证书标签：SW-CERT
- 证书类型：服务器证书
- 省份：集团移动
- 证书算法：SM3WithSM2
- 状态：使用中
- 创建时间：2024-11-29 23:00:00

在表格上方，有一个搜索框，提示用户可以输入证书标签进行查询，并有两个按钮，分别是“查询”和“重置”。右上角显示了当前用户的权限信息，表明该用户具有永久授权，并且用户名为“admin”。
```


#### 文档列表

![](images/8b53c46801472bb1f31997067119976dd82de7a9651d061a63de75786477fe74.jpg)
```text
这张图片显示的是一个名为“密码服务管理平台”的网页界面。在左侧的导航栏中，有多个选项卡，包括首页、集省一体服务、上报配置、产品列表、密钥列表、证书列表、文档列表、密码应用列表、应用测评列表和安全事件列表等。当前选中的选项是“文档列表”。

在右侧的主要内容区域，有一个表格列出了文档的相关信息。表格的列标题包括序号、文档名称、文档类型、省份、所属对象、创建时间和操作。目前表格中有两条记录，分别是名为“aaaaaaaaaaaa.docx”和“sss.xlsx”的文档，它们的类型都是“密码资料”，省份为“广东移动”，创建时间分别为2024年11月29日23:18:01和2024年11月29日23:18:00。

在表格的上方，有一个搜索框，用户可以输入文档名称进行搜索。在表格的右下角，有一个分页器，显示当前页面是第1页，总共有2条记录，每页显示20条记录。

此外，在页面的右上角，有一些功能按钮，如“永久授权”、“大屏”和“管理员admin”的设置选项。
```


#### 密码应用列表

![](images/be59b720d2b12aafd9781256e2b4bec0fcd6f97248bec8cd0f078808a3194c73.jpg)
```text
这张图片显示的是一个名为“密码服务管理平台”的网页界面。页面左侧有一个导航栏，列出了多个管理选项，包括“集省一体服务”、“上报配置”、“产品列表”、“密钥列表”、“证书列表”、“文档列表”、“密码应用列表”、“应用测评列表”和“安全事件列表”。当前选中的选项是“密码应用列表”。

在页面的右侧，有一个表格，标题为“密码应用列表”，表格中有以下列：序号、应用名、业务系统类型、省份、应用场景、备注、创建时间和操作。表格中有一条记录，序号为1，应用名为“0010010001”，业务系统类型为“其他”，省份为“集团移动”，创建时间为“2024-11-29 22:27:00”，操作列有两个链接：“密码业务”和“编辑”。

页面顶部有一个标签栏，列出了多个标签页，包括“首页”、“上报配置”、“产品列表”、“密钥列表”、“证书列表”、“文档列表”和“密码应用列表”。当前选中的标签页是“密码应用列表”。

页面右上角显示了用户的登录信息，包括用户名“admin”和一些其他选项。
```


![](images/323c53322cb64c08f0a1fe7c887004c551e59516cb0bdd6fd1d755922f81c74e.jpg)
```text
这是一张显示密码服务管理平台界面的截图，具体是应用测评列表页面。
```


# 安全事件列表

![](images/a4bd21671cf060dc12534962f46d0eed95e711789fcf3178e8d9263c906628d8.jpg)
```text
这是一张显示密码服务管理平台界面的截图。
```


## 二、省级

注：在省级密码服务平台，平台管理员登录。

### 4上报配置

点击【集省一体服务-上报配置】，依次配置

1、集团平台的配置路径：需要询问集团平台的相关负责人，支持 IPV6。IP 为集团平台的第一个网关的IP，端口为8866；如果现场是通过VPN 过去的，需要填写VPN 映射的 IP 和端口；2、集团平台的访问凭证：需要询问集团平台的相关负责人，拿到 AKSK 凭证文件；

3、当前平台的操作员配置：当前平台的操作员账号信息；

配置好上面三个信息后，根据需要可以依次点击下面的启用按钮，开启需要同步到集团的相关信息（如果密码服务平台是 V3.3.1 版本，密码文档和密码应用测评开启不了，因此版本不存在密评功能和密码知识库）；

![](images/7904b32ec90680cd4331f6650a6f4272da82913fb81cdf1edff886918774b84a.jpg)
```text
这张图片展示了一个名为“密码服务管理平台”的网页界面。页面左侧有一个导航栏，列出了多个功能模块，包括首页、集团一体服务、产品列表、密钥列表、证书列表、文档列表、密码应用列表、应用测评列表、安全事件列表、用户管理和系统管理等。当前选中的模块是“上报配置”。

在页面的主体部分，有三个红色边框的区域，分别标注了不同的信息：

1. 集团平台的调用路径：显示了IP地址为***********和端口为8866。
2. 集团平台访问凭证：提供了平台标识（2001111）、AK（46DD3YGU4WQN5JM）等信息。
3. 当前平台操作员配置：列出了操作员账号名（oper）和操作员口令。

下方是一个表格，标题为“上报内容列表”，列出了序号、上报内容、周期、是否启用、最后上报时间和操作等信息。表格中列出了七项上报内容，每项内容的周期都是“每小时上报一次”，并且都处于“停用”状态，最后上报时间均为2024年12月2日10:18:03至10:18:01之间。每行记录后面都有“编辑”和“启用”两个操作按钮。

页面右上角显示了用户的权限信息，当前用户为管理员admin，拥有永久授权，并且有一个大屏图标。
```


### 5查询和编辑

通过操作【上报配置】后，集省一体服务会从密码服务平台拉取相关数据，可以展示列表并可以编辑一些特殊字段。

### 6产品列表

![](images/94d663f30231e82060e7299781d0547ad8e347cfad92ac2c109bfcf1d1cfb3f8.jpg)
```text
识别失败
```


#### 密钥列表

<html><body><table><tr><td>S 密码服务管理平台</td><td colspan="7"></td></tr><tr><td>命颜</td><td colspan="2">颜 上报配置X 产品列表X 密钥列表 证书列表X 密码应用列表X 文档列表×</td></tr><tr><td>集省一体服务</td><td colspan="2">密钥标识 请输入密奶标识</td></tr><tr><td>上报配置</td><td colspan="2"></td></tr><tr><td>产品列表 密钥列表</td><td colspan="2">密钥真法 密钥长度 192</td></tr><tr><td>序号 密钥标识 证书列表 1 19</td><td>密钥类型 册 广东移动</td></tr><tr><td>文档列表 密码应用列表 虛用測評列表 安全事件列表</td><td>对称密钥</td></tr><tr><td>每系统管理</td><td>3DES 2 20 对称密钥 广东移动 3DES 3</td></tr><tr><td></td><td>192 17 对称密钥 广东移动 3DES 192</td></tr><tr><td></td><td></td></tr><tr><td></td><td>18 对称密钥 广东移动 3DES 192 6 对称密钥 广东移动 3DES 192</td></tr><tr><td>5 6 7</td><td>7 对称密钥 广东移动 3DES</td></tr><tr><td></td><td>对称密钥 广东移动 3DES</td></tr><tr><td></td><td>8 9 对称密钥 广东移动</td></tr><tr><td></td><td>10 对称密钥 广东移动</td></tr><tr><td>9 10</td><td>11 对称密钥 广东移动</td></tr><tr><td></td><td>3DES 对称密钥 广东移动 3DES</td></tr><tr><td>12</td><td>对称密钥 广东移动 3DES</td></tr><tr><td>12 13</td><td>192 对称密钥</td></tr><tr><td>13 14 14 15 15 16</td><td>广东移动 3DES 192 2024-11-29 18:30:03</td></tr></table></body></html>

#### 证书列表

![](images/76423e58bbeb0eb17f544105b6be77f3fb568f8e016b20bb329d2e1809573074.jpg)
```text
这张图片显示的是一个名为“密码服务管理平台”的用户界面，当前页面是“证书列表”。在左侧的导航栏中，可以看到多个选项卡，包括“首页”、“集省一体服务”、“用户管理”和“系统管理”。在“集省一体服务”下，有“上报配置”、“产品列表”、“密钥列表”、“证书列表”、“文档列表”、“密码应用列表”、“应用测评列表”和“安全事件列表”等子选项。

在主内容区域，有一个表格列出了证书的相关信息。表格的列标题包括“序号”、“证书标签”、“证书类型”、“省份”、“证书算法”、“状态”和“创建时间”。在这个例子中，只有一条记录，其证书标签为“SW-CERT”，证书类型为“服务器证书”，省份为“广东移动”，证书算法为“SM3WithSM2”，状态为“使用中”，创建时间为“2024-11-29 18:30:02”。

此外，在表格上方有一个搜索框，用户可以输入证书标签进行查询，并有两个按钮，分别是“查询”和“重置”。在右上角，显示了当前用户的权限信息，表明该用户具有“永久授权”，并且用户名为“admin”。
```


#### 文档列表

<html><body><table><tr><td>密码服务管理平台</td><td colspan="6">永久授权回大屏</td></tr><tr><td></td><td colspan="6">管理员|admin ￥ 首页 上报配置X 产品列表× 密钥列表× 证书列表X 密码应用列表X 文档列表</td></tr><tr><td rowspan="5">由集省一体服务 上报配置 产品列表 密钥列表</td><td colspan="5">文档名称 请输入文档名称</td></tr><tr><td colspan="5"></td></tr><tr><td colspan="5">序号 文档名称 文档类型</td></tr><tr><td colspan="2">1 aaaaa.docx 密码资料</td><td>册 广东移动</td><td>所属对象</td><td>创建时同 2024-11-29 8:30:05</td><td>操作 编橱下载</td></tr><tr><td colspan="2">2 S5s×xlsx</td><td>密码资料</td><td>广东移动</td><td></td><td>2024-11-29 8:30:02 编扭下载</td></tr><tr><td colspan="2">文档列表 密码应用列表 应用测评列表</td><td colspan="4"></td></tr><tr><td colspan="2" rowspan="2">安全事件列表 品用旷管理</td><td colspan="3"></td><td rowspan="2"></td></tr><tr><td colspan="2"></td></tr><tr><td colspan="2"></td><td></td><td></td><td></td></tr><tr><td colspan="2">系统管理</td><td colspan="4"></td></tr></table></body></html>

#### 密码应用列表

<html><body><table><tr><td>密码服务管理平台</td><td colspan="7">永久授权国大屏 管理员 |admin </td></tr><tr><td>价颜</td><td colspan="2">页 上报配置× 产品列表X 密钥列表×</td><td colspan="5">证书列表X 密码应用列表X 文档列表×</td></tr><tr><td>画集省一休国务 上报配置</td><td colspan="7">应用名称 请输入应用名称 询 重</td></tr><tr><td rowspan="5">产列表 密钥列表</td><td colspan="7"></td></tr><tr><td colspan="7"></td></tr><tr><td>序号 应用名 1 2</td><td>其他</td><td>业务系统类型 省份 广东移动</td><td>应用场景</td><td>备注</td><td>创建时间 2024-11-29 18:30:05</td><td>操作</td></tr><tr><td>2</td><td>2</td><td>其他</td><td>广东移动</td><td></td><td></td><td>密码量务 编辑</td></tr><tr><td rowspan="2">文档列表 密码应用列表</td><td>3 2</td><td></td><td>广东移动</td><td></td><td>2024-11-29 18:30:05</td><td></td><td>密码业务 编辑</td></tr><tr><td></td><td></td><td>其他</td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密码业务 编粗</td></tr><tr><td rowspan="10">应用測评列表 安全事件列表 迎系统管理</td><td>4 2</td><td></td><td>其他</td><td>广东移动</td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密码业务编相</td></tr><tr><td>5 2</td><td></td><td>其他</td><td>广东移动</td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密码业务 编轴</td></tr><tr><td>6 2</td><td></td><td>其他</td><td>广东移动</td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密码业务编辑</td></tr><tr><td>7 2</td><td></td><td>其他</td><td>广东移动</td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密码量务 编辅</td></tr><tr><td>8 2</td><td></td><td>其他</td><td>广东移动</td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密同业务 编粗</td></tr><tr><td>9 2</td><td></td><td>其他</td><td>广东移动</td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密码业务编辑</td></tr><tr><td>10 2</td><td></td><td>其他</td><td>广东移动</td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密码业务 编钮</td></tr><tr><td>" 2</td><td></td><td>其他</td><td>广东移动</td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密捐业务编辑</td></tr><tr><td>12 2</td><td></td><td>其他</td><td>广东移动 广东移动</td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密码业务 编辅</td></tr><tr><td>13 2</td><td></td><td>其他</td><td></td><td></td><td></td><td>2024-11-29 18:30:05</td><td>密码业务 编租</td></tr><tr><td></td><td>14 2 15 1</td><td></td><td>其他 甘h</td><td>广东移动 广车班动</td><td></td><td>3m4 11 20 18.3n.05</td><td>2024-11-29 18:30:05</td><td>密码业务 编期</td></tr></table></body></html>

2

![](images/e76f2450ef8db6dc208dfa5f2f4926bd50780aa31c93967065dd7fd03c51d778.jpg)
```text
这张图片显示的是一个名为“密码服务管理平台”的网页界面，当前页面是“应用测评列表”。在左侧的导航栏中，可以看到多个选项卡，包括首页、集省一体服务、上报配置、产品列表、密钥列表、证书列表、文档列表、密码应用列表、应用测评列表和安全事件列表。此外，还有用户管理和系统管理两个可展开的菜单。

在主内容区域，有一个表格列出了应用测评的相关信息，包括序号、应用名、省份、密改阶段、密码阶段进度、密改支持厂商、密评机构、安全等级、密评得分和创建时间。表格中有两条记录，都与广东移动有关，应用名为'a'，密改阶段为项目准备，密改支持厂商为三未信安科技股份有限公司，安全等级为1，创建时间为2024-11-29 18:30:03。其中一条记录的密码阶段进度为0，另一条为9，密评得分为50，而第一条记录的密评机构为空。

在表格上方，有一个搜索框，可以输入应用名称进行查询，以及两个按钮，分别是查询和重置。在右上角，显示了当前用户的权限状态为永久授权，并且用户名为admin。
```


#### 安全事件列表

![](images/b2b248eeda4a456f63fec3c891a0ede65a87528f75ea4c210f37b82e561896c8.jpg)
```text
这是一张显示密码服务管理平台界面的截图。
```


## 三、快速上报数据方法

注：现场环境若需要快速上报一次数据进行展示，可依据此步骤进行操作；若不需要快速上报，则无需执行此步骤

### 7打开每分钟上报一次数据的权限

使用 ccsp 用户登录省级密码服务平台节点一后台，执行下列命令连接省级集省一体服务的数据库

<html><body><table><tr><td>export LD_LIBRARY_PATH=/opt/ccsp/datatools/gauss/lib:$ {LD_LIBRARY_PATH}</td></tr><tr><td>export PATH=/opt/ccsp/datatools/gauss/bin:$ {PATH}</td></tr><tr><td>/opt/ccsp/datatools/gauss/bin/gsql -h $dbHost -d $dbName -U $dbUser -p $dbPort -W $dbPasswd</td></tr><tr><td>（将命令中的$dbHost 替换为现场opengauss 数据库的主节点 IP、$dbName 替换为数据库实 例名、$dbUser 替换为数据库用户名、$dbPort 替换为数据库端口、$dbPasswd 替换为数据库 连接密码） 数据库成功连接后，执行下列sql，插入每分钟上报一次数据的选项；</td></tr><tr><td>set search_path to ccsp_data;</td></tr><tr><td>INSERT INTO dic_frequency (frequency_id, frequency_name, cron_expression) VALUES(1,' 每分钟上报一次'0****?”;</td></tr><tr><td>执行sql进行查询，结果如下图则代表插入成功；</td></tr></table></body></html>

![](images/e4d0b8f7b11cd2faf40f49a63c73009fe49c8caa6a9c4fbab7ce32d4c597ebd9.jpg)
```text
这是一张显示数据库查询结果的截图，查询语句为 `select * from dic_frequency;`。查询结果显示了四个不同的频率设置，每个设置包括一个频率ID、频率名称和对应的Cron表达式。具体如下：

1. 频率ID为2，频率名称为“每天上报一次”，Cron表达式为`0 0 0 * * ?`。
2. 频率ID为3，频率名称为“每周上报一次”，Cron表达式为`0 0 0 ? * 1`。
3. 频率ID为4，频率名称为“每月上报一次”，Cron表达式为`0 0 0 1 * ?`。
4. 频率ID为1，频率名称为“每分钟上报一次”，Cron表达式为`0 * * * * ?`。

这些Cron表达式定义了任务执行的时间规则，例如每天的零点执行、每周一的零点执行、每月的第一天零点执行以及每分钟执行一次。
```


### 8上报数据

使用管理员登录省级密码服务平台 web 页面，点击上报配置下各上报数据的编辑按钮，将上报周期调整为每分钟上报一次并启用上报

![](images/ffdbe0d5a56b30c62a8e4e22d7c988b32abd86a763f3627cea8f79058646b5d2.jpg)
```text
这张图片显示的是一个密码服务管理平台的界面，具体是在“上报配置”模块。界面上有一个弹出窗口，标题为“编辑上报频率”，用户正在选择上报周期。当前选中的上报周期是“每周上报一次”，但下拉菜单中还有其他选项，如“每天上报一次”、“每月上报一次”和“每分钟上报一次”。

在背景中，可以看到“上报内容列表”，列出了不同的上报内容及其对应的上报周期、是否启用状态以及最后上报时间。例如，第一项是“密码应用数据上报”，其上报周期为“每周上报一次”，目前处于“启用”状态。

左侧的导航栏显示了多个模块，包括首页、监控告警、集省一体服务、上报配置、产品列表、密钥列表、证书列表、文档列表、密码应用列表、应用测评列表、安全事件列表、用户管理和系统管理等。

右上角显示了当前用户的权限信息，包括“永久授权”、“大屏”、“管理员”和用户名“admin”。
```


### 9关闭每分钟上报一次数据的权限

待数据成功上报至集团平台之后，考虑到后续网关的压力，不可继续保持每分钟上报一次的频率，需要将此权限关闭；

首先使用管理员登录省级密码服务平台web页面，点击上报配置下各上报数据的编辑按钮，将所有上报选项的上报频率调整为每月上报一次

![](images/23e72c2f59639a20ff5b6b37dc3cd0d16ffe7671c2db088156d53a9a5f9a3a9d.jpg)
```text
这张图片显示的是一个密码服务管理平台的界面，具体是在“上报配置”模块中。界面上有一个弹出窗口，标题为“编辑上报频率”，用户正在选择上报周期。当前选中的上报周期是“每月上报一次”，其他选项包括“每天上报一次”、“每周上报一次”和“每分钟上报一次”。

在弹出窗口的背景中，可以看到“上报内容列表”，列出了不同的上报内容及其对应的上报周期、是否启用状态以及最后上报时间。例如，第一项是“密码应用数据上报”，其上报周期被设置为“每月上报一次”，并且处于“停用”状态。

左侧的导航栏列出了多个功能模块，如“首页”、“监控告警”、“集约一体服务”、“上报配置”、“产品列表”、“密钥列表”、“证书列表”、“文档列表”、“密码应用列表”、“应用测评列表”、“安全事件列表”、“用户管理”和“系统管理”。当前选中的模块是“上报配置”。

右上角显示了用户的登录信息，包括用户名“admin”和一些操作按钮，如“永久授权”和“大屏”。
```


确保所有上报选项均不再使用每分钟上报一次的频率后，使用 ccsp 用户登录省级密码服务平台节点一后台，执行下列命令连接省级集省一体服务的数据库

<html><body><table><tr><td>export LD_LIBRARY_PATH=/opt/ccsp/datatools/gauss/lib:$ {LD_LIBRARY_PATH}</td></tr><tr><td>export PATH=/opt/ccsp/datatools/gauss/bin:$ {PATH}</td></tr><tr><td>/opt/ccsp/datatools/gauss/bin/gsql -h $dbHost -d $dbName -U $dbUser -p $dbPort -W $dbPasswd</td></tr></table></body></html>

（将命令中的\$dbHost 替换为现场 opengauss 数据库的主节点 IP、\$dbName 替换为数据库实例名、\$dbUser 替换为数据库用户名、\$dbPort 替换为数据库端口、\$dbPasswd 替换为数据库连接密码）

数据库成功连接后，执行下列 sql，删除每分钟上报一次数据的选项；

<html><body><table><tr><td>set search_path to ccsp_data;</td></tr><tr><td>delete from dic_frequency where frequency_id=1;</td></tr><tr><td>执行sql进行查询，结果如下图则代表删除成功；</td></tr><tr><td>select * from dic_frequency;</td></tr></table></body></html>

<html><body><table><tr><td colspan="2">sansec=> select + from dic_frequency;</td></tr><tr><td>frequency_id I frequency_name 2 每天上报一次 3 每周上报一次 1 每月上报一次 rows)</td><td>cron_expression 日 日 日 日</td></tr></table></body></html>